{"name": "heygen-video-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "type-check": "tsc --noEmit", "db:generate": "supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID > src/types/database.ts", "db:reset": "supabase db reset", "db:migrate": "supabase migration new"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "js-cookie": "^3.0.5", "lucide-react": "^0.460.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "recharts": "^2.13.3", "sonner": "^1.7.1", "stripe": "^16.12.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "util-deprecate": "^1.0.2", "uuid": "^10.0.0", "whatwg-url": "^14.2.0", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.48.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.4", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/testing-library__jest-dom": "^6.0.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.3.4", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "postcss": "^8.5.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}