'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SettingsPage() {
  const router = useRouter()

  useEffect(() => {
    router.replace('/settings/profile')
  }, [router])

  return (
    <div className="glass-card rounded-xl p-6 shadow-card border border-white/20">
      <div className="animate-pulse">
        <div className="h-6 bg-white/20 rounded w-1/4 mb-4"></div>
        <div className="h-4 bg-white/10 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-white/10 rounded w-1/2"></div>
      </div>
    </div>
  )
}