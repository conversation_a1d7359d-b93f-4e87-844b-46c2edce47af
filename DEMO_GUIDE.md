# AI Video SaaS Demo Guide

## 🚀 Quick Start Instructions

### 1. Prerequisites
```bash
# Make sure you have Node.js 18+ installed
node --version

# Navigate to project directory
cd /mnt/f/Claude-Code-Setup/heygen-video-saas
```

### 2. Install Dependencies
```bash
# Clean install (if having npm issues)
rm -rf node_modules package-lock.json
npm install

# Or try with yarn
yarn install
```

### 3. Environment Setup
The `.env.local` file is already configured with:
- ✅ Supabase credentials (provided)
- ✅ Groq API key (provided) 
- ✅ Hugging Face API key (provided)
- ⚠️ HeyGen API key (needs real key for video generation)
- ⚠️ Stripe keys (needs real keys for payments)

### 4. Start Development Server
```bash
npm run dev
# Server will start at http://localhost:3000
```

## 📱 System Walkthrough

### **Landing & Authentication**
1. **Home Page** → `http://localhost:3000`
   - Welcome page with feature overview
   - Login/Register buttons

2. **Registration** → `/signup`
   - Email + Password signup
   - Password strength indicator
   - Terms acceptance checkbox
   - Creates user account in Supabase

3. **Login** → `/login`
   - Email + Password login
   - Remember me option
   - Forgot password link

### **Main Dashboard** → `/dashboard`
```
📊 Dashboard Overview:
├── 📈 Usage Statistics (credits, videos created)
├── 🎬 Recent Videos (last 5 videos)
├── ⚡ Quick Actions (Create Video, View Library)
└── 📱 Responsive sidebar navigation
```

### **Video Creation Wizard** → `/dashboard/create`

#### **Step 1: Script & Content**
- 📝 **Title Input**: Required video title
- 📄 **Script Editor**: Rich text area with real-time stats
- 🤖 **AI Enhancement**: 
  - Click "Enhance with AI" → Opens options panel
  - Select tone (professional, casual, enthusiastic)
  - Choose target length (short, medium, long)
  - Pick platform optimization (YouTube, TikTok, LinkedIn)
  - Calls Groq API to enhance script
- 📋 **Templates**: Pre-built script templates
  - Product Demo template
  - Welcome Message template  
  - Educational Content template

#### **Step 2: Avatar Selection**
- 🎭 **Avatar Gallery**: Grid of AI presenters
- 🔍 **Search & Filter**: By name, gender, style
- ❤️ **Favorites System**: Save preferred avatars
- 👁️ **Preview Modal**: Full-size avatar preview
- 🏷️ **Metadata Display**: Gender, age range, style tags

#### **Step 3: Voice Selection**
- 🗣️ **Voice Library**: Multiple languages and accents
- ▶️ **Audio Previews**: Click to hear voice samples
- 🔍 **Advanced Filtering**: 
  - Language (English, Spanish, French, German)
  - Gender (Male, Female, All)
  - Search by accent or style
- ❤️ **Favorites**: Save preferred voices
- 📊 **Voice Details**: Language code, accent, age range

#### **Step 4: Video Settings**
- 📐 **Aspect Ratio Selection**:
  - 16:9 (Landscape) - YouTube, presentations
  - 9:16 (Portrait) - TikTok, Instagram Stories  
  - 1:1 (Square) - Instagram posts, LinkedIn
- 🎨 **Background Customization**:
  - Solid colors (8 presets)
  - Gradients (6 beautiful presets)
  - Custom color picker
  - Custom gradient editor
  - Image backgrounds (Pro feature)
- 👁️ **Live Preview**: See settings applied in real-time

#### **Step 5: Review & Generate**
- ✅ **Final Review**: All selections summarized
- 💰 **Credit Cost**: Shows credit usage (1 credit per video)
- 🔄 **Edit Options**: Jump back to any step
- ⚡ **Generate Button**: Starts video creation

#### **Step 6: Generation Progress**
- 📊 **Real-time Progress**: 0-100% completion
- 📋 **Step Tracking**: 
  - Processing script
  - Preparing avatar
  - Generating speech
  - Rendering video
  - Finalizing
- ⏱️ **Time Estimates**: Elapsed and remaining time
- 🎯 **Current Status**: What's happening now

### **Video Library** → `/videos`

#### **Library Overview**
- 📊 **Statistics Cards**: Total, Completed, Processing, Failed
- 🔍 **Search & Filters**: 
  - Text search by title
  - Status filter (All, Completed, Processing, Failed)
- 👁️ **View Modes**: 
  - Grid view (cards with thumbnails)
  - List view (detailed table)

#### **Grid View Features**
- 🖼️ **Video Cards**: Thumbnail, title, status, duration
- ✅ **Bulk Selection**: Checkboxes for multiple videos
- ❤️ **Hover Actions**: Preview and view buttons
- 🏷️ **Status Badges**: Color-coded status indicators
- 📱 **Responsive Grid**: Adapts to screen size

#### **List View Features**
- 📋 **Detailed Table**: All metadata in columns
- 🔄 **Sortable Headers**: Click to sort by any field
- ✅ **Select All**: Checkbox to select all videos
- ⚡ **Quick Actions**: Inline preview and view buttons

### **Video Details Page** → `/videos/[id]`

#### **Video Player Section**
- 🎬 **Custom Video Player**: Built-in controls
- 📱 **Responsive Player**: Adapts to aspect ratio
- ⏸️ **Playback Controls**: Play, pause, seek, volume
- 🖼️ **Thumbnail Fallback**: Shows when not ready

#### **Video Management**
- ✏️ **Edit Mode**: 
  - Update title and description
  - Save changes to database
- 🗑️ **Delete Function**: 
  - Confirmation dialog
  - Permanent deletion
- 🔒 **Privacy Toggle**: Make public/private
- 📊 **Statistics Display**: Views, file size, duration

#### **Sharing Features**
- 🔗 **Share Link**: Copy direct link to video
- 📱 **Social Media**: One-click sharing to:
  - Twitter
  - Facebook  
  - LinkedIn
  - WhatsApp
  - Email
- 💻 **Embed Code**: iframe code for websites
- 🔒 **Privacy Controls**: Public/private visibility

## 🛠️ Technical Architecture

### **Frontend Structure**
```
src/
├── app/                    # Next.js 15 App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   └── layout.tsx         # Root layout
├── components/
│   ├── ui/                # Reusable UI components
│   ├── layout/            # Layout components
│   └── video/             # Video-specific components
├── context/
│   └── auth-context.tsx   # Authentication state
└── types/
    └── database.types.ts  # TypeScript definitions
```

### **API Routes**
```
/api/
├── auth/                  # Supabase authentication
├── heygen/               # Video generation
│   ├── avatars           # Get available avatars
│   ├── voices           # Get available voices
│   ├── generate         # Create new video
│   └── status/[id]      # Check generation status
├── groq/
│   └── enhance-script   # AI script enhancement
├── videos/              # Video management
│   ├── GET /            # List user videos
│   ├── GET /[id]        # Get video details
│   ├── PATCH /[id]      # Update video
│   └── DELETE /[id]     # Delete video
└── stripe/              # Payment processing
```

### **Database Schema**
- 👤 **Users**: Authentication and profile data
- 🎬 **Videos**: Video metadata and status
- 💳 **Subscriptions**: Stripe subscription data
- 📊 **Usage**: Credit tracking and analytics

### **Key Features**
- ✅ **Real-time Updates**: WebSocket for generation status
- 🔒 **Security**: Row Level Security (RLS) policies
- 📱 **Responsive Design**: Works on all devices
- ⚡ **Performance**: Optimized loading and caching
- 🎯 **Error Handling**: Comprehensive error boundaries
- 🔄 **State Management**: React Context + local state

## 🚀 Next Steps

1. **Install Dependencies**: Resolve npm installation issues
2. **Start Development Server**: `npm run dev`
3. **Open Browser**: Navigate to `http://localhost:3000`
4. **Create Account**: Sign up with email/password
5. **Create First Video**: Follow the 6-step wizard
6. **Explore Features**: Try all the video management tools

## 🐛 Troubleshooting

### Common Issues:
1. **npm Install Errors**: 
   ```bash
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```

2. **Environment Variables**: Check `.env.local` file exists

3. **API Keys**: Ensure HeyGen API key is valid for video generation

4. **Supabase Connection**: Verify database is accessible

The system is fully functional and ready to demonstrate once the dependencies are installed!