'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

interface GenerationProgressProps {
  videoId: string | null
  videoTitle: string
  onComplete?: (videoUrl: string) => void
}

interface GenerationStatus {
  video_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  current_step: string
  video_url?: string
  error?: string
  estimated_time_remaining?: number
}

export function GenerationProgress({ videoId, videoTitle, onComplete }: GenerationProgressProps) {
  const [status, setStatus] = useState<GenerationStatus | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [isPolling, setIsPolling] = useState(true)

  useEffect(() => {
    if (!videoId) return

    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/heygen/status/${videoId}`)
        const result = await response.json()

        if (result.success) {
          setStatus(result.data)
          
          if (result.data.status === 'completed') {
            setIsPolling(false)
            if (onComplete && result.data.video_url) {
              onComplete(result.data.video_url)
            }
          } else if (result.data.status === 'failed') {
            setIsPolling(false)
          }
        }
      } catch (error) {
        console.error('Failed to fetch generation status:', error)
      }
    }

    // Initial poll
    pollStatus()

    // Set up polling interval
    const interval = setInterval(pollStatus, 3000) // Poll every 3 seconds

    return () => clearInterval(interval)
  }, [videoId, onComplete])

  useEffect(() => {
    if (!isPolling) return

    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [isPolling])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500'
      case 'processing': return 'bg-blue-500'
      case 'completed': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Preparing...'
      case 'processing': return 'Generating...'
      case 'completed': return 'Complete!'
      case 'failed': return 'Failed'
      default: return 'Unknown'
    }
  }

  if (!videoId) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Generation Failed</h3>
          <p className="text-gray-600">Unable to start video generation. Please try again.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Generating Your Video</h3>
        <p className="text-sm text-gray-600 mb-6">
          Your AI video is being created. This usually takes 2-5 minutes depending on length and complexity.
        </p>
      </div>

      {/* Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">{videoTitle}</CardTitle>
            <Badge variant="outline" className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(status?.status || 'pending')}`}></div>
              <span>{getStatusText(status?.status || 'pending')}</span>
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Progress</span>
              <span className="font-medium">{status?.progress || 0}%</span>
            </div>
            <Progress value={status?.progress || 0} className="h-2" />
          </div>

          {/* Current Step */}
          {status?.current_step && (
            <div className="space-y-2">
              <div className="text-sm text-gray-600">Current Step</div>
              <div className="text-sm font-medium">{status.current_step}</div>
            </div>
          )}

          {/* Time Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Elapsed Time</div>
              <div className="font-medium">{formatTime(elapsedTime)}</div>
            </div>
            {status?.estimated_time_remaining && (
              <div>
                <div className="text-gray-600">Est. Remaining</div>
                <div className="font-medium">{formatTime(status.estimated_time_remaining)}</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Generation Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Generation Process</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { step: 'Processing script', description: 'Analyzing and optimizing your script' },
              { step: 'Preparing avatar', description: 'Setting up AI presenter' },
              { step: 'Generating speech', description: 'Creating voice audio' },
              { step: 'Rendering video', description: 'Combining all elements' },
              { step: 'Finalizing', description: 'Preparing final video' }
            ].map((item, index) => {
              const isActive = status?.current_step === item.step
              const isCompleted = (status?.progress || 0) > (index + 1) * 20
              
              return (
                <div key={index} className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isCompleted
                        ? 'bg-green-500 text-white'
                        : isActive
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {isCompleted ? (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : isActive ? (
                      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${isActive ? 'text-blue-600' : 'text-gray-900'}`}>
                      {item.step}
                    </div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {status?.status === 'failed' && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-red-900">Generation Failed</h4>
                <p className="text-sm text-red-700">
                  {status.error || 'Something went wrong during video generation. Please try again.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Success Display */}
      {status?.status === 'completed' && status.video_url && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-green-900">Video Generated Successfully!</h4>
                  <p className="text-sm text-green-700">Your AI video is ready to view and share.</p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(status.video_url, '_blank')}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Preview
                </Button>
                <Button
                  size="sm"
                  onClick={() => onComplete?.(status.video_url)}
                >
                  View Details
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tips while waiting */}
      {status?.status === 'processing' && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-blue-900 mb-2">💡 While you wait...</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Your video is being generated with high-quality AI technology</li>
              <li>• The process typically takes 2-5 minutes for most videos</li>
              <li>• You can safely close this tab and return later</li>
              <li>• We'll send you an email notification when it's ready</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}