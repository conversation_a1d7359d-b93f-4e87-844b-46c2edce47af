'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { LoadingSpinner } from '@/components/ui/loading'
import { MicroExpression, VoiceModulation, GestureIntelligence } from '@/types/humanization'

interface MicroExpressionEngineProps {
  script: string
  duration: number // video duration in seconds
  onExpressionsGenerated: (expressions: MicroExpression[], voiceModulations: VoiceModulation[], gestures: GestureIntelligence[]) => void
  existingExpressions?: MicroExpression[]
  existingVoiceModulations?: VoiceModulation[]
  existingGestures?: GestureIntelligence[]
}

interface ExpressionSettings {
  frequency: number // expressions per minute
  intensity: number // 0.1 to 1.0
  naturalness: number // 0.1 to 1.0
  contextAwareness: boolean
  includeBreathing: boolean
  includeHesitations: boolean
  enableGestures: boolean
}

const EXPRESSION_TYPES = [
  { type: 'blink' as const, label: 'Natural Blinking', frequency: 15, contexts: ['natural'] },
  { type: 'eye_movement' as const, label: 'Eye Movement', frequency: 8, contexts: ['engagement', 'emphasis'] },
  { type: 'subtle_smile' as const, label: 'Subtle Smile', frequency: 3, contexts: ['engagement', 'emphasis'] },
  { type: 'eyebrow_raise' as const, label: 'Eyebrow Raise', frequency: 2, contexts: ['emphasis', 'transition'] },
  { type: 'head_tilt' as const, label: 'Head Tilt', frequency: 1.5, contexts: ['engagement', 'emphasis'] },
  { type: 'micro_nod' as const, label: 'Micro Nod', frequency: 4, contexts: ['emphasis', 'transition'] }
]

const VOICE_MODULATION_TYPES = [
  { type: 'emotion' as const, label: 'Emotional Variation', description: 'Adjust voice based on content emotion' },
  { type: 'emphasis' as const, label: 'Emphasis Modulation', description: 'Highlight important points' },
  { type: 'hesitation' as const, label: 'Natural Hesitations', description: 'Add human-like speech patterns' },
  { type: 'breathing' as const, label: 'Breathing Sounds', description: 'Natural breathing between phrases' },
  { type: 'pace_change' as const, label: 'Pace Variation', description: 'Vary speaking speed naturally' }
]

const GESTURE_TYPES = [
  { type: 'hand_movement' as const, label: 'Hand Gestures', description: 'Natural hand movements while speaking' },
  { type: 'head_gesture' as const, label: 'Head Movements', description: 'Nodding and head positioning' },
  { type: 'body_lean' as const, label: 'Body Language', description: 'Subtle body positioning changes' },
  { type: 'finger_point' as const, label: 'Pointing Gestures', description: 'Directional hand gestures' },
  { type: 'open_palm' as const, label: 'Open Gestures', description: 'Welcoming, open hand positions' },
  { type: 'thinking_pose' as const, label: 'Thinking Poses', description: 'Contemplative body language' }
]

export function MicroExpressionEngine({
  script,
  duration,
  onExpressionsGenerated,
  existingExpressions = [],
  existingVoiceModulations = [],
  existingGestures = []
}: MicroExpressionEngineProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [settings, setSettings] = useState<ExpressionSettings>({
    frequency: 8, // expressions per minute
    intensity: 0.7,
    naturalness: 0.8,
    contextAwareness: true,
    includeBreathing: true,
    includeHesitations: true,
    enableGestures: true
  })
  
  const [generatedExpressions, setGeneratedExpressions] = useState<MicroExpression[]>(existingExpressions)
  const [generatedVoiceModulations, setGeneratedVoiceModulations] = useState<VoiceModulation[]>(existingVoiceModulations)
  const [generatedGestures, setGeneratedGestures] = useState<GestureIntelligence[]>(existingGestures)
  const [previewMode, setPreviewMode] = useState(false)

  // Analyze script for emotional context and emphasis points
  const analyzeScriptContext = useCallback((text: string) => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const contexts: { time: number; context: string; intensity: number }[] = []
    
    sentences.forEach((sentence, index) => {
      const timePerSentence = duration / sentences.length
      const timing = index * timePerSentence
      
      // Detect emotional context
      if (/[!]/.test(sentence) || /amazing|great|awesome|fantastic/i.test(sentence)) {
        contexts.push({ time: timing, context: 'emphasis', intensity: 0.8 })
      } else if (/[?]/.test(sentence)) {
        contexts.push({ time: timing, context: 'engagement', intensity: 0.6 })
      } else if (/however|but|although|unfortunately/i.test(sentence)) {
        contexts.push({ time: timing, context: 'transition', intensity: 0.5 })
      } else if (/\[PAUSE\]/.test(sentence)) {
        contexts.push({ time: timing, context: 'pause', intensity: 0.4 })
      } else {
        contexts.push({ time: timing, context: 'natural', intensity: 0.3 })
      }
    })
    
    return contexts
  }, [duration])

  // Generate natural micro-expressions based on script analysis
  const generateMicroExpressions = useCallback(async () => {
    setIsGenerating(true)
    
    try {
      const scriptContexts = analyzeScriptContext(script)
      const expressions: MicroExpression[] = []
      const voiceModulations: VoiceModulation[] = []
      const gestures: GestureIntelligence[] = []
      
      // Generate expressions based on frequency and context
      const totalExpressions = Math.floor((duration / 60) * settings.frequency)
      
      for (let i = 0; i < totalExpressions; i++) {
        // Find appropriate timing based on script context
        const randomContext = scriptContexts[Math.floor(Math.random() * scriptContexts.length)]
        const baseTime = randomContext.time
        const jitter = (Math.random() - 0.5) * 2 // Add some randomness
        const timing = Math.max(0, Math.min(duration - 1, baseTime + jitter))
        
        // Select expression type based on context
        const contextualExpressions = EXPRESSION_TYPES.filter(exp => 
          exp.contexts.includes(randomContext.context as any) || 
          exp.contexts.includes('natural')
        )
        
        const selectedExpression = contextualExpressions[
          Math.floor(Math.random() * contextualExpressions.length)
        ]
        
        const expression: MicroExpression = {
          id: `expr_${Date.now()}_${i}`,
          type: selectedExpression.type,
          timing: timing,
          duration: selectedExpression.type === 'blink' ? 0.15 : 
                   selectedExpression.type === 'eye_movement' ? 0.8 : 1.2,
          intensity: Math.max(0.1, Math.min(1.0, 
            settings.intensity * randomContext.intensity * (0.8 + Math.random() * 0.4)
          )),
          context: randomContext.context as any
        }
        
        expressions.push(expression)
      }
      
      // Generate voice modulations
      if (settings.includeBreathing || settings.includeHesitations) {
        scriptContexts.forEach(context => {
          if (context.context === 'pause' || context.context === 'transition') {
            // Add breathing at natural pause points
            if (settings.includeBreathing && Math.random() < 0.6) {
              voiceModulations.push({
                id: `voice_${Date.now()}_${context.time}`,
                startTime: context.time,
                endTime: context.time + 0.5,
                modulationType: 'breathing',
                intensity: 0.3 + Math.random() * 0.3,
                parameters: {
                  breathing_pattern: Math.random() < 0.7 ? 'natural' : 'slight_pause'
                }
              })
            }
            
            // Add hesitations occasionally
            if (settings.includeHesitations && Math.random() < 0.3) {
              voiceModulations.push({
                id: `voice_hes_${Date.now()}_${context.time}`,
                startTime: context.time,
                endTime: context.time + 0.3,
                modulationType: 'hesitation',
                intensity: 0.4,
                parameters: {
                  hesitation_type: ['um', 'uh', 'pause'][Math.floor(Math.random() * 3)] as any
                }
              })
            }
          } else if (context.context === 'emphasis') {
            // Add emotional modulation for emphasis
            voiceModulations.push({
              id: `voice_emph_${Date.now()}_${context.time}`,
              startTime: context.time,
              endTime: context.time + 2,
              modulationType: 'emotion',
              intensity: context.intensity,
              parameters: {
                emotion_type: 'confidence',
                pitch_variation: 0.1 + context.intensity * 0.2,
                volume_adjustment: 0.05 + context.intensity * 0.1
              }
            })
          }
        })
      }
      
      // Generate gestures
      if (settings.enableGestures) {
        const gestureCount = Math.floor((duration / 60) * 3) // 3 gestures per minute
        
        for (let i = 0; i < gestureCount; i++) {
          const randomContext = scriptContexts[Math.floor(Math.random() * scriptContexts.length)]
          const gestureType = GESTURE_TYPES[Math.floor(Math.random() * GESTURE_TYPES.length)]
          
          gestures.push({
            id: `gesture_${Date.now()}_${i}`,
            timing: randomContext.time,
            duration: 1.5 + Math.random() * 1.5,
            gestureType: gestureType.type,
            context: script.substring(
              Math.max(0, Math.floor(randomContext.time * 10)), 
              Math.min(script.length, Math.floor((randomContext.time + 3) * 10))
            ),
            intensity: randomContext.intensity > 0.6 ? 'pronounced' : 
                      randomContext.intensity > 0.4 ? 'moderate' : 'subtle',
            coordination: {
              matches_voice_emphasis: randomContext.context === 'emphasis',
              relates_to_content: true,
              natural_flow: true
            }
          })
        }
      }
      
      // Sort by timing
      expressions.sort((a, b) => a.timing - b.timing)
      voiceModulations.sort((a, b) => a.startTime - b.startTime)
      gestures.sort((a, b) => a.timing - b.timing)
      
      setGeneratedExpressions(expressions)
      setGeneratedVoiceModulations(voiceModulations)
      setGeneratedGestures(gestures)
      
      // Call the parent callback
      onExpressionsGenerated(expressions, voiceModulations, gestures)
      
    } catch (error) {
      console.error('Failed to generate micro-expressions:', error)
      alert('Failed to generate micro-expressions. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }, [script, duration, settings, analyzeScriptContext, onExpressionsGenerated])

  const clearAll = () => {
    setGeneratedExpressions([])
    setGeneratedVoiceModulations([])
    setGeneratedGestures([])
    onExpressionsGenerated([], [], [])
  }

  const expressionStats = {
    totalExpressions: generatedExpressions.length,
    avgIntensity: generatedExpressions.reduce((sum, exp) => sum + exp.intensity, 0) / (generatedExpressions.length || 1),
    voiceModulations: generatedVoiceModulations.length,
    gestures: generatedGestures.length,
    expressionsPerMinute: (generatedExpressions.length / duration) * 60
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Micro-Expression Engine</h3>
        <p className="text-sm text-gray-600">
          Generate natural micro-expressions, voice modulations, and gestures to make your AI avatar appear completely human
        </p>
      </div>

      {/* Settings Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Humanization Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Expression Frequency: {settings.frequency}/min
              </label>
              <Slider
                value={[settings.frequency]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, frequency: value }))}
                min={2}
                max={20}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Intensity: {Math.round(settings.intensity * 100)}%
              </label>
              <Slider
                value={[settings.intensity]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, intensity: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Naturalness: {Math.round(settings.naturalness * 100)}%
              </label>
              <Slider
                value={[settings.naturalness]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, naturalness: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="mt-2"
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Context Awareness</label>
                <Switch
                  checked={settings.contextAwareness}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, contextAwareness: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Include Breathing</label>
                <Switch
                  checked={settings.includeBreathing}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, includeBreathing: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Natural Hesitations</label>
                <Switch
                  checked={settings.includeHesitations}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, includeHesitations: checked }))}
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Enable Gestures</label>
                <Switch
                  checked={settings.enableGestures}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableGestures: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Controls */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={generateMicroExpressions}
          disabled={isGenerating || !script.trim()}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          {isGenerating ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Generating Expressions...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Generate Human-Like Expressions
            </>
          )}
        </Button>
        
        {(generatedExpressions.length > 0 || generatedVoiceModulations.length > 0 || generatedGestures.length > 0) && (
          <Button variant="outline" onClick={clearAll}>
            Clear All
          </Button>
        )}
        
        <Button
          variant="outline"
          onClick={() => setPreviewMode(!previewMode)}
          disabled={generatedExpressions.length === 0}
        >
          {previewMode ? 'Hide Preview' : 'Preview Timeline'}
        </Button>
      </div>

      {/* Generated Results */}
      {(generatedExpressions.length > 0 || generatedVoiceModulations.length > 0 || generatedGestures.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Generated Humanization Elements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{expressionStats.totalExpressions}</div>
                <div className="text-sm text-gray-600">Micro-Expressions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{expressionStats.voiceModulations}</div>
                <div className="text-sm text-gray-600">Voice Modulations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{expressionStats.gestures}</div>
                <div className="text-sm text-gray-600">Gestures</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{expressionStats.expressionsPerMinute.toFixed(1)}</div>
                <div className="text-sm text-gray-600">Per Minute</div>
              </div>
            </div>

            {previewMode && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium mb-3">Expression Timeline Preview</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {generatedExpressions.slice(0, 10).map((expr) => (
                    <div key={expr.id} className="flex items-center justify-between text-sm">
                      <span className="font-medium">{expr.type.replace('_', ' ')}</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{expr.timing.toFixed(1)}s</Badge>
                        <Badge variant="outline">{Math.round(expr.intensity * 100)}%</Badge>
                        <Badge variant="outline">{expr.context}</Badge>
                      </div>
                    </div>
                  ))}
                  {generatedExpressions.length > 10 && (
                    <div className="text-xs text-gray-500 text-center pt-2">
                      ... and {generatedExpressions.length - 10} more expressions
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quality Indicators */}
      {generatedExpressions.length > 0 && (
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-base text-green-800">Humanization Quality Assessment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <div className="text-lg font-bold text-green-700">
                  {Math.min(95, 60 + (expressionStats.avgIntensity * 35)).toFixed(0)}%
                </div>
                <div className="text-sm text-green-600">Naturalness Score</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-700">
                  {Math.max(5, 95 - (expressionStats.expressionsPerMinute * 2)).toFixed(0)}%
                </div>
                <div className="text-sm text-blue-600">AI Detection Avoidance</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-700">
                  {Math.min(98, 75 + (settings.naturalness * 23)).toFixed(0)}%
                </div>
                <div className="text-sm text-purple-600">Human-Like Rating</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}