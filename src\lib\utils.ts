import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDuration(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export function formatCredits(credits: number): string {
  return credits.toLocaleString()
}

export function formatPrice(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`
}

export function getSubscriptionTierName(tier: string): string {
  const tierNames = {
    free: 'Free',
    starter: 'Starter',
    pro: 'Pro',
    enterprise: 'Enterprise'
  }
  return tierNames[tier as keyof typeof tierNames] || 'Unknown'
}

export function getSubscriptionTierCredits(tier: string): number {
  const tierCredits = {
    free: 5,
    starter: 100,
    pro: 500,
    enterprise: 2000
  }
  return tierCredits[tier as keyof typeof tierCredits] || 0
}

export function getSubscriptionTierPrice(tier: string): number {
  const tierPrices = {
    free: 0,
    starter: 29,
    pro: 99,
    enterprise: 299
  }
  return tierPrices[tier as keyof typeof tierPrices] || 0
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function generateVideoTitle(script: string): string {
  // Extract first meaningful sentence or create title from script
  const firstSentence = script.split(/[.!?]/)[0]
  const words = firstSentence.split(' ').slice(0, 8) // First 8 words
  return words.join(' ') + (words.length === 8 ? '...' : '')
}

export function estimateVideoDuration(script: string): number {
  // Rough estimation: 150 words per minute for speech
  const wordCount = script.split(' ').length
  return Math.ceil((wordCount / 150) * 60) // Return seconds
}

export function getVideoStatusColor(status: string): string {
  const statusColors = {
    pending: 'text-yellow-600 bg-yellow-100',
    processing: 'text-blue-600 bg-blue-100',
    completed: 'text-green-600 bg-green-100',
    failed: 'text-red-600 bg-red-100'
  }
  return statusColors[status as keyof typeof statusColors] || 'text-gray-600 bg-gray-100'
}

export function getAvatarInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

export function downloadFile(url: string, filename: string): void {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}