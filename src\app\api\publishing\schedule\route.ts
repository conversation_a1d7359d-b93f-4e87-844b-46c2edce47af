import { NextRequest, NextResponse } from 'next/server'

interface ScheduleRequest {
  videoId: string
  platforms: {
    platform: string
    scheduledTime: string
    title: string
    description: string
    hashtags: string[]
  }[]
}

export async function POST(request: NextRequest) {
  try {
    const { videoId, platforms }: ScheduleRequest = await request.json()

    if (!videoId || !platforms?.length) {
      return NextResponse.json(
        { success: false, error: 'Video ID and platforms are required' },
        { status: 400 }
      )
    }

    // Validate platforms
    const supportedPlatforms = ['youtube', 'tiktok', 'linkedin', 'twitter', 'facebook']
    const invalidPlatforms = platforms.filter(p => !supportedPlatforms.includes(p.platform))
    
    if (invalidPlatforms.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Unsupported platforms: ${invalidPlatforms.map(p => p.platform).join(', ')}` 
        },
        { status: 400 }
      )
    }

    // Validate scheduled times
    const now = new Date()
    const invalidTimes = platforms.filter(p => {
      const scheduledTime = new Date(p.scheduledTime)
      return scheduledTime <= now
    })

    if (invalidTimes.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Scheduled times must be in the future' },
        { status: 400 }
      )
    }

    // In a real implementation, this would:
    // 1. Store the scheduling data in the database
    // 2. Set up background jobs for each platform
    // 3. Integrate with platform APIs for publishing
    
    // Mock response simulating successful scheduling
    const scheduledJobs = platforms.map(platform => ({
      platform: platform.platform,
      scheduledTime: platform.scheduledTime,
      jobId: `job_${videoId}_${platform.platform}_${Date.now()}`,
      status: 'scheduled',
      estimatedPublishTime: platform.scheduledTime
    }))

    // Simulate optimal posting times recommendations
    const recommendations = {
      youtube: {
        bestTimes: ['2:00 PM - 4:00 PM', '6:00 PM - 9:00 PM'],
        bestDays: ['Tuesday', 'Wednesday', 'Thursday'],
        timeZone: 'Local audience timezone'
      },
      tiktok: {
        bestTimes: ['6:00 AM - 10:00 AM', '7:00 PM - 9:00 PM'],
        bestDays: ['Tuesday', 'Thursday', 'Friday'],
        timeZone: 'Local audience timezone'
      },
      linkedin: {
        bestTimes: ['8:00 AM - 10:00 AM', '12:00 PM - 2:00 PM'],
        bestDays: ['Tuesday', 'Wednesday', 'Thursday'],
        timeZone: 'Business hours in target market'
      },
      twitter: {
        bestTimes: ['9:00 AM - 10:00 AM', '8:00 PM - 9:00 PM'],
        bestDays: ['Wednesday', 'Friday'],
        timeZone: 'Local audience timezone'
      },
      facebook: {
        bestTimes: ['1:00 PM - 3:00 PM', '8:00 PM - 9:00 PM'],
        bestDays: ['Wednesday', 'Thursday', 'Friday'],
        timeZone: 'Local audience timezone'
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        videoId,
        scheduledJobs,
        recommendations,
        summary: {
          totalPlatforms: platforms.length,
          earliestPublish: platforms.reduce((earliest, p) => 
            p.scheduledTime < earliest ? p.scheduledTime : earliest, 
            platforms[0].scheduledTime
          ),
          latestPublish: platforms.reduce((latest, p) => 
            p.scheduledTime > latest ? p.scheduledTime : latest, 
            platforms[0].scheduledTime
          )
        }
      }
    })

  } catch (error) {
    console.error('Publishing schedule error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const videoId = searchParams.get('videoId')

    if (!videoId) {
      return NextResponse.json(
        { success: false, error: 'Video ID is required' },
        { status: 400 }
      )
    }

    // In a real implementation, this would fetch from database
    // Mock response for scheduled publications
    const mockScheduledPosts = [
      {
        platform: 'youtube',
        scheduledTime: '2024-01-15T14:00:00Z',
        status: 'scheduled',
        jobId: `job_${videoId}_youtube_123`
      },
      {
        platform: 'tiktok',
        scheduledTime: '2024-01-15T19:00:00Z',
        status: 'scheduled',
        jobId: `job_${videoId}_tiktok_124`
      }
    ]

    return NextResponse.json({
      success: true,
      data: {
        videoId,
        scheduledPosts: mockScheduledPosts,
        totalScheduled: mockScheduledPosts.length
      }
    })

  } catch (error) {
    console.error('Fetch schedule error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}