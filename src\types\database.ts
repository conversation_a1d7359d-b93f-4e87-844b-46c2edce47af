export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'starter' | 'pro' | 'enterprise'
          credits_remaining: number
          credits_total: number
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'starter' | 'pro' | 'enterprise'
          credits_remaining?: number
          credits_total?: number
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'starter' | 'pro' | 'enterprise'
          credits_remaining?: number
          credits_total?: number
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      videos: {
        Row: {
          id: string
          user_id: string
          title: string
          script: string
          heygen_video_id: string | null
          video_url: string | null
          thumbnail_url: string | null
          status: 'pending' | 'processing' | 'completed' | 'failed'
          avatar_id: string
          voice_id: string
          duration: number | null
          credits_used: number
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          script: string
          heygen_video_id?: string | null
          video_url?: string | null
          thumbnail_url?: string | null
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          avatar_id: string
          voice_id: string
          duration?: number | null
          credits_used?: number
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          script?: string
          heygen_video_id?: string | null
          video_url?: string | null
          thumbnail_url?: string | null
          status?: 'pending' | 'processing' | 'completed' | 'failed'
          avatar_id?: string
          voice_id?: string
          duration?: number | null
          credits_used?: number
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "videos_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          tier: 'free' | 'starter' | 'pro' | 'enterprise'
          status: 'active' | 'canceled' | 'past_due' | 'incomplete'
          current_period_start: string
          current_period_end: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_subscription_id: string
          stripe_customer_id: string
          tier: 'free' | 'starter' | 'pro' | 'enterprise'
          status?: 'active' | 'canceled' | 'past_due' | 'incomplete'
          current_period_start: string
          current_period_end: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_subscription_id?: string
          stripe_customer_id?: string
          tier?: 'free' | 'starter' | 'pro' | 'enterprise'
          status?: 'active' | 'canceled' | 'past_due' | 'incomplete'
          current_period_start?: string
          current_period_end?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      payment_history: {
        Row: {
          id: string
          user_id: string
          stripe_payment_intent_id: string
          amount: number
          currency: string
          status: string
          description: string | null
          credits_purchased: number | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_payment_intent_id: string
          amount: number
          currency?: string
          status: string
          description?: string | null
          credits_purchased?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_payment_intent_id?: string
          amount?: number
          currency?: string
          status?: string
          description?: string | null
          credits_purchased?: number | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_history_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      video_templates: {
        Row: {
          id: string
          name: string
          description: string | null
          script_template: string
          avatar_id: string
          voice_id: string
          category: string | null
          tags: string[] | null
          is_public: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          script_template: string
          avatar_id: string
          voice_id: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          script_template?: string
          avatar_id?: string
          voice_id?: string
          category?: string | null
          tags?: string[] | null
          is_public?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_templates_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_settings: {
        Row: {
          id: string
          user_id: string
          heygen_api_key: string | null
          default_avatar_id: string | null
          default_voice_id: string | null
          preferred_aspect_ratio: string
          email_notifications: boolean
          webhook_url: string | null
          last_api_test: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          heygen_api_key?: string | null
          default_avatar_id?: string | null
          default_voice_id?: string | null
          preferred_aspect_ratio?: string
          email_notifications?: boolean
          webhook_url?: string | null
          last_api_test?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          heygen_api_key?: string | null
          default_avatar_id?: string | null
          default_voice_id?: string | null
          preferred_aspect_ratio?: string
          email_notifications?: boolean
          webhook_url?: string | null
          last_api_test?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_settings_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subscription_tier_credits: {
        Row: {
          tier: 'free' | 'starter' | 'pro' | 'enterprise'
          monthly_credits: number
          price_cents: number
          stripe_price_id: string | null
        }
        Insert: {
          tier: 'free' | 'starter' | 'pro' | 'enterprise'
          monthly_credits: number
          price_cents: number
          stripe_price_id?: string | null
        }
        Update: {
          tier?: 'free' | 'starter' | 'pro' | 'enterprise'
          monthly_credits?: number
          price_cents?: number
          stripe_price_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      subscription_tier: 'free' | 'starter' | 'pro' | 'enterprise'
      video_status: 'pending' | 'processing' | 'completed' | 'failed'
      subscription_status: 'active' | 'canceled' | 'past_due' | 'incomplete'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}