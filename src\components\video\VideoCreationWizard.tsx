'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { ScriptEditor } from './ScriptEditor'
import { AvatarSelector } from './AvatarSelector'
import { VoiceSelector } from './VoiceSelector'
import { VideoSettings } from './VideoSettings'
import { GenerationProgress } from './GenerationProgress'
import { useAuth } from '@/context/auth-context'

interface VideoCreationData {
  title: string
  script: string
  avatar_id: string
  voice_id: string
  aspect_ratio: '16:9' | '9:16' | '1:1'
  background?: {
    type: 'color' | 'image'
    value: string
  }
}

const steps = [
  { id: 1, title: 'Script', description: 'Write or enhance your script' },
  { id: 2, title: 'Avatar', description: 'Choose your AI presenter' },
  { id: 3, title: 'Voice', description: 'Select the perfect voice' },
  { id: 4, title: 'Settings', description: 'Configure video settings' },
  { id: 5, title: 'Review', description: 'Review and generate' },
  { id: 6, title: 'Generate', description: 'Creating your video' },
]

export function VideoCreationWizard() {
  const router = useRouter()
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [videoData, setVideoData] = useState<VideoCreationData>({
    title: '',
    script: '',
    avatar_id: '',
    voice_id: '',
    aspect_ratio: '16:9',
    background: { type: 'color', value: '#f0f0f0' }
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationId, setGenerationId] = useState<string | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!videoData.title.trim()) newErrors.title = 'Title is required'
        if (!videoData.script.trim()) newErrors.script = 'Script is required'
        if (videoData.script.length < 10) newErrors.script = 'Script must be at least 10 characters'
        break
      case 2:
        if (!videoData.avatar_id) newErrors.avatar = 'Please select an avatar'
        break
      case 3:
        if (!videoData.voice_id) newErrors.voice = 'Please select a voice'
        break
      case 4:
        if (!videoData.aspect_ratio) newErrors.settings = 'Please select aspect ratio'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleGenerate = async () => {
    if (!validateStep(4)) return

    // Check credits
    if (!user || user.credits_remaining < 1) {
      alert('Insufficient credits. Please upgrade your plan.')
      router.push('/billing')
      return
    }

    setIsGenerating(true)
    setCurrentStep(6)

    try {
      const response = await fetch('/api/heygen/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(videoData),
      })

      const result = await response.json()

      if (result.success) {
        setGenerationId(result.data.video_id)
        // You could also redirect to video details page
        // router.push(`/videos/${result.data.video_id}`)
      } else {
        throw new Error(result.error || 'Failed to generate video')
      }
    } catch (error) {
      console.error('Generation failed:', error)
      alert('Failed to generate video. Please try again.')
      setCurrentStep(5) // Go back to review step
    } finally {
      setIsGenerating(false)
    }
  }

  const updateVideoData = (updates: Partial<VideoCreationData>) => {
    setVideoData(prev => ({ ...prev, ...updates }))
  }

  const progress = ((currentStep - 1) / (steps.length - 1)) * 100

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ScriptEditor
            title={videoData.title}
            script={videoData.script}
            onUpdate={updateVideoData}
            errors={errors}
          />
        )
      case 2:
        return (
          <AvatarSelector
            selectedAvatarId={videoData.avatar_id}
            onSelect={(avatar_id) => updateVideoData({ avatar_id })}
            error={errors.avatar}
          />
        )
      case 3:
        return (
          <VoiceSelector
            selectedVoiceId={videoData.voice_id}
            onSelect={(voice_id) => updateVideoData({ voice_id })}
            error={errors.voice}
          />
        )
      case 4:
        return (
          <VideoSettings
            aspectRatio={videoData.aspect_ratio}
            background={videoData.background}
            onUpdate={updateVideoData}
            error={errors.settings}
          />
        )
      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Review Your Video</h3>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-gray-700">Title</label>
                  <p className="mt-1 text-sm text-gray-900">{videoData.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Aspect Ratio</label>
                  <p className="mt-1 text-sm text-gray-900">{videoData.aspect_ratio}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Script</label>
                <div className="mt-1 p-3 border rounded-md bg-gray-50 max-h-32 overflow-y-auto">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{videoData.script}</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <p className="font-medium text-blue-900">Ready to generate?</p>
                  <p className="text-sm text-blue-700">This will use 1 credit from your account</p>
                </div>
                <Badge variant="info">
                  {user?.credits_remaining || 0} credits remaining
                </Badge>
              </div>
            </div>
          </div>
        )
      case 6:
        return (
          <GenerationProgress
            videoId={generationId}
            videoTitle={videoData.title}
            onComplete={(videoUrl) => {
              router.push(`/videos/${generationId}`)
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Step {currentStep} of {steps.length}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {steps[currentStep - 1]?.description}
              </p>
            </div>
            <Badge variant="outline">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="mt-4" />
        </CardHeader>
      </Card>

      {/* Step Navigation */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep > step.id
                    ? 'bg-green-500 text-white'
                    : currentStep === step.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {currentStep > step.id ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  step.id
                )}
              </div>
              <span className="ml-2 text-sm font-medium text-gray-700 hidden sm:block">
                {step.title}
              </span>
              {index < steps.length - 1 && (
                <div className="w-8 h-px bg-gray-300 ml-4" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      {currentStep < 6 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </Button>

          {currentStep === 5 ? (
            <Button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                  </svg>
                  Generate Video
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleNext}>
              Next
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          )}
        </div>
      )}
    </div>
  )
}