import { NextRequest, NextResponse } from 'next/server'
import { runAllConnectionTests } from '@/lib/test-connection'

export async function GET(request: NextRequest) {
  try {
    // Run all connection tests
    const testResults = await runAllConnectionTests()
    
    return NextResponse.json({
      success: testResults.success,
      message: testResults.success 
        ? 'All systems operational' 
        : 'Some systems have issues',
      data: testResults,
      timestamp: new Date().toISOString()
    }, {
      status: testResults.success ? 200 : 500
    })
  } catch (error) {
    console.error('Connection test API error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to run connection tests',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, {
      status: 500
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { testType } = body
    
    let result
    
    switch (testType) {
      case 'connection':
        const { testSupabaseConnection } = await import('@/lib/test-connection')
        result = await testSupabaseConnection()
        break
      case 'auth':
        const { testSupabaseAuth } = await import('@/lib/test-connection')
        result = await testSupabaseAuth()
        break
      case 'tables':
        const { testDatabaseTables } = await import('@/lib/test-connection')
        result = await testDatabaseTables()
        break
      default:
        const { runAllConnectionTests } = await import('@/lib/test-connection')
        result = await runAllConnectionTests()
    }
    
    return NextResponse.json({
      success: result.success,
      data: result,
      timestamp: new Date().toISOString()
    }, {
      status: result.success ? 200 : 500
    })
  } catch (error) {
    console.error('Connection test API error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to run specific connection test',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, {
      status: 500
    })
  }
}