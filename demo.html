<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Video SaaS - System Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#8b5cf6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg"></div>
                    <h1 class="text-xl font-bold">AI Video SaaS Demo</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">5 credits remaining</div>
                    <div class="w-8 h-8 bg-gray-300 rounded-full"></div>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Navigation Tabs -->
        <div class="mb-8">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="showTab('dashboard')" id="dashboard-tab" class="tab-button active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                        Dashboard
                    </button>
                    <button onclick="showTab('create')" id="create-tab" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                        Create Video
                    </button>
                    <button onclick="showTab('videos')" id="videos-tab" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                        Video Library
                    </button>
                </nav>
            </div>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard-content" class="tab-content">
            <div class="grid gap-6 mb-8 md:grid-cols-4">
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="text-2xl font-bold text-blue-600">12</div>
                    <div class="text-sm text-gray-600">Total Videos</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="text-2xl font-bold text-green-600">8</div>
                    <div class="text-sm text-gray-600">Completed</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="text-2xl font-bold text-yellow-600">2</div>
                    <div class="text-sm text-gray-600">Processing</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="text-2xl font-bold text-purple-600">156</div>
                    <div class="text-sm text-gray-600">Total Views</div>
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-2">
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4">Recent Videos</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="w-16 h-10 bg-gray-300 rounded"></div>
                            <div class="flex-1">
                                <div class="font-medium">Welcome to Our Product</div>
                                <div class="text-sm text-gray-500">Created 2 hours ago</div>
                            </div>
                            <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">Completed</span>
                        </div>
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="w-16 h-10 bg-gray-300 rounded"></div>
                            <div class="flex-1">
                                <div class="font-medium">Product Demo Video</div>
                                <div class="text-sm text-gray-500">Created 1 day ago</div>
                            </div>
                            <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">Processing</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="showTab('create')" class="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create New Video
                        </button>
                        <button onclick="showTab('videos')" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            View Video Library
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Video Tab -->
        <div id="create-content" class="tab-content hidden">
            <!-- Progress Header -->
            <div class="bg-white p-6 rounded-lg shadow-sm border mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h2 class="text-xl font-semibold">Step <span id="current-step">1</span> of 6</h2>
                        <p class="text-sm text-gray-600 mt-1">Write or enhance your script</p>
                    </div>
                    <span class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full">17% Complete</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 17%"></div>
                </div>
            </div>

            <!-- Step Navigation -->
            <div class="flex justify-center mb-6">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-medium">1</div>
                        <span class="ml-2 text-sm font-medium text-gray-700">Script</span>
                    </div>
                    <div class="w-8 h-px bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">2</div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Avatar</span>
                    </div>
                    <div class="w-8 h-px bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">3</div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Voice</span>
                    </div>
                    <div class="w-8 h-px bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">4</div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Settings</span>
                    </div>
                    <div class="w-8 h-px bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">5</div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Review</span>
                    </div>
                    <div class="w-8 h-px bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center text-sm font-medium">6</div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Generate</span>
                    </div>
                </div>
            </div>

            <!-- Step Content -->
            <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div id="step-1" class="step-content">
                    <h3 class="text-lg font-semibold mb-4">Script & Content</h3>
                    <p class="text-sm text-gray-600 mb-6">Write your script or use our AI-powered enhancement to create engaging content</p>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-700 mb-2 block">Video Title *</label>
                            <input type="text" placeholder="Enter a compelling title for your video" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="Welcome to Our Amazing Product">
                        </div>
                        
                        <div class="flex gap-3 mb-4">
                            <button class="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Use Template
                            </button>
                            <button class="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Enhance with AI
                            </button>
                        </div>
                        
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label class="text-sm font-medium text-gray-700">Script Content *</label>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span>42 words</span>
                                    <span>234 characters</span>
                                    <span>~17s duration</span>
                                </div>
                            </div>
                            <textarea rows="8" placeholder="Enter your script here..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">Welcome to our amazing platform! Today I want to show you how our revolutionary product can transform your business. With our cutting-edge AI technology, you can create professional videos in minutes, not hours. Let me walk you through the key features that make us the industry leader.</textarea>
                        </div>
                    </div>
                </div>
                
                <div id="step-2" class="step-content hidden">
                    <h3 class="text-lg font-semibold mb-4">Choose Your AI Avatar</h3>
                    <p class="text-sm text-gray-600 mb-6">Select an AI presenter that best represents your brand and message</p>
                    
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
                        <div class="relative group cursor-pointer rounded-lg border-2 border-blue-500 ring-2 ring-blue-200 shadow-lg">
                            <div class="relative overflow-hidden rounded-lg">
                                <div class="w-full aspect-[3/4] bg-gradient-to-b from-blue-100 to-blue-200 flex items-center justify-center">
                                    <span class="text-4xl">👩‍💼</span>
                                </div>
                                <div class="absolute top-2 left-2">
                                    <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium text-gray-900">Sarah</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">female</span>
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">professional</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="relative group cursor-pointer rounded-lg border-2 border-gray-200 hover:border-gray-300">
                            <div class="relative overflow-hidden rounded-lg">
                                <div class="w-full aspect-[3/4] bg-gradient-to-b from-green-100 to-green-200 flex items-center justify-center">
                                    <span class="text-4xl">👨‍💼</span>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium text-gray-900">David</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">male</span>
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">business</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="relative group cursor-pointer rounded-lg border-2 border-gray-200 hover:border-gray-300">
                            <div class="relative overflow-hidden rounded-lg">
                                <div class="w-full aspect-[3/4] bg-gradient-to-b from-purple-100 to-purple-200 flex items-center justify-center">
                                    <span class="text-4xl">👩‍🎓</span>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium text-gray-900">Emma</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">female</span>
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">casual</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="relative group cursor-pointer rounded-lg border-2 border-gray-200 hover:border-gray-300">
                            <div class="relative overflow-hidden rounded-lg">
                                <div class="w-full aspect-[3/4] bg-gradient-to-b from-orange-100 to-orange-200 flex items-center justify-center">
                                    <span class="text-4xl">👨‍💻</span>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium text-gray-900">Michael</h4>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">male</span>
                                    <span class="px-2 py-1 text-xs border border-gray-300 rounded">executive</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-6">
                <button onclick="previousStep()" id="prev-btn" class="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50" disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Previous
                </button>
                <button onclick="nextStep()" id="next-btn" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Next
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Video Library Tab -->
        <div id="videos-content" class="tab-content hidden">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold">My Videos</h2>
                    <p class="text-gray-600">Manage and organize your AI-generated videos</p>
                </div>
                <button onclick="showTab('create')" class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Video
                </button>
            </div>

            <!-- Filters -->
            <div class="bg-white p-4 rounded-lg shadow-sm border mb-6">
                <div class="flex flex-col lg:flex-row gap-4 items-center">
                    <div class="flex-1">
                        <input type="text" placeholder="Search videos..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md">All</button>
                        <button class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Completed</button>
                        <button class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Processing</button>
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="p-2 bg-blue-600 text-white rounded-md">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                        </button>
                        <button class="p-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Video Grid -->
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">completed</span>
                        </div>
                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                            0:45
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Welcome to Our Product</h3>
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                            <span>Jan 15, 2024</span>
                            <span>16:9</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-2">Avatar: Sarah</div>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>156 views</span>
                            <span>2.3 MB</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                            <div class="text-center">
                                <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                <p class="text-white text-sm">Processing...</p>
                            </div>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">processing</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Product Demo - New Features</h3>
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                            <span>Jan 16, 2024</span>
                            <span>9:16</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-2">Avatar: David</div>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>0 views</span>
                            <span>-</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">completed</span>
                        </div>
                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                            2:00
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Monthly Update Video</h3>
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                            <span>Jan 14, 2024</span>
                            <span>1:1</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-2">Avatar: Emma</div>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>89 views</span>
                            <span>4.1 MB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(tab => {
                tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }
        
        function nextStep() {
            if (currentStep < 6) {
                currentStep++;
                updateStep();
            }
        }
        
        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStep();
            }
        }
        
        function updateStep() {
            // Update step number
            document.getElementById('current-step').textContent = currentStep;
            
            // Update progress bar
            const progress = ((currentStep - 1) / 5) * 100;
            document.querySelector('.bg-blue-500').style.width = progress + '%';
            
            // Update step description
            const descriptions = [
                'Write or enhance your script',
                'Choose your AI presenter',
                'Select the perfect voice',
                'Configure video settings',
                'Review and generate',
                'Creating your video'
            ];
            document.querySelector('.text-gray-600').textContent = descriptions[currentStep - 1];
            
            // Show/hide step content
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            if (currentStep <= 2) {
                document.getElementById('step-' + currentStep).classList.remove('hidden');
            }
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentStep === 1;
            
            if (currentStep === 6) {
                document.getElementById('next-btn').innerHTML = `
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Generating...
                `;
                document.getElementById('next-btn').disabled = true;
            }
        }
        
        // Initialize the demo
        showTab('dashboard');
    </script>
</body>
</html>