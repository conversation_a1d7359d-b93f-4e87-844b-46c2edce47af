'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application Error:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-hero flex flex-col items-center justify-center px-6 py-24">
      <div className="max-w-md mx-auto text-center">
        {/* Logo */}
        <Link href="/" className="inline-block mb-8 hover:opacity-80 transition-opacity">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <div className="w-5 h-5 bg-gradient-primary rounded-sm"></div>
            </div>
            <span className="text-2xl font-bold text-white">HeyGen Video</span>
          </div>
        </Link>

        {/* Error Content */}
        <div className="glass-card p-8 rounded-2xl shadow-floating border border-white/20">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong!</h1>
            <p className="text-gray-600 mb-6">
              We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={reset}
                className="btn-gradient text-white"
              >
                Try Again
              </Button>
              <Link href="/">
                <Button variant="outline">
                  Go Home
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Technical Details (Development Mode) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 glass-card p-4 rounded-lg border border-red-200 bg-red-50/80">
            <details className="text-left">
              <summary className="text-sm font-medium text-red-800 cursor-pointer">
                Technical Details (Development Only)
              </summary>
              <div className="mt-2 text-xs text-red-700 font-mono">
                <p><strong>Error:</strong> {error.message}</p>
                {error.digest && <p><strong>Digest:</strong> {error.digest}</p>}
                <pre className="mt-2 whitespace-pre-wrap text-xs">
                  {error.stack}
                </pre>
              </div>
            </details>
          </div>
        )}

        {/* Help Links */}
        <div className="mt-8 text-center">
          <p className="text-white/70 text-sm mb-4">
            If this problem persists, please contact our support team.
          </p>
          <div className="flex justify-center space-x-6 text-sm">
            <Link href="/contact" className="text-white/80 hover:text-white transition-colors">
              Contact Support
            </Link>
            <Link href="/help" className="text-white/80 hover:text-white transition-colors">
              Help Center
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}