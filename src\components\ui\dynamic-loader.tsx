'use client'

import { Suspense, lazy } from 'react'
import { LoadingSpinner } from './loading'

interface DynamicLoaderProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
}

export function DynamicLoader({ 
  children, 
  fallback = <LoadingSpinner size="lg" className="mx-auto" />,
  className = "min-h-[200px] flex items-center justify-center"
}: DynamicLoaderProps) {
  return (
    <Suspense fallback={
      <div className={className}>
        {fallback}
      </div>
    }>
      {children}
    </Suspense>
  )
}

// Higher-order component for dynamic imports
export function withDynamicLoading<T extends object>(
  Component: React.ComponentType<T>,
  loadingComponent?: React.ReactNode
) {
  return function DynamicComponent(props: T) {
    return (
      <DynamicLoader fallback={loadingComponent}>
        <Component {...props} />
      </DynamicLoader>
    )
  }
}

// Lazy loading utility for heavy components
export const createLazyComponent = <T extends object>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  fallback?: React.ReactNode
) => {
  const LazyComponent = lazy(importFn)
  
  return function LazyWrapper(props: T) {
    return (
      <DynamicLoader fallback={fallback}>
        <LazyComponent {...props} />
      </DynamicLoader>
    )
  }
}
