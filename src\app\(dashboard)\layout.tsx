'use client'

import { useState } from 'react'
import { AuthProvider } from '@/context/auth-context'
import { DashboardSidebar } from '@/components/layout/dashboard-sidebar'
import { DashboardHeader } from '@/components/layout/dashboard-header'
import { MobileOptimized } from '@/components/layout/mobile-optimized'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  return (
    <AuthProvider>
      <MobileOptimized>
        <div className="h-screen flex bg-dashboard">
          {/* Sidebar - Hidden on mobile, shown in drawer */}
          <div className="hidden md:block">
            <DashboardSidebar />
          </div>

          {/* Mobile Sidebar Drawer */}
          <DashboardSidebar
            isMobileOpen={isMobileSidebarOpen}
            onMobileClose={() => setIsMobileSidebarOpen(false)}
          />

          {/* Main content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <DashboardHeader
              onMobileMenuClick={() => setIsMobileSidebarOpen(true)}
            />

            {/* Page content */}
            <main className="flex-1 overflow-y-auto p-3 md:p-6">
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </main>
          </div>
        </div>
      </MobileOptimized>
    </AuthProvider>
  )
}