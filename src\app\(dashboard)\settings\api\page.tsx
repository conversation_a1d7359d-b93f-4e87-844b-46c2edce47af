'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/context/auth-context'
import { createSupabaseClient } from '@/lib/supabase'
import { toast } from 'sonner'

const apiConfigSchema = z.object({
  heygen_api_key: z.string().min(1, 'HeyGen API key is required'),
  default_avatar_id: z.string().optional(),
  default_voice_id: z.string().optional(),
})

type ApiConfigForm = z.infer<typeof apiConfigSchema>

interface ApiStatus {
  status: 'testing' | 'connected' | 'error' | 'idle'
  message?: string
  lastTested?: string
}

export default function ApiConfigurationPage() {
  const { user } = useAuth()
  const [apiStatus, setApiStatus] = useState<ApiStatus>({ status: 'idle' })
  const [availableAvatars, setAvailableAvatars] = useState<any[]>([])
  const [availableVoices, setAvailableVoices] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ApiConfigForm>({
    resolver: zodResolver(apiConfigSchema),
  })

  const watchedApiKey = watch('heygen_api_key')

  // Load current settings
  useEffect(() => {
    if (user) {
      loadCurrentSettings()
    }
  }, [user])

  const loadCurrentSettings = async () => {
    try {
      const supabase = createSupabaseClient()
      const { data: settings } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user?.id)
        .single()

      if (settings) {
        setValue('heygen_api_key', settings.heygen_api_key || '')
        setValue('default_avatar_id', settings.default_avatar_id || '')
        setValue('default_voice_id', settings.default_voice_id || '')
        
        if (settings.heygen_api_key) {
          setApiStatus({ 
            status: 'connected', 
            message: 'API key configured',
            lastTested: settings.last_api_test || undefined
          })
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  const testApiConnection = async () => {
    if (!watchedApiKey) {
      toast.error('Please enter an API key first')
      return
    }

    setApiStatus({ status: 'testing', message: 'Testing connection...' })
    
    try {
      // Test API connection
      const response = await fetch('/api/test/heygen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: watchedApiKey,
        }),
      })

      const result = await response.json()

      if (result.success) {
        setApiStatus({
          status: 'connected',
          message: 'API connection successful!',
          lastTested: new Date().toISOString(),
        })
        
        // Load avatars and voices
        if (result.avatars) setAvailableAvatars(result.avatars)
        if (result.voices) setAvailableVoices(result.voices)
        
        toast.success('API connection successful!')
      } else {
        setApiStatus({
          status: 'error',
          message: result.error || 'API connection failed',
        })
        toast.error(result.error || 'API connection failed')
      }
    } catch (error) {
      setApiStatus({
        status: 'error',
        message: 'Failed to test API connection',
      })
      toast.error('Failed to test API connection')
    }
  }

  const onSubmit = async (data: ApiConfigForm) => {
    setIsSaving(true)
    
    try {
      const supabase = createSupabaseClient()
      
      // Save or update user settings
      const { error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user?.id,
          heygen_api_key: data.heygen_api_key,
          default_avatar_id: data.default_avatar_id,
          default_voice_id: data.default_voice_id,
          last_api_test: apiStatus.status === 'connected' ? apiStatus.lastTested : null,
          updated_at: new Date().toISOString(),
        })

      if (error) {
        throw error
      }

      toast.success('API configuration saved successfully!')
    } catch (error) {
      console.error('Failed to save settings:', error)
      toast.error('Failed to save API configuration')
    } finally {
      setIsSaving(false)
    }
  }

  const getStatusBadge = () => {
    switch (apiStatus.status) {
      case 'connected':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Connected</Badge>
      case 'testing':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Testing...</Badge>
      case 'error':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">Error</Badge>
      default:
        return <Badge variant="outline">Not configured</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Card className="glass-card shadow-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-gray-900">HeyGen API Configuration</CardTitle>
              <CardDescription>
                Configure your HeyGen API credentials to enable video generation
              </CardDescription>
            </div>
            {getStatusBadge()}
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* API Key */}
            <div className="space-y-2">
              <Label htmlFor="heygen_api_key">HeyGen API Key</Label>
              <div className="flex gap-3">
                <Input
                  id="heygen_api_key"
                  type="password"
                  placeholder="Enter your HeyGen API key"
                  {...register('heygen_api_key')}
                  className="flex-1"
                />
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={testApiConnection}
                  disabled={!watchedApiKey || apiStatus.status === 'testing'}
                >
                  {apiStatus.status === 'testing' ? 'Testing...' : 'Test Connection'}
                </Button>
              </div>
              {errors.heygen_api_key && (
                <p className="text-sm text-red-600">{errors.heygen_api_key.message}</p>
              )}
              {apiStatus.message && (
                <p className={`text-sm ${
                  apiStatus.status === 'connected' ? 'text-green-600' : 
                  apiStatus.status === 'error' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {apiStatus.message}
                </p>
              )}
            </div>

            {/* Help Text */}
            <div className="bg-blue-50/80 p-4 rounded-lg border border-blue-200/50">
              <h4 className="text-sm font-medium text-blue-900 mb-2">How to get your HeyGen API key:</h4>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Visit the <a href="https://app.heygen.com/settings/api" target="_blank" rel="noopener noreferrer" className="underline hover:no-underline">HeyGen API settings page</a></li>
                <li>Generate a new API key or copy your existing one</li>
                <li>Paste it in the field above and test the connection</li>
              </ol>
            </div>

            {/* Default Selections */}
            {(availableAvatars.length > 0 || availableVoices.length > 0) && (
              <div className="grid md:grid-cols-2 gap-6">
                {/* Default Avatar */}
                {availableAvatars.length > 0 && (
                  <div className="space-y-2">
                    <Label htmlFor="default_avatar_id">Default Avatar</Label>
                    <select
                      id="default_avatar_id"
                      {...register('default_avatar_id')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a default avatar</option>
                      {availableAvatars.map((avatar) => (
                        <option key={avatar.id} value={avatar.id}>
                          {avatar.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Default Voice */}
                {availableVoices.length > 0 && (
                  <div className="space-y-2">
                    <Label htmlFor="default_voice_id">Default Voice</Label>
                    <select
                      id="default_voice_id"
                      {...register('default_voice_id')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a default voice</option>
                      {availableVoices.map((voice) => (
                        <option key={voice.id} value={voice.id}>
                          {voice.name} ({voice.language})
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                className="btn-gradient text-white"
                disabled={isSaving}
              >
                {isSaving ? 'Saving...' : 'Save Configuration'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Status Information */}
      {apiStatus.lastTested && (
        <Card className="glass-card shadow-card border-white/20">
          <CardHeader>
            <CardTitle className="text-gray-900">Connection Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last tested:</span>
                <span className="text-sm text-gray-900">
                  {new Date(apiStatus.lastTested).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Available avatars:</span>
                <span className="text-sm text-gray-900">{availableAvatars.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Available voices:</span>
                <span className="text-sm text-gray-900">{availableVoices.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}