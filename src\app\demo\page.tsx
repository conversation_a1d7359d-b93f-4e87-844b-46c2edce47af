'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

export default function DemoPage() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [currentStep, setCurrentStep] = useState(1)

  const nextStep = () => {
    if (currentStep < 6) setCurrentStep(currentStep + 1)
  }

  const previousStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  const progress = ((currentStep - 1) / 5) * 100

  return (
    <div className="min-h-screen bg-gradient-hero">
      {/* Header */}
      <header className="glass-nav border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-premium rounded-lg"></div>
              <h1 className="text-xl font-bold text-white">AI Video SaaS Demo</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-white/80">5 credits remaining</div>
              <div className="w-8 h-8 bg-white/20 rounded-full"></div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-white/20">
            <nav className="-mb-px flex space-x-8">
              <button 
                onClick={() => setActiveTab('dashboard')} 
                className={`border-b-2 py-2 px-1 text-sm font-medium transition-colors ${
                  activeTab === 'dashboard' 
                    ? 'border-white text-white' 
                    : 'border-transparent text-white/70 hover:text-white'
                }`}
              >
                Dashboard
              </button>
              <button 
                onClick={() => setActiveTab('create')} 
                className={`border-b-2 py-2 px-1 text-sm font-medium transition-colors ${
                  activeTab === 'create' 
                    ? 'border-white text-white' 
                    : 'border-transparent text-white/70 hover:text-white'
                }`}
              >
                Create Video
              </button>
              <button 
                onClick={() => setActiveTab('videos')} 
                className={`border-b-2 py-2 px-1 text-sm font-medium transition-colors ${
                  activeTab === 'videos' 
                    ? 'border-white text-white' 
                    : 'border-transparent text-white/70 hover:text-white'
                }`}
              >
                Video Library
              </button>
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8 animate-fade-in">
            <div className="grid gap-6 md:grid-cols-4">
              <Card className="glass-card p-6 card-float">
                <div className="text-2xl font-bold text-gradient mb-2">12</div>
                <div className="text-sm text-white/70">Total Videos</div>
              </Card>
              <Card className="glass-card p-6 card-float">
                <div className="text-2xl font-bold text-green-400 mb-2">8</div>
                <div className="text-sm text-white/70">Completed</div>
              </Card>
              <Card className="glass-card p-6 card-float">
                <div className="text-2xl font-bold text-yellow-400 mb-2">2</div>
                <div className="text-sm text-white/70">Processing</div>
              </Card>
              <Card className="glass-card p-6 card-float">
                <div className="text-2xl font-bold text-gradient mb-2">156</div>
                <div className="text-sm text-white/70">Total Views</div>
              </Card>
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
              <Card className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Recent Videos</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                    <div className="w-16 h-10 bg-gradient-to-br from-purple-400 to-pink-400 rounded"></div>
                    <div className="flex-1">
                      <div className="font-medium text-white">Welcome to Our Product</div>
                      <div className="text-sm text-white/60">Created 2 hours ago</div>
                    </div>
                    <Badge className="bg-green-500/20 text-green-400 border-0">Completed</Badge>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                    <div className="w-16 h-10 bg-gradient-to-br from-blue-400 to-purple-400 rounded"></div>
                    <div className="flex-1">
                      <div className="font-medium text-white">Product Demo Video</div>
                      <div className="text-sm text-white/60">Created 1 day ago</div>
                    </div>
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-0">Processing</Badge>
                  </div>
                </div>
              </Card>

              <Card className="glass-card p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Button 
                    onClick={() => setActiveTab('create')} 
                    className="w-full btn-premium text-white justify-center"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create New Video
                  </Button>
                  <Button 
                    onClick={() => setActiveTab('videos')} 
                    className="w-full btn-glass text-white justify-center"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    View Video Library
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* Create Video Tab */}
        {activeTab === 'create' && (
          <div className="space-y-6 animate-fade-in">
            {/* Progress Header */}
            <Card className="glass-card p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-white">Step {currentStep} of 6</h2>
                  <p className="text-sm text-white/70 mt-1">
                    {currentStep === 1 && "Write or enhance your script"}
                    {currentStep === 2 && "Choose your AI presenter"}
                    {currentStep === 3 && "Select the perfect voice"}
                    {currentStep === 4 && "Configure video settings"}
                    {currentStep === 5 && "Review and generate"}
                    {currentStep === 6 && "Creating your video"}
                  </p>
                </div>
                <Badge className="bg-gradient-premium text-white border-0 px-3 py-1">
                  {Math.round(progress)}% Complete
                </Badge>
              </div>
              <Progress value={progress} className="h-2" />
            </Card>

            {/* Step Navigation */}
            <div className="flex justify-center mb-6">
              <div className="flex items-center space-x-4">
                {[1, 2, 3, 4, 5, 6].map((step, index) => (
                  <div key={step} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      currentStep > step
                        ? 'bg-green-400 text-white'
                        : currentStep === step
                        ? 'bg-gradient-premium text-white'
                        : 'bg-white/20 text-white/60'
                    }`}>
                      {currentStep > step ? (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        step
                      )}
                    </div>
                    <span className="ml-2 text-sm font-medium text-white/80 hidden sm:block">
                      {step === 1 && "Script"}
                      {step === 2 && "Avatar"}
                      {step === 3 && "Voice"}
                      {step === 4 && "Settings"}
                      {step === 5 && "Review"}
                      {step === 6 && "Generate"}
                    </span>
                    {index < 5 && <div className="w-8 h-px bg-white/30 ml-4"></div>}
                  </div>
                ))}
              </div>
            </div>

            {/* Step Content */}
            <Card className="glass-card p-6">
              {currentStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Script & Content</h3>
                  <p className="text-sm text-white/70">Write your script or use our AI-powered enhancement to create engaging content</p>
                  
                  <div>
                    <label className="text-sm font-medium text-white/90 mb-2 block">Video Title *</label>
                    <input 
                      type="text" 
                      placeholder="Enter a compelling title for your video" 
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30" 
                      defaultValue="Welcome to Our Amazing Product"
                    />
                  </div>
                  
                  <div className="flex gap-3 mb-4">
                    <Button className="btn-glass text-white">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                      Use Template
                    </Button>
                    <Button className="btn-glass text-white">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                      Enhance with AI
                    </Button>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="text-sm font-medium text-white/90">Script Content *</label>
                      <div className="flex items-center space-x-4 text-xs text-white/60">
                        <span>42 words</span>
                        <span>234 characters</span>
                        <span>~17s duration</span>
                      </div>
                    </div>
                    <textarea 
                      rows={8} 
                      placeholder="Enter your script here..." 
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
                      defaultValue="Welcome to our amazing platform! Today I want to show you how our revolutionary product can transform your business. With our cutting-edge AI technology, you can create professional videos in minutes, not hours. Let me walk you through the key features that make us the industry leader."
                    />
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Choose Your AI Avatar</h3>
                  <p className="text-sm text-white/70">Select an AI presenter that best represents your brand and message</p>
                  
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <div className="relative group cursor-pointer rounded-lg border-2 border-gradient-premium ring-2 ring-white/20 shadow-floating card-float">
                      <div className="relative overflow-hidden rounded-lg">
                        <div className="w-full aspect-[3/4] bg-gradient-to-b from-purple-400/50 to-pink-400/50 flex items-center justify-center backdrop-blur-sm">
                          <span className="text-4xl">👩‍💼</span>
                        </div>
                        <div className="absolute top-2 left-2">
                          <div className="w-6 h-6 bg-green-400 text-white rounded-full flex items-center justify-center">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="p-3 bg-white/5 backdrop-blur-sm">
                        <h4 className="font-medium text-white">Sarah</h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className="bg-white/10 text-white/80 border-0 text-xs">female</Badge>
                          <Badge className="bg-white/10 text-white/80 border-0 text-xs">professional</Badge>
                        </div>
                      </div>
                    </div>
                    
                    {['David', 'Emma', 'Michael'].map((name, index) => (
                      <div key={name} className="relative group cursor-pointer rounded-lg border-2 border-white/20 hover:border-white/40 transition-colors card-float">
                        <div className="relative overflow-hidden rounded-lg">
                          <div className={`w-full aspect-[3/4] bg-gradient-to-b ${
                            index === 0 ? 'from-green-400/50 to-blue-400/50' :
                            index === 1 ? 'from-purple-400/50 to-pink-400/50' :
                            'from-orange-400/50 to-red-400/50'
                          } flex items-center justify-center backdrop-blur-sm`}>
                            <span className="text-4xl">
                              {index === 0 ? '👨‍💼' : index === 1 ? '👩‍🎓' : '👨‍💻'}
                            </span>
                          </div>
                        </div>
                        <div className="p-3 bg-white/5 backdrop-blur-sm">
                          <h4 className="font-medium text-white">{name}</h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge className="bg-white/10 text-white/80 border-0 text-xs">
                              {index === 0 || index === 2 ? 'male' : 'female'}
                            </Badge>
                            <Badge className="bg-white/10 text-white/80 border-0 text-xs">
                              {index === 0 ? 'business' : index === 1 ? 'casual' : 'executive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {currentStep >= 3 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-premium rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Step {currentStep} - Coming Soon</h3>
                  <p className="text-white/70">This step will be available in the full application</p>
                </div>
              )}
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                onClick={previousStep}
                disabled={currentStep === 1}
                className="btn-glass text-white disabled:opacity-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Previous
              </Button>
              <Button
                onClick={nextStep}
                disabled={currentStep === 6}
                className="btn-premium text-white disabled:opacity-50"
              >
                Next
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </Button>
            </div>
          </div>
        )}

        {/* Video Library Tab */}
        {activeTab === 'videos' && (
          <div className="space-y-6 animate-fade-in">
            {/* Header */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold text-white">My Videos</h2>
                <p className="text-white/70">Manage and organize your AI-generated videos</p>
              </div>
              <Button onClick={() => setActiveTab('create')} className="btn-premium text-white">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Video
              </Button>
            </div>

            {/* Filters */}
            <Card className="glass-card p-4">
              <div className="flex flex-col lg:flex-row gap-4 items-center">
                <div className="flex-1">
                  <input 
                    type="text" 
                    placeholder="Search videos..." 
                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button className="btn-premium text-white text-sm">All</Button>
                  <Button className="btn-glass text-white text-sm">Completed</Button>
                  <Button className="btn-glass text-white text-sm">Processing</Button>
                </div>
              </div>
            </Card>

            {/* Video Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Video Card 1 */}
              <Card className="glass-card overflow-hidden card-float">
                <div className="relative">
                  <div className="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center">
                    <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z"></path>
                    </svg>
                  </div>
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-green-500/20 text-green-400 border-0">completed</Badge>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black/75 text-white px-2 py-1 rounded text-xs">
                    0:45
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-white mb-2">Welcome to Our Product</h3>
                  <div className="flex items-center justify-between text-sm text-white/60 mb-2">
                    <span>Jan 15, 2024</span>
                    <span>16:9</span>
                  </div>
                  <div className="text-xs text-white/60 mb-2">Avatar: Sarah</div>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>156 views</span>
                    <span>2.3 MB</span>
                  </div>
                </div>
              </Card>

              {/* Video Card 2 - Processing */}
              <Card className="glass-card overflow-hidden card-float">
                <div className="relative">
                  <div className="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-white text-sm">Processing...</p>
                    </div>
                  </div>
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-0">processing</Badge>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-white mb-2">Product Demo - New Features</h3>
                  <div className="flex items-center justify-between text-sm text-white/60 mb-2">
                    <span>Jan 16, 2024</span>
                    <span>9:16</span>
                  </div>
                  <div className="text-xs text-white/60 mb-2">Avatar: David</div>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>0 views</span>
                    <span>-</span>
                  </div>
                </div>
              </Card>

              {/* Video Card 3 */}
              <Card className="glass-card overflow-hidden card-float">
                <div className="relative">
                  <div className="w-full h-48 bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center">
                    <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z"></path>
                    </svg>
                  </div>
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-green-500/20 text-green-400 border-0">completed</Badge>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black/75 text-white px-2 py-1 rounded text-xs">
                    2:00
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-white mb-2">Monthly Update Video</h3>
                  <div className="flex items-center justify-between text-sm text-white/60 mb-2">
                    <span>Jan 14, 2024</span>
                    <span>1:1</span>
                  </div>
                  <div className="text-xs text-white/60 mb-2">Avatar: Emma</div>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <span>89 views</span>
                    <span>4.1 MB</span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 