import { NextRequest, NextResponse } from 'next/server'
import { constructEvent, getTierFromPriceId } from '@/lib/stripe'
import { createSupabaseAdminClient } from '@/lib/supabase'
import { PRICING_TIERS } from '@/types'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }

    // Verify webhook signature
    const event = constructEvent(body, signature)
    const supabase = createSupabaseAdminClient()

    console.log('Stripe webhook event:', event.type)

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        
        if (session.mode === 'subscription' && session.subscription) {
          // Handle successful subscription checkout
          const subscriptionId = session.subscription as string
          const customerId = session.customer as string
          const userId = session.client_reference_id || session.metadata?.userId

          if (!userId) {
            console.error('No user ID found in checkout session')
            break
          }

          // Update user with customer ID
          await supabase
            .from('users')
            .update({ stripe_customer_id: customerId })
            .eq('id', userId)

          console.log(`Checkout completed for user ${userId}, subscription ${subscriptionId}`)
        }
        break
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        
        // Get user by customer ID
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('stripe_customer_id', subscription.customer)
          .single()

        if (userError || !userData) {
          console.error('User not found for customer:', subscription.customer)
          break
        }

        const priceId = subscription.items.data[0]?.price.id
        const tier = getTierFromPriceId(priceId)
        
        if (!tier) {
          console.error('Unknown price ID:', priceId)
          break
        }

        const tierData = PRICING_TIERS.find(t => t.id === tier)
        if (!tierData) {
          console.error('Tier data not found for:', tier)
          break
        }

        // Update or create subscription record
        const subscriptionData = {
          user_id: userData.id,
          stripe_subscription_id: subscription.id,
          stripe_customer_id: subscription.customer as string,
          tier,
          status: subscription.status as any,
          current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        }

        const { error: subscriptionError } = await supabase
          .from('subscriptions')
          .upsert(subscriptionData, { onConflict: 'stripe_subscription_id' })

        if (subscriptionError) {
          console.error('Failed to upsert subscription:', subscriptionError)
          break
        }

        // Update user tier and credits
        const { error: userUpdateError } = await supabase
          .from('users')
          .update({
            subscription_tier: tier,
            credits_remaining: tierData.monthly_credits,
            credits_total: tierData.monthly_credits,
          })
          .eq('id', userData.id)

        if (userUpdateError) {
          console.error('Failed to update user tier:', userUpdateError)
        }

        console.log(`Subscription ${event.type} for user ${userData.id}, tier ${tier}`)
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription

        // Get user by customer ID
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('stripe_customer_id', subscription.customer)
          .single()

        if (userError || !userData) {
          console.error('User not found for customer:', subscription.customer)
          break
        }

        // Update subscription status
        await supabase
          .from('subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', subscription.id)

        // Downgrade user to free tier
        const freeTier = PRICING_TIERS.find(t => t.id === 'free')
        if (freeTier) {
          await supabase
            .from('users')
            .update({
              subscription_tier: 'free',
              credits_remaining: freeTier.monthly_credits,
              credits_total: freeTier.monthly_credits,
            })
            .eq('id', userData.id)
        }

        console.log(`Subscription canceled for user ${userData.id}`)
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        
        if (invoice.subscription) {
          // Get user by customer ID
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, subscription_tier')
            .eq('stripe_customer_id', invoice.customer)
            .single()

          if (userError || !userData) {
            console.error('User not found for customer:', invoice.customer)
            break
          }

          // Record payment history
          await supabase
            .from('payment_history')
            .insert({
              user_id: userData.id,
              stripe_payment_intent_id: invoice.payment_intent as string,
              amount: invoice.amount_paid,
              currency: invoice.currency,
              status: 'succeeded',
              description: invoice.description || 'Subscription payment',
            })

          // Reset credits for the new billing period
          const tierData = PRICING_TIERS.find(t => t.id === userData.subscription_tier)
          if (tierData) {
            await supabase
              .from('users')
              .update({
                credits_remaining: tierData.monthly_credits,
                credits_total: tierData.monthly_credits,
              })
              .eq('id', userData.id)
          }

          console.log(`Payment succeeded for user ${userData.id}, amount ${invoice.amount_paid}`)
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        
        // Get user by customer ID
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('stripe_customer_id', invoice.customer)
          .single()

        if (userError || !userData) {
          console.error('User not found for customer:', invoice.customer)
          break
        }

        // Record failed payment
        await supabase
          .from('payment_history')
          .insert({
            user_id: userData.id,
            stripe_payment_intent_id: invoice.payment_intent as string,
            amount: invoice.amount_due,
            currency: invoice.currency,
            status: 'failed',
            description: invoice.description || 'Subscription payment failed',
          })

        // Update subscription status to past_due
        if (invoice.subscription) {
          await supabase
            .from('subscriptions')
            .update({ status: 'past_due' })
            .eq('stripe_subscription_id', invoice.subscription)
        }

        console.log(`Payment failed for user ${userData.id}, amount ${invoice.amount_due}`)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    )
  }
}