import { Database } from './database'

// Database types
export type User = Database['public']['Tables']['users']['Row']
export type Video = Database['public']['Tables']['videos']['Row']
export type Subscription = Database['public']['Tables']['subscriptions']['Row']
export type PaymentHistory = Database['public']['Tables']['payment_history']['Row']
export type VideoTemplate = Database['public']['Tables']['video_templates']['Row']
export type UserSettings = Database['public']['Tables']['user_settings']['Row']
export type SubscriptionTierCredits = Database['public']['Tables']['subscription_tier_credits']['Row']

// Insert types
export type InsertUser = Database['public']['Tables']['users']['Insert']
export type InsertVideo = Database['public']['Tables']['videos']['Insert']
export type InsertSubscription = Database['public']['Tables']['subscriptions']['Insert']
export type InsertPaymentHistory = Database['public']['Tables']['payment_history']['Insert']
export type InsertVideoTemplate = Database['public']['Tables']['video_templates']['Insert']
export type InsertUserSettings = Database['public']['Tables']['user_settings']['Insert']

// Update types
export type UpdateUser = Database['public']['Tables']['users']['Update']
export type UpdateVideo = Database['public']['Tables']['videos']['Update']
export type UpdateSubscription = Database['public']['Tables']['subscriptions']['Update']
export type UpdateUserSettings = Database['public']['Tables']['user_settings']['Update']

// Enum types
export type SubscriptionTier = Database['public']['Enums']['subscription_tier']
export type VideoStatus = Database['public']['Enums']['video_status']
export type SubscriptionStatus = Database['public']['Enums']['subscription_status']

// HeyGen API types
export interface HeyGenAvatar {
  id: string
  name: string
  preview_image_url: string
  gender: 'male' | 'female'
  age_range: string
  style: string
  is_public: boolean
}

export interface HeyGenVoice {
  id: string
  name: string
  language: string
  accent: string
  gender: 'male' | 'female'
  age_range: string
  preview_audio_url: string
  is_public: boolean
}

export interface HeyGenGenerateVideoRequest {
  script: string
  avatar_id: string
  voice_id: string
  title?: string
  aspect_ratio?: '16:9' | '9:16' | '1:1'
  background?: string
}

export interface HeyGenGenerateVideoResponse {
  video_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  message?: string
}

export interface HeyGenVideoStatus {
  video_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  video_url?: string
  thumbnail_url?: string
  duration?: number
  error_message?: string
}

// Groq API types
export interface GroqEnhanceScriptRequest {
  script: string
  tone?: 'professional' | 'casual' | 'friendly' | 'persuasive' | 'educational'
  target_audience?: string
  purpose?: 'marketing' | 'education' | 'entertainment' | 'business'
  max_length?: number
}

export interface GroqEnhanceScriptResponse {
  enhanced_script: string
  improvements: string[]
  readability_score: number
  word_count: number
}

// Stripe types
export interface StripeCheckoutSession {
  id: string
  url: string
  payment_status: string
}

export interface StripeCustomerPortal {
  url: string
}

// Application types
export interface VideoCreationForm {
  title: string
  script: string
  avatar_id: string
  voice_id: string
  aspect_ratio: '16:9' | '9:16' | '1:1'
  background?: string
  enhance_script?: boolean
}

export interface VideoLibraryFilters {
  status?: VideoStatus[]
  date_range?: {
    start: string
    end: string
  }
  search?: string
  sort_by?: 'created_at' | 'title' | 'duration'
  sort_order?: 'asc' | 'desc'
}

export interface DashboardStats {
  total_videos: number
  videos_this_month: number
  credits_used: number
  credits_remaining: number
  subscription_tier: SubscriptionTier
  next_billing_date?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Authentication types
export interface AuthUser {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
}

export interface AuthSession {
  user: AuthUser
  access_token: string
  refresh_token: string
  expires_at: number
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface SignupForm {
  email: string
  password: string
  full_name: string
  terms_accepted: boolean
}

export interface ForgotPasswordForm {
  email: string
}

export interface ResetPasswordForm {
  password: string
  confirm_password: string
}

// Subscription pricing
export interface PricingTier {
  id: SubscriptionTier
  name: string
  description: string
  price_cents: number
  monthly_credits: number
  features: string[]
  stripe_price_id?: string
  popular?: boolean
}

export const PRICING_TIERS: PricingTier[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for trying out our platform',
    price_cents: 0,
    monthly_credits: 5,
    features: [
      '5 videos per month',
      'Basic avatars',
      'Standard voices',
      'HD video quality',
      'Community support'
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    description: 'Great for individuals and small teams',
    price_cents: 2900,
    monthly_credits: 100,
    features: [
      '100 videos per month',
      'All avatars',
      'Premium voices',
      'HD video quality',
      'Email support',
      'Custom backgrounds'
    ],
    stripe_price_id: 'price_starter_monthly'
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Perfect for growing businesses',
    price_cents: 9900,
    monthly_credits: 500,
    features: [
      '500 videos per month',
      'All avatars',
      'Premium voices',
      '4K video quality',
      'Priority support',
      'Custom backgrounds',
      'API access',
      'Bulk generation'
    ],
    stripe_price_id: 'price_pro_monthly',
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large organizations',
    price_cents: 29900,
    monthly_credits: 2000,
    features: [
      '2000 videos per month',
      'All avatars',
      'Premium voices',
      '4K video quality',
      'Dedicated support',
      'Custom backgrounds',
      'API access',
      'Bulk generation',
      'Custom integrations',
      'White-label options'
    ],
    stripe_price_id: 'price_enterprise_monthly'
  }
]

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type NonNullable<T> = T extends null | undefined ? never : T

export type OptionalExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

export interface SortConfig {
  key: string
  direction: 'asc' | 'desc'
}