import { createSupabaseClient, createSupabaseAdminClient } from './supabase'

export async function testSupabaseConnection() {
  try {
    const supabase = createSupabaseClient()
    
    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('Supabase connection test failed:', error)
      return {
        success: false,
        error: error.message,
        details: 'Failed to query users table'
      }
    }
    
    console.log('✅ Supabase connection test passed')
    return {
      success: true,
      message: 'Successfully connected to Supabase',
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('Supabase connection error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to establish connection'
    }
  }
}

export async function testSupabaseAuth() {
  try {
    const supabase = createSupabaseClient()
    
    // Test auth state
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Supabase auth test failed:', error)
      return {
        success: false,
        error: error.message,
        details: 'Failed to get auth session'
      }
    }
    
    console.log('✅ Supabase auth test passed')
    return {
      success: true,
      message: 'Auth system is functional',
      hasSession: !!session,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('Supabase auth error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to test auth system'
    }
  }
}

export async function testDatabaseTables() {
  try {
    const supabase = createSupabaseAdminClient()
    
    // Test all main tables
    const tables = ['users', 'videos', 'subscriptions', 'user_settings']
    const results = []
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count')
          .limit(1)
        
        if (error) {
          results.push({
            table,
            success: false,
            error: error.message
          })
        } else {
          results.push({
            table,
            success: true,
            message: 'Table accessible'
          })
        }
      } catch (tableError) {
        results.push({
          table,
          success: false,
          error: tableError instanceof Error ? tableError.message : 'Unknown error'
        })
      }
    }
    
    const allSuccess = results.every(r => r.success)
    
    if (allSuccess) {
      console.log('✅ All database tables accessible')
    } else {
      console.warn('⚠️ Some database tables have issues:', results.filter(r => !r.success))
    }
    
    return {
      success: allSuccess,
      results,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('Database tables test error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to test database tables'
    }
  }
}

export async function runAllConnectionTests() {
  console.log('🔄 Running Supabase connection tests...')
  
  const results = {
    connection: await testSupabaseConnection(),
    auth: await testSupabaseAuth(),
    tables: await testDatabaseTables(),
    timestamp: new Date().toISOString()
  }
  
  const allPassed = results.connection.success && results.auth.success && results.tables.success
  
  if (allPassed) {
    console.log('✅ All Supabase tests passed! System is ready.')
  } else {
    console.error('❌ Some Supabase tests failed. Check configuration.')
  }
  
  return {
    success: allPassed,
    results,
    summary: {
      passed: Object.values(results).filter(r => r.success).length - 1, // -1 for timestamp
      total: 3,
      timestamp: results.timestamp
    }
  }
}