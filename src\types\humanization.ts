// Advanced AI Video Humanization Types
// These types define the comprehensive humanization system for AI-generated videos

export interface MicroExpression {
  id: string
  type: 'blink' | 'eye_movement' | 'subtle_smile' | 'eyebrow_raise' | 'head_tilt' | 'micro_nod'
  timing: number // seconds into the video
  duration: number // duration in seconds
  intensity: number // 0.1 to 1.0 scale
  context: 'emphasis' | 'pause' | 'transition' | 'engagement' | 'natural'
}

export interface VoiceModulation {
  id: string
  startTime: number
  endTime: number
  modulationType: 'emotion' | 'emphasis' | 'hesitation' | 'breathing' | 'pace_change'
  intensity: number // 0.1 to 1.0
  parameters: {
    pitch_variation?: number
    pace_multiplier?: number
    volume_adjustment?: number
    emotion_type?: 'excitement' | 'concern' | 'curiosity' | 'confidence' | 'warmth'
    breathing_pattern?: 'natural' | 'slight_pause' | 'deep_breath'
    hesitation_type?: 'um' | 'uh' | 'pause' | 'slight_stutter'
  }
}

export interface GestureIntelligence {
  id: string
  timing: number
  duration: number
  gestureType: 'hand_movement' | 'head_gesture' | 'body_lean' | 'finger_point' | 'open_palm' | 'thinking_pose'
  context: string // what part of script this relates to
  intensity: 'subtle' | 'moderate' | 'pronounced'
  coordination: {
    matches_voice_emphasis: boolean
    relates_to_content: boolean
    natural_flow: boolean
  }
}

export interface ConversationalFlow {
  robotic_patterns: {
    pattern_type: 'monotone_delivery' | 'unnatural_pauses' | 'robotic_transitions' | 'lack_of_variation'
    confidence_score: number // 0-1, higher means more robotic
    suggested_fix: string
    text_location: {
      start_char: number
      end_char: number
    }
  }[]
  natural_flow_score: number // 0-1, higher is more natural
  improvement_suggestions: string[]
}

export interface EmotionMapping {
  segment_id: string
  start_time: number
  end_time: number
  detected_emotion: 'neutral' | 'excited' | 'serious' | 'friendly' | 'urgent' | 'explanatory' | 'conclusive'
  emotion_intensity: number // 0-1
  voice_adjustments: VoiceModulation[]
  expression_adjustments: MicroExpression[]
  gesture_suggestions: GestureIntelligence[]
}

export interface VisualIntelligence {
  scene_transitions: {
    from_time: number
    to_time: number
    transition_type: 'cut' | 'fade' | 'slide' | 'zoom' | 'dissolve'
    reason: string // why this transition was chosen
    natural_flow_score: number
  }[]
  visual_elements: {
    element_type: 'text_overlay' | 'highlight' | 'arrow' | 'background_change' | 'focus_shift'
    timing: number
    duration: number
    content_relevance: string
    visual_impact: 'subtle' | 'moderate' | 'strong'
  }[]
  camera_movements: {
    movement_type: 'zoom_in' | 'zoom_out' | 'pan_left' | 'pan_right' | 'focus_shift' | 'slight_shake'
    timing: number
    duration: number
    intensity: number
    purpose: 'emphasis' | 'engagement' | 'transition' | 'natural_variation'
  }[]
}

export interface NaturalEditingPatterns {
  cut_timing: {
    cuts: {
      time: number
      reason: 'content_break' | 'natural_pause' | 'emphasis' | 'engagement' | 'platform_optimization'
      cut_type: 'hard_cut' | 'soft_cut' | 'jump_cut' | 'match_cut'
    }[]
    pacing_analysis: {
      average_shot_length: number
      pacing_variation: number // 0-1, higher is more varied
      content_density_score: number // 0-1, higher means more information per second
    }
  }
  natural_imperfections: {
    imperfection_type: 'slight_stumble' | 'natural_pause' | 'self_correction' | 'authentic_breath'
    timing: number
    severity: 'barely_noticeable' | 'subtle' | 'natural'
    purpose: 'authenticity' | 'relatability' | 'natural_flow'
  }[]
  audio_enhancements: {
    ambient_sounds: boolean
    natural_room_tone: boolean
    microphone_variations: boolean
    breathing_sounds: boolean
  }
}

export interface PlatformOptimization {
  platform: 'youtube' | 'tiktok' | 'linkedin' | 'twitter' | 'facebook' | 'instagram'
  creator_patterns: {
    typical_pacing: number // seconds per information unit
    common_expressions: string[]
    editing_style: 'fast_cuts' | 'smooth_transitions' | 'dynamic' | 'professional' | 'casual'
    engagement_techniques: string[]
  }
  platform_specific_adjustments: {
    aspect_ratio_optimization: boolean
    length_optimization: boolean
    hook_placement: number // seconds
    call_to_action_timing: number // seconds from end
    trending_elements: string[]
  }
}

export interface QualityAssurance {
  authenticity_score: number // 0-1, higher is more human-like
  detection_risk_factors: {
    factor: string
    risk_level: 'low' | 'medium' | 'high'
    mitigation_suggestion: string
  }[]
  improvement_areas: {
    area: 'voice_naturalness' | 'visual_flow' | 'content_pacing' | 'expression_authenticity'
    current_score: number
    target_score: number
    specific_improvements: string[]
  }[]
  overall_assessment: {
    passes_human_like_threshold: boolean
    confidence_level: number
    ready_for_publication: boolean
    additional_processing_needed: string[]
  }
}

export interface HumanizationConfig {
  // User preferences for humanization
  naturalness_level: 'subtle' | 'moderate' | 'high' | 'maximum'
  preserve_brand_voice: boolean
  platform_specific_optimization: boolean
  target_audience_adaptation: boolean
  
  // Advanced settings
  micro_expression_frequency: number // expressions per minute
  voice_variation_intensity: number // 0-1
  gesture_frequency: number // gestures per minute
  imperfection_rate: number // 0-1, how many natural imperfections to include
  
  // Content-specific settings
  content_type: 'educational' | 'entertainment' | 'business' | 'personal' | 'promotional'
  formality_level: 'casual' | 'semi_formal' | 'formal' | 'highly_formal'
  energy_level: 'calm' | 'moderate' | 'energetic' | 'high_energy'
}

export interface HumanizationResult {
  config_used: HumanizationConfig
  processing_time: number // seconds
  improvements_applied: {
    micro_expressions: MicroExpression[]
    voice_modulations: VoiceModulation[]
    gesture_intelligence: GestureIntelligence[]
    visual_intelligence: VisualIntelligence
    editing_patterns: NaturalEditingPatterns
    platform_optimization: PlatformOptimization
  }
  quality_assessment: QualityAssurance
  before_after_comparison: {
    authenticity_improvement: number // percentage improvement
    engagement_prediction: number // 0-1 predicted engagement increase
    detection_risk_reduction: number // percentage reduction in AI detection risk
  }
}

// API Response Types
export interface HumanizationApiResponse {
  success: boolean
  data?: {
    humanization_result: HumanizationResult
    processing_metadata: {
      model_version: string
      processing_time: number
      quality_checks_passed: boolean
      estimated_improvement: number
    }
  }
  error?: string
}

export interface FlowAnalysisApiResponse {
  success: boolean
  data?: {
    conversational_flow: ConversationalFlow
    emotion_mapping: EmotionMapping[]
    suggestions: {
      high_priority: string[]
      medium_priority: string[]
      low_priority: string[]
    }
  }
  error?: string
}

export interface QualityScoreApiResponse {
  success: boolean
  data?: {
    quality_assessment: QualityAssurance
    recommendations: {
      immediate_fixes: string[]
      enhancement_opportunities: string[]
      advanced_optimizations: string[]
    }
    comparative_analysis: {
      similar_content_benchmark: number
      platform_average: number
      top_performer_comparison: number
    }
  }
  error?: string
}

// Database Extensions for Humanization
export interface VideoHumanization {
  id: string
  video_id: string
  project_id: string
  
  // Configuration
  humanization_config: HumanizationConfig
  
  // Processing Results
  processing_result: HumanizationResult
  
  // Performance Tracking
  before_metrics: {
    authenticity_score: number
    engagement_prediction: number
    detection_risk: number
  }
  after_metrics: {
    authenticity_score: number
    engagement_prediction: number
    detection_risk: number
  }
  
  // Quality Control
  quality_passed: boolean
  manual_review_required: boolean
  human_validation_score?: number
  
  // Metadata
  processing_version: string
  created_at: string
  updated_at: string
  processed_at: string
}

export interface HumanizationTemplate {
  id: string
  user_id: string
  
  template_name: string
  description: string
  
  // Template Configuration
  config: HumanizationConfig
  target_platforms: string[]
  content_categories: string[]
  
  // Performance Data
  usage_count: number
  average_improvement: number
  success_rate: number
  
  // Template Settings
  is_public: boolean
  is_premium: boolean
  
  created_at: string
  updated_at: string
}

// Enhanced Video Project Types with Humanization
export interface EnhancedVideoProjectWithHumanization extends VideoProject {
  humanization_config?: HumanizationConfig
  humanization_result?: HumanizationResult
  quality_assessment?: QualityAssurance
  
  // Performance tracking
  authenticity_scores: {
    initial: number
    final: number
    improvement: number
  }
  
  // A/B testing support
  humanization_variants?: {
    variant_id: string
    config: HumanizationConfig
    performance_metrics: Record<string, number>
  }[]
}

// Import the base types we're extending
import { VideoProject } from './enhanced-workflow'