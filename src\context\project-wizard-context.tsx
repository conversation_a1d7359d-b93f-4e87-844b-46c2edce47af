'use client'

import React, { create<PERSON>ontext, useContext, useReducer, ReactNode } from 'react'

// Enhanced data structure for advanced workflow
export interface ProjectData {
  // Step 1: Topic & Research
  topic: string
  researched_content?: {
    documentation_links: string[]
    tutorial_steps: string[]
    common_issues: string[]
    best_practices: string[]
    estimated_difficulty: 'beginner' | 'intermediate' | 'advanced'
  }
  
  // Step 2: Audience & Style
  target_audience: 'beginner' | 'intermediate' | 'advanced' | 'mixed'
  video_style: 'explainer' | 'tutorial' | 'review' | 'comparison' | 'walkthrough'
  platforms: ('youtube' | 'tiktok' | 'linkedin' | 'twitter' | 'facebook')[]
  
  // Step 3: Script & Content (Enhanced)
  title: string
  script: string
  script_segments: {
    id: string
    type: 'intro' | 'step' | 'tip' | 'outro'
    content: string
    visual_cues: string[]
    estimated_duration: number
  }[]
  
  // Step 4: Avatar Selection (Existing)
  avatar_id: string
  
  // Step 5: Voice & Branding (Enhanced)
  voice_id: string
  custom_branding?: {
    logo_url?: string
    brand_colors: {
      primary: string
      secondary: string
      accent: string
    }
    watermark_position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  }
  
  // Step 6: Visual Content (New)
  visual_content: {
    type: 'upload' | 'ai_generated'
    screen_recording?: {
      file_url: string
      scenes: {
        start_time: number
        end_time: number
        title: string
        annotations: {
          type: 'highlight' | 'arrow' | 'text' | 'callout'
          position: { x: number; y: number }
          content?: string
        }[]
      }[]
    }
    ai_walkthrough?: {
      website_url: string
      automation_steps: {
        action: 'click' | 'type' | 'hover' | 'scroll' | 'wait'
        selector: string
        value?: string
        description: string
      }[]
    }
  }
  
  // Step 7: Video Settings (Enhanced)
  aspect_ratio: '16:9' | '9:16' | '1:1'
  background?: {
    type: 'color' | 'image'
    value: string
  }
  timeline_settings: {
    avatar_position: 'side' | 'corner' | 'overlay' | 'fullscreen'
    avatar_size: 'small' | 'medium' | 'large'
    transition_style: 'cut' | 'fade' | 'slide' | 'zoom'
    background_music?: string
  }
  
  // Step 8: Publishing (New)
  publishing_config: {
    platforms: {
      platform: string
      title: string
      description: string
      hashtags: string[]
      scheduled_time?: Date
    }[]
    seo_optimization: {
      keywords: string[]
      thumbnail_style: 'auto' | 'custom'
      custom_thumbnail_url?: string
    }
  }
}

type ProjectAction =
  | { type: 'UPDATE_TOPIC'; payload: string }
  | { type: 'UPDATE_RESEARCH'; payload: ProjectData['researched_content'] }
  | { type: 'UPDATE_AUDIENCE_STYLE'; payload: { target_audience: ProjectData['target_audience']; video_style: ProjectData['video_style']; platforms: ProjectData['platforms'] } }
  | { type: 'UPDATE_SCRIPT'; payload: { title?: string; script?: string; script_segments?: ProjectData['script_segments'] } }
  | { type: 'UPDATE_AVATAR'; payload: string }
  | { type: 'UPDATE_VOICE_BRANDING'; payload: { voice_id?: string; custom_branding?: ProjectData['custom_branding'] } }
  | { type: 'UPDATE_VISUAL_CONTENT'; payload: ProjectData['visual_content'] }
  | { type: 'UPDATE_VIDEO_SETTINGS'; payload: { aspect_ratio?: ProjectData['aspect_ratio']; background?: ProjectData['background']; timeline_settings?: ProjectData['timeline_settings'] } }
  | { type: 'UPDATE_PUBLISHING'; payload: ProjectData['publishing_config'] }
  | { type: 'RESET_PROJECT' }
  | { type: 'LOAD_PROJECT'; payload: ProjectData }

interface ProjectWizardState {
  currentStep: number
  projectData: ProjectData
  errors: Record<string, string>
  isLoading: boolean
  isSaving: boolean
}

interface ProjectWizardContextType extends ProjectWizardState {
  dispatch: React.Dispatch<ProjectAction>
  setCurrentStep: (step: number) => void
  validateStep: (step: number) => boolean
  saveProject: () => Promise<void>
  loadProject: (projectId: string) => Promise<void>
  resetProject: () => void
}

const initialProjectData: ProjectData = {
  topic: '',
  target_audience: 'beginner',
  video_style: 'tutorial',
  platforms: ['youtube'],
  title: '',
  script: '',
  script_segments: [],
  avatar_id: '',
  voice_id: '',
  visual_content: {
    type: 'upload'
  },
  aspect_ratio: '16:9',
  timeline_settings: {
    avatar_position: 'corner',
    avatar_size: 'medium',
    transition_style: 'fade'
  },
  publishing_config: {
    platforms: [],
    seo_optimization: {
      keywords: [],
      thumbnail_style: 'auto'
    }
  }
}

const initialState: ProjectWizardState = {
  currentStep: 1,
  projectData: initialProjectData,
  errors: {},
  isLoading: false,
  isSaving: false
}

function projectWizardReducer(state: ProjectWizardState, action: ProjectAction): ProjectWizardState {
  switch (action.type) {
    case 'UPDATE_TOPIC':
      return {
        ...state,
        projectData: { ...state.projectData, topic: action.payload },
        errors: { ...state.errors, topic: '' }
      }
    
    case 'UPDATE_RESEARCH':
      return {
        ...state,
        projectData: { ...state.projectData, researched_content: action.payload }
      }
    
    case 'UPDATE_AUDIENCE_STYLE':
      return {
        ...state,
        projectData: { 
          ...state.projectData, 
          target_audience: action.payload.target_audience,
          video_style: action.payload.video_style,
          platforms: action.payload.platforms
        }
      }
    
    case 'UPDATE_SCRIPT':
      return {
        ...state,
        projectData: { ...state.projectData, ...action.payload },
        errors: { ...state.errors, title: '', script: '' }
      }
    
    case 'UPDATE_AVATAR':
      return {
        ...state,
        projectData: { ...state.projectData, avatar_id: action.payload },
        errors: { ...state.errors, avatar: '' }
      }
    
    case 'UPDATE_VOICE_BRANDING':
      return {
        ...state,
        projectData: { 
          ...state.projectData, 
          voice_id: action.payload.voice_id || state.projectData.voice_id,
          custom_branding: action.payload.custom_branding || state.projectData.custom_branding
        },
        errors: { ...state.errors, voice: '' }
      }
    
    case 'UPDATE_VISUAL_CONTENT':
      return {
        ...state,
        projectData: { ...state.projectData, visual_content: action.payload }
      }
    
    case 'UPDATE_VIDEO_SETTINGS':
      return {
        ...state,
        projectData: { 
          ...state.projectData,
          aspect_ratio: action.payload.aspect_ratio || state.projectData.aspect_ratio,
          background: action.payload.background || state.projectData.background,
          timeline_settings: { ...state.projectData.timeline_settings, ...action.payload.timeline_settings }
        }
      }
    
    case 'UPDATE_PUBLISHING':
      return {
        ...state,
        projectData: { ...state.projectData, publishing_config: action.payload }
      }
    
    case 'RESET_PROJECT':
      return { ...initialState }
    
    case 'LOAD_PROJECT':
      return {
        ...state,
        projectData: action.payload,
        isLoading: false
      }
    
    default:
      return state
  }
}

const ProjectWizardContext = createContext<ProjectWizardContextType | undefined>(undefined)

export function ProjectWizardProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(projectWizardReducer, initialState)

  const setCurrentStep = (step: number) => {
    // We'll handle step changes through navigation logic
  }

  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {}

    switch (step) {
      case 1: // Topic & Research
        if (!state.projectData.topic.trim()) {
          errors.topic = 'Topic is required'
        }
        break
      
      case 2: // Audience & Style
        if (!state.projectData.target_audience) {
          errors.audience = 'Target audience is required'
        }
        if (!state.projectData.video_style) {
          errors.style = 'Video style is required'
        }
        if (state.projectData.platforms.length === 0) {
          errors.platforms = 'At least one platform must be selected'
        }
        break
      
      case 3: // Script & Content
        if (!state.projectData.title.trim()) {
          errors.title = 'Title is required'
        }
        if (!state.projectData.script.trim()) {
          errors.script = 'Script is required'
        }
        if (state.projectData.script.length < 10) {
          errors.script = 'Script must be at least 10 characters'
        }
        break
      
      case 4: // Avatar Selection
        if (!state.projectData.avatar_id) {
          errors.avatar = 'Please select an avatar'
        }
        break
      
      case 5: // Voice & Branding
        if (!state.projectData.voice_id) {
          errors.voice = 'Please select a voice'
        }
        break
      
      case 6: // Visual Content - Optional for now
        // Visual content is optional in current implementation
        // Will be enhanced in future updates
        break
      
      case 7: // Video Settings
        if (!state.projectData.aspect_ratio) {
          errors.settings = 'Please select aspect ratio'
        }
        break
      
      case 8: // Review & Publishing - Optional for now
        // Publishing configuration is optional in current implementation
        // Review step validates core requirements only
        break
    }

    // Update errors in state (we'd need to add an action for this)
    return Object.keys(errors).length === 0
  }

  const saveProject = async (): Promise<void> => {
    // Implement project saving logic
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(state.projectData)
      })
      
      if (!response.ok) {
        throw new Error('Failed to save project')
      }
    } catch (error) {
      console.error('Error saving project:', error)
      throw error
    }
  }

  const loadProject = async (projectId: string): Promise<void> => {
    // Implement project loading logic
    try {
      const response = await fetch(`/api/projects/${projectId}`)
      
      if (!response.ok) {
        throw new Error('Failed to load project')
      }
      
      const projectData = await response.json()
      dispatch({ type: 'LOAD_PROJECT', payload: projectData })
    } catch (error) {
      console.error('Error loading project:', error)
      throw error
    }
  }

  const resetProject = () => {
    dispatch({ type: 'RESET_PROJECT' })
  }

  const contextValue: ProjectWizardContextType = {
    ...state,
    dispatch,
    setCurrentStep,
    validateStep,
    saveProject,
    loadProject,
    resetProject
  }

  return (
    <ProjectWizardContext.Provider value={contextValue}>
      {children}
    </ProjectWizardContext.Provider>
  )
}

export function useProjectWizard() {
  const context = useContext(ProjectWizardContext)
  if (context === undefined) {
    throw new Error('useProjectWizard must be used within a ProjectWizardProvider')
  }
  return context
}