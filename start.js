#!/usr/bin/env node

console.log('🎬 AI Video SaaS - Starting Application...\n');

// Check if Next.js is available
try {
  require.resolve('next');
  console.log('✅ Next.js found - Starting full application...');
  
  // Start Next.js development server
  const { spawn } = require('child_process');
  const nextDev = spawn('npx', ['next', 'dev'], {
    stdio: 'inherit',
    shell: true
  });
  
  nextDev.on('error', (error) => {
    console.log('❌ Next.js failed to start:', error.message);
    console.log('🔄 Falling back to simple server...\n');
    startSimpleServer();
  });
  
} catch (error) {
  console.log('⚠️  Next.js not found - Using simple server...\n');
  startSimpleServer();
}

function startSimpleServer() {
  // Start our simple Node.js server
  require('./server.js');
}