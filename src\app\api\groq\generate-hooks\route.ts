import { NextRequest, NextResponse } from 'next/server'
import { groqClient } from '@/lib/groq'
import { createSupabaseServerClient } from '@/lib/supabase'
import { z } from 'zod'

const generateHooksSchema = z.object({
  topic: z.string().min(1, 'Topic is required'),
  count: z.number().min(1).max(10).optional().default(5)
})

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const { topic, count } = generateHooksSchema.parse(body)

    // Generate hooks using Groq
    const hooks = await groqClient.generateHooks(topic, count)

    return NextResponse.json({
      success: true,
      data: {
        hooks,
        topic,
        count: hooks.length,
        suggestions: [
          'Test different hooks with your audience',
          'A/B test hooks for better performance',
          'Use the most engaging hook in your final script',
          'Combine elements from multiple hooks for maximum impact'
        ]
      }
    })

  } catch (error) {
    console.error('Error generating hooks:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to generate hooks' },
      { status: 500 }
    )
  }
}