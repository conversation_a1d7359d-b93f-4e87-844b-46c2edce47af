import { NextRequest, NextResponse } from 'next/server'
import { groqClient, ScriptEnhancementOptions } from '@/lib/groq'
import { createSupabaseServerClient } from '@/lib/supabase'
import { z } from 'zod'

const generateScriptSchema = z.object({
  topic: z.string().min(1, 'Topic is required'),
  options: z.object({
    tone: z.enum(['professional', 'casual', 'enthusiastic', 'educational', 'sales']).optional(),
    targetLength: z.enum(['short', 'medium', 'long']).optional(),
    industry: z.string().optional(),
    callToAction: z.string().optional(),
    platform: z.enum(['youtube', 'tiktok', 'linkedin', 'instagram', 'general']).optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const { topic, options = {} } = generateScriptSchema.parse(body)

    // Generate script using Groq
    const generatedScript = await groqClient.generateScriptFromTopic(topic, options as ScriptEnhancementOptions)

    // Calculate metrics
    const wordCount = generatedScript.split(' ').length
    const estimatedDuration = Math.ceil(wordCount / 2.5) // Rough estimate: 2.5 words per second

    return NextResponse.json({
      success: true,
      data: {
        script: generatedScript,
        topic,
        options: options || {},
        metrics: {
          word_count: wordCount,
          estimated_duration: estimatedDuration,
          structure_points: [
            'Attention-grabbing hook',
            'Problem identification',
            'Solution presentation',
            'Credibility markers',
            'Clear call-to-action'
          ]
        }
      }
    })

  } catch (error) {
    console.error('Error generating script:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to generate script' },
      { status: 500 }
    )
  }
}