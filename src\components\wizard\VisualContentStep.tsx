'use client'

import { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { LoadingSpinner } from '@/components/ui/loading'
import { useProjectWizard } from '@/context/project-wizard-context'

interface ScreenRecording {
  file_url: string
  duration: number
  file_size: number
  filename: string
  scenes: VideoScene[]
}

interface VideoScene {
  start_time: number
  end_time: number
  title: string
  annotations: Annotation[]
}

interface Annotation {
  type: 'highlight' | 'arrow' | 'text' | 'callout'
  position: { x: number; y: number }
  content?: string
}

interface AutomationStep {
  action: 'click' | 'type' | 'hover' | 'scroll' | 'wait'
  selector: string
  value?: string
  description: string
}

const AUTOMATION_STEP_TEMPLATES = [
  {
    action: 'click' as const,
    description: 'Click on element',
    placeholder: 'button.submit, #login-btn'
  },
  {
    action: 'type' as const,
    description: 'Type text into field',
    placeholder: 'input[name="username"]'
  },
  {
    action: 'hover' as const,
    description: 'Hover over element',
    placeholder: '.menu-item'
  },
  {
    action: 'scroll' as const,
    description: 'Scroll to element',
    placeholder: '#section-bottom'
  },
  {
    action: 'wait' as const,
    description: 'Wait for element to appear',
    placeholder: '.loading-complete'
  }
]

export function VisualContentStep() {
  const { projectData, dispatch } = useProjectWizard()
  const [contentType, setContentType] = useState<'upload' | 'ai_generated'>(
    projectData.visual_content.type || 'upload'
  )
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // AI Walkthrough state
  const [websiteUrl, setWebsiteUrl] = useState(
    projectData.visual_content.ai_walkthrough?.website_url || ''
  )
  const [automationSteps, setAutomationSteps] = useState<AutomationStep[]>(
    projectData.visual_content.ai_walkthrough?.automation_steps || []
  )
  const [isGeneratingWalkthrough, setIsGeneratingWalkthrough] = useState(false)

  // Screen recording state
  const [recordingData, setRecordingData] = useState<ScreenRecording | null>(
    projectData.visual_content.screen_recording || null
  )

  const handleContentTypeChange = (type: 'upload' | 'ai_generated') => {
    setContentType(type)
    
    const updatedVisualContent = {
      type,
      screen_recording: type === 'upload' ? recordingData : undefined,
      ai_walkthrough: type === 'ai_generated' ? {
        website_url: websiteUrl,
        automation_steps: automationSteps
      } : undefined
    }

    dispatch({
      type: 'UPDATE_VISUAL_CONTENT',
      payload: updatedVisualContent
    })
  }

  const handleFileUpload = useCallback(async (file: File) => {
    if (!file) return

    // Validate file type
    const validTypes = ['video/mp4', 'video/webm', 'video/quicktime', 'video/avi']
    if (!validTypes.includes(file.type)) {
      alert('Please upload a video file (MP4, WebM, MOV, or AVI)')
      return
    }

    // Validate file size (max 100MB)
    const maxSize = 100 * 1024 * 1024
    if (file.size > maxSize) {
      alert('Video file must be smaller than 100MB')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // Create video preview
      const videoUrl = URL.createObjectURL(file)
      setPreviewUrl(videoUrl)

      // Get video duration
      const video = document.createElement('video')
      video.preload = 'metadata'
      
      const duration = await new Promise<number>((resolve) => {
        video.onloadedmetadata = () => {
          resolve(video.duration)
        }
        video.src = videoUrl
      })

      // Clean up
      clearInterval(progressInterval)
      setUploadProgress(100)

      // Create recording data
      const newRecording: ScreenRecording = {
        file_url: videoUrl, // In production, this would be uploaded to cloud storage
        duration,
        file_size: file.size,
        filename: file.name,
        scenes: []
      }

      setRecordingData(newRecording)

      // Update project data
      const updatedVisualContent = {
        type: 'upload' as const,
        screen_recording: newRecording,
        ai_walkthrough: undefined
      }

      dispatch({
        type: 'UPDATE_VISUAL_CONTENT',
        payload: updatedVisualContent
      })

      setTimeout(() => {
        setIsUploading(false)
        setUploadProgress(0)
      }, 1000)

    } catch (error) {
      console.error('Error uploading video:', error)
      alert('Failed to upload video. Please try again.')
      setIsUploading(false)
      setUploadProgress(0)
    }
  }, [dispatch])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
  }

  const removeUploadedVideo = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
    }
    setPreviewUrl(null)
    setRecordingData(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }

    const updatedVisualContent = {
      type: contentType,
      screen_recording: undefined,
      ai_walkthrough: contentType === 'ai_generated' ? {
        website_url: websiteUrl,
        automation_steps: automationSteps
      } : undefined
    }

    dispatch({
      type: 'UPDATE_VISUAL_CONTENT',
      payload: updatedVisualContent
    })
  }

  const addAutomationStep = () => {
    const newStep: AutomationStep = {
      action: 'click',
      selector: '',
      value: '',
      description: ''
    }
    const updatedSteps = [...automationSteps, newStep]
    setAutomationSteps(updatedSteps)
    updateAIWalkthrough(websiteUrl, updatedSteps)
  }

  const updateAutomationStep = (index: number, updates: Partial<AutomationStep>) => {
    const updatedSteps = automationSteps.map((step, i) => 
      i === index ? { ...step, ...updates } : step
    )
    setAutomationSteps(updatedSteps)
    updateAIWalkthrough(websiteUrl, updatedSteps)
  }

  const removeAutomationStep = (index: number) => {
    const updatedSteps = automationSteps.filter((_, i) => i !== index)
    setAutomationSteps(updatedSteps)
    updateAIWalkthrough(websiteUrl, updatedSteps)
  }

  const updateAIWalkthrough = (url: string, steps: AutomationStep[]) => {
    const updatedVisualContent = {
      type: 'ai_generated' as const,
      screen_recording: undefined,
      ai_walkthrough: {
        website_url: url,
        automation_steps: steps
      }
    }

    dispatch({
      type: 'UPDATE_VISUAL_CONTENT',
      payload: updatedVisualContent
    })
  }

  const handleWebsiteUrlChange = (url: string) => {
    setWebsiteUrl(url)
    updateAIWalkthrough(url, automationSteps)
  }

  const generateAutomatedWalkthrough = async () => {
    if (!websiteUrl.trim()) {
      alert('Please enter a website URL')
      return
    }

    setIsGeneratingWalkthrough(true)
    try {
      // In a real implementation, this would call an API to analyze the website
      // and generate automation steps
      
      // Simulate AI analysis
      await new Promise(resolve => setTimeout(resolve, 3000))

      const generatedSteps: AutomationStep[] = [
        {
          action: 'click',
          selector: '[data-testid="login-button"]',
          description: 'Click the login button to start authentication',
          value: ''
        },
        {
          action: 'type',
          selector: 'input[name="email"]',
          description: 'Enter email address in the email field',
          value: '<EMAIL>'
        },
        {
          action: 'type',
          selector: 'input[name="password"]',
          description: 'Enter password in the password field',
          value: 'password123'
        },
        {
          action: 'click',
          selector: 'button[type="submit"]',
          description: 'Submit the login form',
          value: ''
        },
        {
          action: 'wait',
          selector: '.dashboard-content',
          description: 'Wait for the dashboard to load',
          value: ''
        }
      ]

      setAutomationSteps(generatedSteps)
      updateAIWalkthrough(websiteUrl, generatedSteps)

    } catch (error) {
      console.error('Error generating walkthrough:', error)
      alert('Failed to generate walkthrough. Please try again.')
    } finally {
      setIsGeneratingWalkthrough(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Visual Content</h3>
        <p className="text-sm text-gray-600 mb-6">
          Add visual elements to your video by uploading screen recordings or creating AI-generated walkthroughs
        </p>
      </div>

      {/* Content Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Choose Visual Content Type</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {/* Upload Option */}
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                contentType === 'upload'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleContentTypeChange('upload')}
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium">Upload Screen Recording</h4>
                  <p className="text-sm text-gray-600">Upload your own video file</p>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                • Supports MP4, WebM, MOV, AVI formats<br/>
                • Maximum file size: 100MB<br/>
                • Recommended: 1080p resolution
              </div>
            </div>

            {/* AI Generation Option */}
            <div
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                contentType === 'ai_generated'
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleContentTypeChange('ai_generated')}
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium">AI Website Walkthrough</h4>
                  <p className="text-sm text-gray-600">Generate automated browser demo</p>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                • Automated website navigation<br/>
                • Customizable interaction steps<br/>
                • Perfect for software demos
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Section */}
      {contentType === 'upload' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Upload Screen Recording</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Upload Area */}
            {!recordingData && !isUploading && (
              <div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Upload Video File</h4>
                  <p className="text-gray-600 mb-4">Drag and drop your screen recording here, or click to browse</p>
                  <Button variant="outline">
                    Choose File
                  </Button>
                </div>
              </div>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Uploading video...</span>
                  <span className="text-sm text-gray-500">{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              </div>
            )}

            {/* Video Preview */}
            {recordingData && previewUrl && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Uploaded Video</h4>
                  <Button variant="ghost" size="sm" onClick={removeUploadedVideo}>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Remove
                  </Button>
                </div>
                
                <div className="bg-gray-100 rounded-lg p-4">
                  <video
                    src={previewUrl}
                    controls
                    className="w-full max-h-64 rounded-md"
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-600">
                      {formatDuration(recordingData.duration)}
                    </div>
                    <div className="text-sm text-gray-600">Duration</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      {formatFileSize(recordingData.file_size)}
                    </div>
                    <div className="text-sm text-gray-600">File Size</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">
                      {recordingData.filename}
                    </div>
                    <div className="text-sm text-gray-600">Filename</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* AI Walkthrough Section */}
      {contentType === 'ai_generated' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">AI Website Walkthrough</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Website URL */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Website URL
              </label>
              <div className="flex gap-3">
                <Input
                  placeholder="https://example.com"
                  value={websiteUrl}
                  onChange={(e) => handleWebsiteUrlChange(e.target.value)}
                  className="flex-1"
                />
                <Button
                  onClick={generateAutomatedWalkthrough}
                  disabled={!websiteUrl.trim() || isGeneratingWalkthrough}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  {isGeneratingWalkthrough ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Auto-Generate
                    </>
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Enter the website URL you want to create a walkthrough for
              </p>
            </div>

            {/* Automation Steps */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="text-sm font-medium text-gray-700">
                  Automation Steps
                </label>
                <Button size="sm" variant="outline" onClick={addAutomationStep}>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Step
                </Button>
              </div>

              {automationSteps.length === 0 ? (
                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No automation steps</h4>
                  <p className="text-gray-600 mb-4">Add steps to create an automated website walkthrough</p>
                  <Button onClick={addAutomationStep}>Add First Step</Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {automationSteps.map((step, index) => (
                    <Card key={index} className="border border-gray-200">
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-4">
                          <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                            {index + 1}
                          </div>
                          
                          <div className="flex-1 space-y-3">
                            <div className="grid gap-3 md:grid-cols-2">
                              {/* Action Type */}
                              <div>
                                <label className="text-xs text-gray-600 mb-1 block">Action</label>
                                <select
                                  value={step.action}
                                  onChange={(e) => updateAutomationStep(index, { 
                                    action: e.target.value as AutomationStep['action'] 
                                  })}
                                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                                >
                                  {AUTOMATION_STEP_TEMPLATES.map((template) => (
                                    <option key={template.action} value={template.action}>
                                      {template.description}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              {/* CSS Selector */}
                              <div>
                                <label className="text-xs text-gray-600 mb-1 block">CSS Selector</label>
                                <Input
                                  placeholder={AUTOMATION_STEP_TEMPLATES.find(t => t.action === step.action)?.placeholder}
                                  value={step.selector}
                                  onChange={(e) => updateAutomationStep(index, { selector: e.target.value })}
                                  className="text-sm"
                                />
                              </div>
                            </div>

                            {/* Value (for type actions) */}
                            {step.action === 'type' && (
                              <div>
                                <label className="text-xs text-gray-600 mb-1 block">Text to Type</label>
                                <Input
                                  placeholder="Enter the text to type..."
                                  value={step.value || ''}
                                  onChange={(e) => updateAutomationStep(index, { value: e.target.value })}
                                  className="text-sm"
                                />
                              </div>
                            )}

                            {/* Description */}
                            <div>
                              <label className="text-xs text-gray-600 mb-1 block">Description</label>
                              <Textarea
                                placeholder="Describe what this step does..."
                                value={step.description}
                                onChange={(e) => updateAutomationStep(index, { description: e.target.value })}
                                rows={2}
                                className="text-sm"
                              />
                            </div>
                          </div>

                          {/* Remove Button */}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeAutomationStep(index)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Walkthrough Preview */}
            {websiteUrl && automationSteps.length > 0 && (
              <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
                <CardHeader>
                  <CardTitle className="text-base flex items-center">
                    <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Walkthrough Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-purple-100 text-purple-800">
                        {websiteUrl}
                      </Badge>
                      <Badge className="bg-blue-100 text-blue-800">
                        {automationSteps.length} steps
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-700">
                      Your AI walkthrough will automatically navigate to <strong>{websiteUrl}</strong> and 
                      perform {automationSteps.length} automated steps to demonstrate the website functionality.
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}