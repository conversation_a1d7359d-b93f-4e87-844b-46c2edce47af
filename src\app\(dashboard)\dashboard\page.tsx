'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'

export default function DashboardPage() {
  const [selectedProject, setSelectedProject] = useState('panda-web')

  const projects = [
    {
      id: 'panda-web',
      name: 'Panda web design &',
      subtitle: 'development',
      team: [
        { id: 1, avatar: 'bg-gradient-to-br from-purple-400 to-pink-400', initials: 'JD' },
        { id: 2, avatar: 'bg-gradient-to-br from-blue-400 to-purple-400', initials: 'SM' },
        { id: 3, avatar: 'bg-gradient-to-br from-green-400 to-blue-400', initials: 'AL' },
      ],
      phases: [
        { name: 'Design Phase', progress: 66, color: 'bg-green-400' },
        { name: 'Development Phase', progress: 33, color: 'bg-blue-400' },
        { name: 'Personal Project', progress: 100, color: 'bg-purple-400' },
      ]
    }
  ]

  const tasks = [
    { id: 1, title: 'Meeting Agenda', completed: true },
    { id: 2, title: 'Project Planning', completed: true },
    { id: 3, title: 'Project Evaluation', completed: false },
  ]

  const teamProjects = [
    {
      id: 1,
      title: 'Website Redesign',
      description: 'Complete overhaul of company website',
      progress: 75,
      dueDate: 'Dec 15',
      team: 4,
      priority: 'high'
    },
    {
      id: 2,
      title: 'Mobile App Development',
      description: 'iOS and Android app development',
      progress: 45,
      dueDate: 'Jan 20',
      team: 6,
      priority: 'medium'
    },
    {
      id: 3,
      title: 'Marketing Campaign',
      description: 'Q1 2024 marketing strategy',
      progress: 20,
      dueDate: 'Feb 10',
      team: 3,
      priority: 'low'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <p className="text-white/70 mt-1">Welcome back! Here's what's happening with your projects.</p>
        </div>
        <Button variant="tech">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Project
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Total Tasks</p>
              <p className="text-2xl font-bold text-white">24</p>
            </div>
            <div className="w-12 h-12 bg-gradient-tech rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Completed</p>
              <p className="text-2xl font-bold text-white">18</p>
            </div>
            <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">In Progress</p>
              <p className="text-2xl font-bold text-white">4</p>
            </div>
            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Overdue</p>
              <p className="text-2xl font-bold text-white">2</p>
            </div>
            <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Project Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Current Project Card */}
          <Card className="bg-project-card p-8 border border-white/10">
            <div className="space-y-6">
              {/* Project Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <Badge variant="tech" className="px-3 py-1">
                  Active Project
                </Badge>
              </div>

              {/* Project Info */}
              <div className="flex items-center space-x-4">
                <div className="flex -space-x-2">
                  {projects[0].team.map((member) => (
                    <div
                      key={member.id}
                      className={`w-8 h-8 ${member.avatar} rounded-full border-2 border-white flex items-center justify-center`}
                    >
                      <span className="text-xs text-white font-medium">{member.initials}</span>
                    </div>
                  ))}
                </div>
                <div>
                  <h3 className="font-semibold text-white">{projects[0].name}</h3>
                  <p className="text-sm text-white/70">{projects[0].subtitle}</p>
                </div>
              </div>

              {/* Project Phases */}
              <div className="space-y-4">
                {projects[0].phases.map((phase, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-white/70">{phase.name}:</span>
                    <div className="flex space-x-1">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-2 h-2 rounded-full ${
                            i < Math.ceil((phase.progress / 100) * 3) ? phase.color : 'bg-white/30'
                          }`}
                        ></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Team Projects */}
          <Card className="bg-task-card p-6 border border-white/10">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">Team Projects</h3>
              <Button variant="ghost" className="text-white/70 hover:text-white">
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {teamProjects.map((project) => (
                <div key={project.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-white">{project.title}</h4>
                      <Badge
                        className={`text-xs ${
                          project.priority === 'high' ? 'bg-red-500/20 text-red-300' :
                          project.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-green-500/20 text-green-300'
                        } border-0`}
                      >
                        {project.priority}
                      </Badge>
                    </div>
                    <span className="text-sm text-white/70">{project.dueDate}</span>
                  </div>
                  <p className="text-sm text-white/60 mb-3">{project.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Progress value={project.progress} className="w-24" />
                      <span className="text-xs text-white/70">{project.progress}%</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-white/70">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      {project.team}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Right Column - Task List */}
        <div className="space-y-6">
          <Card className="bg-task-card p-6 border border-white/10">
            <h4 className="font-medium text-white/90 mb-4">Today's Tasks</h4>
            <div className="space-y-3">
              {tasks.map((task) => (
                <div key={task.id} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                  <div className={`w-4 h-4 rounded-sm flex items-center justify-center ${
                    task.completed ? 'bg-green-400' : 'border-2 border-white/30'
                  }`}>
                    {task.completed && (
                      <svg className="w-2 h-2 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    )}
                  </div>
                  <span className={`text-sm ${task.completed ? 'text-white/80' : 'text-white/60'}`}>
                    {task.title}
                  </span>
                </div>
              ))}
            </div>
            <Button className="w-full mt-4 bg-white/10 text-white border border-white/20 hover:bg-white/20">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Task
            </Button>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-task-card p-6 border border-white/10">
            <h4 className="font-medium text-white/90 mb-4">Quick Actions</h4>
            <div className="space-y-3">
              <Button className="w-full justify-start bg-white/5 text-white border border-white/10 hover:bg-white/10">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Create New Task
              </Button>
              <Button className="w-full justify-start bg-white/5 text-white border border-white/10 hover:bg-white/10">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                New Project
              </Button>
              <Button className="w-full justify-start bg-white/5 text-white border border-white/10 hover:bg-white/10">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Invite Team
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}