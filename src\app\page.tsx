import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-hero">
      {/* Navigation Header */}
      <nav className="glass-nav sticky top-0 z-50 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center shadow-premium">
                <div className="w-5 h-5 bg-gradient-premium rounded-md"></div>
              </div>
              <span className="text-2xl font-bold text-white">HeyGen Video</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-white/90 hover:text-white transition-colors font-medium">Solutions</a>
              <a href="#resources" className="text-white/90 hover:text-white transition-colors font-medium">Resources</a>
              <a href="#developers" className="text-white/90 hover:text-white transition-colors font-medium">Developers</a>
              <a href="#pricing" className="text-white/90 hover:text-white transition-colors font-medium">Pricing</a>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link href="/login">
                <Button variant="ghost" className="text-white hover:bg-white/10 font-medium">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative px-6 py-20 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="text-left animate-fade-in">
              <h1 className="text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
                The Go-To Hub
                <br />
                for All Your <span className="text-gradient">To-Dos</span>
              </h1>
              <p className="text-xl text-white/90 mb-8 leading-relaxed max-w-lg">
                Your essential tool for streamlining task management and
                achieving greater efficiency in your daily routines.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link href="/signup">
                  <Button size="lg" className="btn-premium text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-floating">
                    Start now—it's free!
                  </Button>
                </Link>
              </div>
              <div className="flex items-center space-x-6 text-white/80">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm">No credit card required</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm">5 free video credits</span>
                </div>
              </div>
            </div>
            
            {/* Right Content - Task Management Demo */}
            <div className="relative animate-float">
              <Card className="bg-task-card p-8 shadow-floating-lg card-3d border border-white/10">
                <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <Badge className="bg-gradient-premium text-white border-0 px-3 py-1">
                      Introducing our
                    </Badge>
                  </div>

                  {/* AI Powered Task Automation Badge */}
                  <div className="text-center">
                    <Badge className="bg-gradient-premium text-white border-0 px-4 py-2 text-sm font-medium">
                      AI Powered Task
                      <br />
                      Automation
                    </Badge>
                  </div>
                  
                  {/* How Our Users Enhance Their Productivity */}
                  <div className="text-center mb-6">
                    <h3 className="text-lg font-semibold text-white mb-2">How Our Users</h3>
                    <h3 className="text-lg font-semibold text-white mb-4">Enhance Their</h3>
                    <h3 className="text-lg font-semibold text-gradient">Productivity</h3>
                  </div>

                  {/* Project Card */}
                  <div className="bg-project-card backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="flex -space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full border-2 border-white"></div>
                        <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-400 rounded-full border-2 border-white"></div>
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">Panda web design &</h3>
                        <p className="text-sm text-white/70">development</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white/70">Design Phase:</span>
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white/70">Development Phase:</span>
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                          <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                        <span className="text-sm text-white/70">Personal Project:</span>
                      <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Task List */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-white/90 mb-3">Team Projects</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                        <div className="w-4 h-4 bg-green-400 rounded-sm flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" viewBox="0 0 8 8" fill="currentColor">
                            <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                          </svg>
                        </div>
                        <span className="text-sm text-white/80">Meeting Agenda</span>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                        <div className="w-4 h-4 bg-green-400 rounded-sm flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" viewBox="0 0 8 8" fill="currentColor">
                            <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                          </svg>
                        </div>
                        <span className="text-sm text-white/80">Project Planning</span>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                        <div className="w-4 h-4 border-2 border-white/30 rounded-sm"></div>
                        <span className="text-sm text-white/60">Project Evaluation</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Unlock Your Endless Possibilities Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Unlock Your
              <br />
              <span className="text-gradient">Endless Possibilities</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Discover powerful features that transform how you manage tasks and boost productivity
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature Card 1 */}
            <Card className="bg-task-card p-8 card-float group border border-white/10">
              <div className="w-14 h-14 bg-gradient-premium rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Smart Task Organization</h3>
              <p className="text-white/70 leading-relaxed">
                Organize your tasks with intelligent categorization and priority management for maximum efficiency.
              </p>
            </Card>
            
            {/* Feature Card 2 */}
            <Card className="bg-task-card p-8 card-float group border border-white/10">
              <div className="w-14 h-14 bg-gradient-premium rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Team Collaboration</h3>
              <p className="text-white/70 leading-relaxed">
                Work seamlessly with your team through shared projects, real-time updates, and collaborative workflows.
              </p>
            </Card>
            
            {/* Feature Card 3 */}
            <Card className="bg-task-card p-8 card-float group border border-white/10">
              <div className="w-14 h-14 bg-gradient-premium rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Progress Analytics</h3>
              <p className="text-white/70 leading-relaxed">
                Track your productivity with detailed analytics and insights to optimize your workflow and performance.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Seamless Integration Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Seamless
              <br />
              <span className="text-gradient">Integration with All Tools</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Connect with your favorite productivity tools and streamline your entire workflow
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Integration Cards */}
            <Card className="bg-task-card p-6 card-float text-center border border-white/10">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Project Management</h3>
              <p className="text-white/60 text-sm">Slack, Trello, Asana, and more</p>
            </Card>

            <Card className="bg-task-card p-6 card-float text-center border border-white/10">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Communication</h3>
              <p className="text-white/60 text-sm">Email, Teams, Discord integration</p>
            </Card>

            <Card className="bg-task-card p-6 card-float text-center border border-white/10">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Analytics</h3>
              <p className="text-white/60 text-sm">Performance tracking and insights</p>
            </Card>

            <Card className="bg-task-card p-6 card-float text-center border border-white/10">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Automation</h3>
              <p className="text-white/60 text-sm">Zapier, IFTTT, and custom APIs</p>
            </Card>
          </div>
        </div>
      </section>

      {/* Master Your Workflow Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="bg-task-card p-16 shadow-floating-lg border border-white/10">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Master Your Workflow
            </h2>
            <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
              Take control of your productivity with intelligent task management and seamless collaboration tools
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/signup">
                <Button size="lg" className="btn-premium text-white px-8 py-4 text-lg font-semibold rounded-xl">
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/demo">
                <Button size="lg" className="btn-glass text-white px-8 py-4 text-lg font-semibold rounded-xl">
                  Watch Demo
                </Button>
              </Link>
            </div>
            <div className="flex items-center justify-center space-x-8 text-white/60">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                <span>Cancel anytime</span>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Trust & Statistics Section */}
      <section className="px-6 py-16 relative">
        <div className="max-w-7xl mx-auto">
          {/* Statistics */}
          <div className="grid md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-gradient mb-2">50M+</div>
              <div className="text-white/80">Tasks Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-gradient mb-2">2M+</div>
              <div className="text-white/80">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-gradient mb-2">98%</div>
              <div className="text-white/80">Productivity Boost</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-gradient mb-2">24/7</div>
              <div className="text-white/80">Smart Assistance</div>
            </div>
          </div>

          {/* Customer Logos */}
          <div className="text-center mb-8">
            <h3 className="text-xl font-semibold text-white mb-8">Trusted by productivity leaders</h3>
            <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center opacity-60">
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">ProductiveCorp</span>
              </div>
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">TaskMaster</span>
              </div>
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">WorkflowPro</span>
              </div>
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">TeamSync</span>
              </div>
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">EfficiencyHub</span>
              </div>
              <div className="flex items-center justify-center h-12 bg-white/10 rounded-lg backdrop-blur-sm">
                <span className="text-white font-semibold text-sm">SmartTasks</span>
              </div>
            </div>
          </div>

          {/* Security & Certifications */}
          <div className="flex flex-wrap justify-center items-center gap-8 text-white/60">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
              </svg>
              <span className="text-sm">SOC 2 Certified</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd"/>
              </svg>
              <span className="text-sm">256-bit SSL</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd"/>
              </svg>
              <span className="text-sm">GDPR Compliant</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
              </svg>
              <span className="text-sm">99.9% Uptime</span>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Success Stories Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Success Stories from
              <br />
              <span className="text-gradient">Productivity Champions</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              See how teams and individuals are transforming their workflow with smart task management
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="bg-task-card p-8 card-float border border-white/10">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl text-white">👩‍💼</span>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white">Sarah Chen</h4>
                  <p className="text-white/60 text-sm">Project Manager</p>
                </div>
              </div>
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-white/80 mb-4">
                "This platform transformed our team's productivity. We can now manage complex projects with ease and never miss a deadline."
              </p>
              <div className="text-white/60 text-sm">
                <strong>Results:</strong> 85% faster project completion, 200+ tasks managed daily
              </div>
            </Card>

            {/* Testimonial 2 */}
            <Card className="bg-task-card p-8 card-float border border-white/10">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl text-white">👨‍🎓</span>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white">Dr. Michael Torres</h4>
                  <p className="text-white/60 text-sm">Research Director</p>
                </div>
              </div>
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-white/80 mb-4">
                "Managing research projects has never been easier. The intelligent task organization helps our team stay focused and productive."
              </p>
              <div className="text-white/60 text-sm">
                <strong>Results:</strong> 60% faster research cycles, 95% team satisfaction
              </div>
            </Card>

            {/* Testimonial 3 */}
            <Card className="bg-task-card p-8 card-float border border-white/10">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-xl text-white">👨‍💻</span>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white">Alex Rivera</h4>
                  <p className="text-white/60 text-sm">Startup Founder</p>
                </div>
              </div>
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-white/80 mb-4">
                "The automation features are incredible. I can focus on strategy while the platform handles task organization and team coordination."
              </p>
              <div className="text-white/60 text-sm">
                <strong>Results:</strong> 90% faster execution, 500+ automated workflows
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Video Showcase Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              See AI Avatars
              <br />
              <span className="text-gradient">in Action</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Experience the quality and realism of our AI-generated videos
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Video Preview 1 */}
            <Card className="glass-card overflow-hidden card-float group">
              <div className="relative">
                <div className="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center cursor-pointer group-hover:scale-105 transition-transform">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                    </svg>
                  </div>
                </div>
                <div className="absolute top-2 left-2">
                  <Badge className="bg-black/50 text-white border-0">Marketing</Badge>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/75 text-white px-2 py-1 rounded text-xs">
                  0:45
                </div>
                <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-white mb-2">Product Launch Video</h3>
                <p className="text-sm text-white/70 mb-3">Professional marketing presentation with Sarah avatar</p>
                <div className="flex items-center justify-between text-xs text-white/60">
                  <span>4K Quality</span>
                  <span>English • Female</span>
                </div>
              </div>
            </Card>

            {/* Video Preview 2 */}
            <Card className="glass-card overflow-hidden card-float group">
              <div className="relative">
                <div className="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center cursor-pointer group-hover:scale-105 transition-transform">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                    </svg>
                  </div>
                </div>
                <div className="absolute top-2 left-2">
                  <Badge className="bg-black/50 text-white border-0">Training</Badge>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/75 text-white px-2 py-1 rounded text-xs">
                  2:15
                </div>
                <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-white mb-2">Employee Training</h3>
                <p className="text-sm text-white/70 mb-3">Corporate training module with David avatar</p>
                <div className="flex items-center justify-between text-xs text-white/60">
                  <span>1080p Quality</span>
                  <span>English • Male</span>
                </div>
              </div>
            </Card>

            {/* Video Preview 3 */}
            <Card className="glass-card overflow-hidden card-float group">
              <div className="relative">
                <div className="w-full h-48 bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center cursor-pointer group-hover:scale-105 transition-transform">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                    </svg>
                  </div>
                </div>
                <div className="absolute top-2 left-2">
                  <Badge className="bg-black/50 text-white border-0">Social Media</Badge>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/75 text-white px-2 py-1 rounded text-xs">
                  0:30
                </div>
                <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-medium text-white mb-2">Social Media Content</h3>
                <p className="text-sm text-white/70 mb-3">Engaging social post with Emma avatar</p>
                <div className="flex items-center justify-between text-xs text-white/60">
                  <span>1080p Quality</span>
                  <span>English • Female</span>
                </div>
              </div>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button className="btn-premium text-white px-8 py-4 text-lg font-semibold rounded-xl">
              Try the Interactive Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Unlock Your Endless Possibilities Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Unlock Your
              <br />
              <span className="text-gradient">Endless Possibilities</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Choose the plan that fits your needs and start achieving more today
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Pricing Card 1 */}
            <Card className="glass-card p-8 card-float">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-2">Starter</h3>
                <div className="text-4xl font-bold text-gradient mb-6">$29</div>
                <p className="text-white/70 mb-8">Perfect for individuals getting started</p>
                <ul className="space-y-4 text-left mb-8">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">100 video credits</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">HD video quality</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">Basic avatars & voices</span>
                  </li>
                </ul>
                <Button className="w-full btn-glass text-white">Get Started</Button>
              </div>
            </Card>

            {/* Pricing Card 2 - Popular */}
            <Card className="glass-card p-8 card-float border-2 border-white/30 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-gradient-premium text-white border-0 px-4 py-1">
                  Most Popular
                </Badge>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-2">Professional</h3>
                <div className="text-4xl font-bold text-gradient mb-6">$79</div>
                <p className="text-white/70 mb-8">Best for growing teams and businesses</p>
                <ul className="space-y-4 text-left mb-8">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">500 video credits</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">4K video quality</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">Premium avatars & voices</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">API access</span>
                  </li>
                </ul>
                <Button className="w-full btn-premium text-white">Get Started</Button>
              </div>
            </Card>

            {/* Pricing Card 3 */}
            <Card className="glass-card p-8 card-float">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-2">Enterprise</h3>
                <div className="text-4xl font-bold text-gradient mb-6">$199</div>
                <p className="text-white/70 mb-8">For large organizations and enterprises</p>
                <ul className="space-y-4 text-left mb-8">
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">2000 video credits</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">Priority support</span>
                  </li>
                  <li className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" viewBox="0 0 8 8" fill="currentColor">
                        <path d="M6.5 1L3 4.5 1.5 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
                      </svg>
                    </div>
                    <span className="text-white/80">White-label options</span>
                  </li>
                </ul>
                <Button className="w-full btn-glass text-white">Contact Sales</Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Seamless Integration Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Seamless
              <br />
              <span className="text-gradient">Integration with All Tools</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Export your videos to multiple platforms and integrate with your existing tools
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Integration Cards */}
            <Card className="glass-card p-6 card-float text-center">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Social Media</h3>
              <p className="text-white/60 text-sm">Share directly to YouTube, TikTok, LinkedIn</p>
            </Card>

            <Card className="glass-card p-6 card-float text-center">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Video Hosting</h3>
              <p className="text-white/60 text-sm">Vimeo, Wistia, and custom hosting</p>
            </Card>

            <Card className="glass-card p-6 card-float text-center">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Analytics</h3>
              <p className="text-white/60 text-sm">Video performance tracking and insights</p>
            </Card>

            <Card className="glass-card p-6 card-float text-center">
              <div className="w-16 h-16 bg-gradient-premium rounded-xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Marketing</h3>
              <p className="text-white/60 text-sm">HubSpot, Mailchimp, and CRM tools</p>
            </Card>
          </div>
        </div>
      </section>

      {/* Master Your Workflow Section */}
      <section className="px-6 py-20 relative">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="glass-card p-16 shadow-floating-lg">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Start Creating Videos Today
            </h2>
            <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
              Join thousands of creators who have transformed their video content with AI avatars
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/signup">
                <Button size="lg" className="btn-premium text-white px-8 py-4 text-lg font-semibold rounded-xl">
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/demo">
                <Button size="lg" className="btn-glass text-white px-8 py-4 text-lg font-semibold rounded-xl">
                  Watch Demo
                </Button>
              </Link>
            </div>
            <div className="flex items-center justify-center space-x-8 text-white/60">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                <span>Cancel anytime</span>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-12 border-t border-white/10">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-white rounded-xl flex items-center justify-center">
                  <div className="w-4 h-4 bg-gradient-premium rounded-md"></div>
                </div>
                <span className="text-xl font-bold text-white">HeyGen Video</span>
              </div>
              <p className="text-white/70">
                The future of video creation is here.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-2 text-white/70">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Templates</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-white/70">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Resources</h4>
              <ul className="space-y-2 text-white/70">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API Docs</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-12 pt-8 text-center text-white/60">
            <p>&copy; 2024 HeyGen Video. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
