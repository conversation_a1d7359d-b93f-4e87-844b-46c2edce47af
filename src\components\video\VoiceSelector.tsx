'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { LoadingSkeleton } from '@/components/ui/loading'

interface Voice {
  voice_id: string
  language: string
  gender: 'male' | 'female'
  preview_audio_url: string
  language_code: string
  accent?: string
  age_range?: string
  style?: string
}

interface VoiceSelectorProps {
  selectedVoiceId: string
  onSelect: (voiceId: string) => void
  error?: string
}

export function VoiceSelector({ selectedVoiceId, onSelect, error }: VoiceSelectorProps) {
  const [voices, setVoices] = useState<Voice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [languageFilter, setLanguageFilter] = useState<string>('all')
  const [genderFilter, setGenderFilter] = useState<'all' | 'male' | 'female'>('all')
  const [playingVoice, setPlayingVoice] = useState<string | null>(null)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    loadVoices()
    // Load favorites from localStorage
    const savedFavorites = localStorage.getItem('favorite-voices')
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)))
    }

    // Cleanup audio on unmount
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
    }
  }, [])

  const loadVoices = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/heygen/voices')
      const result = await response.json()

      if (result.success) {
        setVoices(result.data)
      } else {
        throw new Error(result.error || 'Failed to load voices')
      }
    } catch (error) {
      console.error('Failed to load voices:', error)
      // Fallback with sample data for development
      setVoices([
        {
          voice_id: 'voice-1',
          language: 'English',
          gender: 'female',
          preview_audio_url: '/api/placeholder/audio/female-english.mp3',
          language_code: 'en-US',
          accent: 'American',
          age_range: '25-35',
          style: 'professional'
        },
        {
          voice_id: 'voice-2',
          language: 'English',
          gender: 'male',
          preview_audio_url: '/api/placeholder/audio/male-english.mp3',
          language_code: 'en-US',
          accent: 'American',
          age_range: '30-40',
          style: 'business'
        },
        {
          voice_id: 'voice-3',
          language: 'English',
          gender: 'female',
          preview_audio_url: '/api/placeholder/audio/female-british.mp3',
          language_code: 'en-GB',
          accent: 'British',
          age_range: '20-30',
          style: 'elegant'
        },
        {
          voice_id: 'voice-4',
          language: 'Spanish',
          gender: 'male',
          preview_audio_url: '/api/placeholder/audio/male-spanish.mp3',
          language_code: 'es-ES',
          accent: 'Spanish',
          age_range: '35-45',
          style: 'warm'
        },
        {
          voice_id: 'voice-5',
          language: 'French',
          gender: 'female',
          preview_audio_url: '/api/placeholder/audio/female-french.mp3',
          language_code: 'fr-FR',
          accent: 'Parisian',
          age_range: '25-35',
          style: 'sophisticated'
        },
        {
          voice_id: 'voice-6',
          language: 'German',
          gender: 'male',
          preview_audio_url: '/api/placeholder/audio/male-german.mp3',
          language_code: 'de-DE',
          accent: 'Standard',
          age_range: '30-40',
          style: 'authoritative'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const playVoicePreview = async (voice: Voice) => {
    try {
      // Stop current audio if playing
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }

      if (playingVoice === voice.voice_id) {
        setPlayingVoice(null)
        return
      }

      setPlayingVoice(voice.voice_id)

      // Create new audio element
      audioRef.current = new Audio(voice.preview_audio_url)
      
      audioRef.current.addEventListener('ended', () => {
        setPlayingVoice(null)
      })

      audioRef.current.addEventListener('error', () => {
        console.error('Failed to play audio preview')
        setPlayingVoice(null)
        // Fallback: use text-to-speech if available
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(
            'Hello, this is a preview of this voice. Thank you for listening.'
          )
          utterance.lang = voice.language_code
          speechSynthesis.speak(utterance)
        }
      })

      await audioRef.current.play()
    } catch (error) {
      console.error('Error playing voice preview:', error)
      setPlayingVoice(null)
    }
  }

  const toggleFavorite = (voiceId: string) => {
    const newFavorites = new Set(favorites)
    if (newFavorites.has(voiceId)) {
      newFavorites.delete(voiceId)
    } else {
      newFavorites.add(voiceId)
    }
    setFavorites(newFavorites)
    localStorage.setItem('favorite-voices', JSON.stringify(Array.from(newFavorites)))
  }

  const languages = Array.from(new Set(voices.map(voice => voice.language)))

  const filteredVoices = voices.filter(voice => {
    const matchesSearch = voice.language.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (voice.accent && voice.accent.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (voice.style && voice.style.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesLanguage = languageFilter === 'all' || voice.language === languageFilter
    const matchesGender = genderFilter === 'all' || voice.gender === genderFilter
    return matchesSearch && matchesLanguage && matchesGender
  })

  // Sort voices: favorites first, then by language and gender
  const sortedVoices = [...filteredVoices].sort((a, b) => {
    const aFavorite = favorites.has(a.voice_id)
    const bFavorite = favorites.has(b.voice_id)
    if (aFavorite && !bFavorite) return -1
    if (!aFavorite && bFavorite) return 1
    if (a.language !== b.language) return a.language.localeCompare(b.language)
    return a.gender.localeCompare(b.gender)
  })

  const selectedVoice = voices.find(voice => voice.voice_id === selectedVoiceId)

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Select Voice</h3>
          <LoadingSkeleton className="h-10 w-64 mb-4" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="space-y-2">
              <LoadingSkeleton className="h-20 w-full" />
              <LoadingSkeleton className="h-4 w-3/4" />
              <LoadingSkeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Select Voice</h3>
        <p className="text-sm text-gray-600 mb-6">
          Choose the perfect voice that matches your content and audience
        </p>
      </div>

      {/* Selected Voice Display */}
      {selectedVoice && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-green-900">Selected Voice</h4>
                  <p className="text-green-700">{selectedVoice.language} • {selectedVoice.gender}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    {selectedVoice.accent && (
                      <Badge variant="outline" className="text-xs">
                        {selectedVoice.accent}
                      </Badge>
                    )}
                    {selectedVoice.style && (
                      <Badge variant="outline" className="text-xs">
                        {selectedVoice.style}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={() => playVoicePreview(selectedVoice)}
                disabled={playingVoice !== null && playingVoice !== selectedVoice.voice_id}
              >
                {playingVoice === selectedVoice.voice_id ? (
                  <>
                    <div className="w-4 h-4 mr-2">
                      <div className="flex space-x-1">
                        <div className="w-1 bg-current animate-pulse"></div>
                        <div className="w-1 bg-current animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-1 bg-current animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                    Playing...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                    </svg>
                    Preview
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search by language, accent, or style..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <select
            value={languageFilter}
            onChange={(e) => setLanguageFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Languages</option>
            {languages.map(language => (
              <option key={language} value={language}>{language}</option>
            ))}
          </select>
          <Button
            variant={genderFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('all')}
          >
            All
          </Button>
          <Button
            variant={genderFilter === 'male' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('male')}
          >
            Male
          </Button>
          <Button
            variant={genderFilter === 'female' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('female')}
          >
            Female
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {error}
        </div>
      )}

      {/* Voice Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sortedVoices.map((voice) => (
          <Card
            key={voice.voice_id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedVoiceId === voice.voice_id
                ? 'ring-2 ring-blue-500 border-blue-500 shadow-lg'
                : 'hover:border-gray-300'
            }`}
            onClick={() => onSelect(voice.voice_id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    voice.gender === 'male' ? 'bg-blue-100 text-blue-600' : 'bg-pink-100 text-pink-600'
                  }`}>
                    {voice.gender === 'male' ? '♂' : '♀'}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{voice.language}</p>
                    <p className="text-xs text-gray-500">{voice.accent}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1">
                  {/* Favorite Button */}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleFavorite(voice.voice_id)
                    }}
                    className="w-8 h-8 p-0"
                  >
                    <svg 
                      className="w-4 h-4" 
                      fill={favorites.has(voice.voice_id) ? 'currentColor' : 'none'} 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </Button>

                  {/* Selection Indicator */}
                  {selectedVoiceId === voice.voice_id && (
                    <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>

              {/* Voice Details */}
              <div className="space-y-2 mb-3">
                <div className="flex flex-wrap gap-1">
                  <Badge variant="outline" className="text-xs">
                    {voice.gender}
                  </Badge>
                  {voice.style && (
                    <Badge variant="outline" className="text-xs">
                      {voice.style}
                    </Badge>
                  )}
                  {voice.age_range && (
                    <Badge variant="outline" className="text-xs">
                      {voice.age_range}
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-600">
                  Language Code: {voice.language_code}
                </p>
              </div>

              {/* Play Button */}
              <Button
                size="sm"
                variant={playingVoice === voice.voice_id ? 'default' : 'outline'}
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation()
                  playVoicePreview(voice)
                }}
                disabled={playingVoice !== null && playingVoice !== voice.voice_id}
              >
                {playingVoice === voice.voice_id ? (
                  <>
                    <div className="w-4 h-4 mr-2 flex space-x-1">
                      <div className="w-1 bg-current animate-pulse rounded-full"></div>
                      <div className="w-1 bg-current animate-pulse rounded-full" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-1 bg-current animate-pulse rounded-full" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    Playing...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                    </svg>
                    Preview Voice
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* No Results */}
      {sortedVoices.length === 0 && (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No voices found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  )
}