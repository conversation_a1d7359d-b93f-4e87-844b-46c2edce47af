import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.APP_URL || 'https://heygen-video-saas.vercel.app'),
  title: {
    default: "HeyGen Video SaaS - AI-Powered Video Generation Platform | Create Professional Avatar Videos",
    template: "%s | HeyGen Video SaaS - AI Video Generation"
  },
  description: "Transform your content creation with our advanced AI video generation platform. Create professional avatar videos in minutes using HeyGen technology. Boost productivity with intelligent task management, team collaboration, and seamless workflow integration.",
  keywords: [
    "AI video generation",
    "avatar videos",
    "HeyGen technology",
    "video creation platform",
    "AI-powered videos",
    "professional video content",
    "automated video production",
    "digital avatar creation",
    "video marketing tools",
    "content automation",
    "productivity platform",
    "task management",
    "team collaboration",
    "workflow optimization",
    "business automation",
    "SaaS platform",
    "video as a service",
    "AI presenter",
    "virtual spokesperson",
    "content scalability"
  ],
  authors: [
    { name: "HeyGen Video SaaS Team", url: "https://heygen-video-saas.vercel.app" }
  ],
  creator: "HeyGen Video SaaS",
  publisher: "HeyGen Video SaaS Platform",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    alternateLocale: ["en_GB", "en_CA", "en_AU"],
    url: "/",
    siteName: "HeyGen Video SaaS - AI Video Generation Platform",
    title: "HeyGen Video SaaS - Transform Your Content with AI-Powered Video Generation",
    description: "Create professional AI avatar videos in minutes. Streamline your workflow with intelligent task management, team collaboration, and seamless integration. Start your free trial today.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "HeyGen Video SaaS - AI-Powered Video Generation Platform",
      },
      {
        url: "/og-image-square.jpg",
        width: 1200,
        height: 1200,
        alt: "HeyGen Video SaaS - Create Professional Avatar Videos",
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@heygenvideosaas",
    creator: "@heygenvideosaas",
    title: "HeyGen Video SaaS - AI-Powered Video Generation Platform",
    description: "Create professional AI avatar videos in minutes. Boost productivity with intelligent automation. Start your free trial today! 🚀",
    images: ["/twitter-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    noarchive: false,
    nosnippet: false,
    noimageindex: false,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: "/",
    languages: {
      'en-US': '/en-US',
      'en-GB': '/en-GB',
      'en-CA': '/en-CA',
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
    other: {
      me: ["mailto:<EMAIL>"],
    },
  },
  category: "Technology",
  classification: "Business Software",
  referrer: "origin-when-cross-origin",
  colorScheme: "dark light",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#8B5CF6" },
    { media: "(prefers-color-scheme: dark)", color: "#A78BFA" },
  ],
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
    viewportFit: "cover",
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "HeyGen Video SaaS",
  },
  appLinks: {
    web: {
      url: "https://heygen-video-saas.vercel.app",
      should_fallback: true,
    },
  },
  bookmarks: ["https://heygen-video-saas.vercel.app"],
  other: {
    "application-name": "HeyGen Video SaaS",
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "black-translucent",
    "apple-mobile-web-app-title": "HeyGen Video SaaS",
    "application-TileColor": "#8B5CF6",
    "msapplication-TileImage": "/mstile-144x144.png",
    "msapplication-config": "/browserconfig.xml",
    "theme-color": "#8B5CF6",
    "format-detection": "telephone=no",
    "HandheldFriendly": "true",
    "MobileOptimized": "width",
    "apple-touch-fullscreen": "yes",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html 
      lang="en" 
      className={inter.variable}
      suppressHydrationWarning
    >
      <head>
        {/* Structured Data - Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "HeyGen Video SaaS",
              "description": "AI-powered video generation platform for creating professional avatar videos",
              "url": "https://heygen-video-saas.vercel.app",
              "logo": "https://heygen-video-saas.vercel.app/logo.png",
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "******-0123",
                "contactType": "customer service",
                "availableLanguage": ["English"]
              },
              "sameAs": [
                "https://twitter.com/heygenvideosaas",
                "https://linkedin.com/company/heygen-video-saas",
                "https://github.com/heygen-video-saas"
              ]
            })
          }}
        />
        
        {/* Structured Data - SoftwareApplication */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "HeyGen Video SaaS",
              "description": "AI-powered video generation platform for creating professional avatar videos with advanced task management and team collaboration features",
              "applicationCategory": "BusinessApplication",
              "operatingSystem": "Web Browser",
              "offers": {
                "@type": "Offer",
                "price": "29",
                "priceCurrency": "USD",
                "priceValidUntil": "2025-12-31",
                "availability": "https://schema.org/InStock"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "234",
                "bestRating": "5",
                "worstRating": "1"
              },
              "featureList": [
                "AI Avatar Video Generation",
                "Professional Video Creation",
                "Task Management",
                "Team Collaboration",
                "Workflow Automation",
                "Multi-language Support",
                "Real-time Collaboration",
                "Advanced Analytics"
              ]
            })
          }}
        />

        {/* Structured Data - WebApplication */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "HeyGen Video SaaS Platform",
              "description": "Transform your content creation with AI-powered video generation. Create professional avatar videos, manage tasks, and collaborate with your team.",
              "url": "https://heygen-video-saas.vercel.app",
              "browserRequirements": "Requires JavaScript. Requires HTML5.",
              "screenshot": "https://heygen-video-saas.vercel.app/screenshot.png",
              "applicationCategory": "ProductivityApplication",
              "operatingSystem": "Web Browser, Windows, macOS, Linux, iOS, Android",
              "permissions": "camera, microphone",
              "offers": {
                "@type": "Offer",
                "category": "Free Trial",
                "price": "0",
                "priceCurrency": "USD",
                "eligibleQuantity": {
                  "@type": "QuantitativeValue",
                  "value": "14",
                  "unitText": "DAY"
                }
              }
            })
          }}
        />

        {/* Performance & Optimization */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="dns-prefetch" href="https://api.heygen.com" />
        <link rel="dns-prefetch" href="https://cdn.tailwindcss.com" />
        
        {/* Security Headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
        
        {/* PWA Support */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="HeyGen Video SaaS" />
        
        {/* Favicons */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#8B5CF6" />
      </head>
      <body 
        className="font-sans antialiased bg-background text-foreground overflow-x-hidden"
        suppressHydrationWarning
      >
        <div className="relative flex min-h-screen flex-col">
          {children}
        </div>
        
        {/* Analytics placeholder - replace with actual analytics */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Google Analytics placeholder
              // gtag('config', 'GA_MEASUREMENT_ID');
              
              // Performance monitoring
              if ('performance' in window) {
                window.addEventListener('load', function() {
                  setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart);
                  }, 0);
                });
              }
            `
          }}
        />
      </body>
    </html>
  );
}
