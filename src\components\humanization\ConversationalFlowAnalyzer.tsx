'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ConversationalFlow } from '@/types/humanization'

interface ConversationalFlowAnalyzerProps {
  script: string
  onAnalysisComplete: (analysis: ConversationalFlow) => void
  onScriptImproved: (improvedScript: string) => void
}

// Patterns that indicate robotic or unnatural speech
const ROBOTIC_PATTERNS = {
  monotone_delivery: {
    indicators: [
      /^[A-Z][a-z,\s]+\.\s[A-Z][a-z,\s]+\.\s[A-Z][a-z,\s]+\./, // repetitive sentence structure
      /\b(The|This|That|It)\s+is\s+\w+\.\s+(The|This|That|It)\s+is\s+\w+\./, // repetitive "This is..." patterns
      /\b(First|Second|Third|Fourth|Fifth)\s*[,.]\s*\w+/, // overly structured enumeration
      /\b(will|can|should|must)\s+\w+\s+(and|then)\s+(will|can|should|must)/, // repetitive modal verbs
    ],
    severity: 0.8,
    description: 'Repetitive sentence structures that sound unnatural'
  },
  unnatural_pauses: {
    indicators: [
      /\[PAUSE\]\s*\[PAUSE\]/, // multiple consecutive pauses
      /\.\s*\[PAUSE\]\s*[A-Z]/, // pause immediately after period
      /\,\s*\[PAUSE\]/, // pause after comma (unnatural)
      /\[PAUSE\].*?\[PAUSE\].*?\[PAUSE\].*?\[PAUSE\]/, // too many pauses in short text
    ],
    severity: 0.6,
    description: 'Unnatural pause placement that breaks conversational flow'
  },
  robotic_transitions: {
    indicators: [
      /\b(Now|Next|Then|Subsequently|Furthermore|Moreover|Additionally),?\s/g,
      /\b(In conclusion|To summarize|To conclude|In summary),?\s/g,
      /\b(Let us|Let's)\s+(now\s+)?(discuss|explore|examine|consider|look at)/g,
      /\b(Moving on|Proceeding|Continuing),?\s+(to|with)/g,
    ],
    severity: 0.7,
    description: 'Overly formal or academic transition phrases'
  },
  lack_of_variation: {
    indicators: [
      /\b(very|really|quite|extremely)\s+\w+\s+.*?\b(very|really|quite|extremely)\s+\w+/, // repetitive intensifiers
      /\b(\w+)\s+.*?\b\1\b/, // word repetition in close proximity
      /\b(and|but|so|because)\s+.*?\b(and|but|so|because)\s+.*?\b(and|but|so|because)/, // repetitive conjunctions
    ],
    severity: 0.5,
    description: 'Lack of vocabulary and structural variation'
  }
}

// Natural conversation patterns that should be encouraged
const NATURAL_PATTERNS = {
  conversational_starters: [
    'You know what?', 'Here\'s the thing:', 'Let me tell you something:', 
    'So here\'s what I discovered:', 'Want to know a secret?', 'Check this out:',
    'I\'ve got to share this with you:', 'This is pretty cool:'
  ],
  natural_transitions: [
    'And here\'s why that matters:', 'But wait, there\'s more:', 'Now, this is where it gets interesting:',
    'Here\'s the kicker:', 'And this is the best part:', 'But here\'s what really surprised me:',
    'So what does this mean for you?', 'Now you might be wondering:'
  ],
  engagement_phrases: [
    'Think about it:', 'Picture this:', 'Imagine if:', 'What if I told you:',
    'You\'ve probably experienced this:', 'Sound familiar?', 'We\'ve all been there:',
    'I bet you\'re thinking:'
  ],
  human_connectors: [
    'honestly', 'actually', 'basically', 'essentially', 'pretty much',
    'kind of', 'sort of', 'you see', 'the thing is', 'what happens is'
  ]
}

// Common filler words and natural speech patterns
const NATURAL_SPEECH_ELEMENTS = {
  fillers: ['um', 'uh', 'you know', 'like', 'I mean', 'well', 'so'],
  softeners: ['kind of', 'sort of', 'pretty much', 'basically', 'essentially'],
  connectors: ['and', 'but', 'so', 'because', 'although', 'however', 'actually'],
  emphasis: ['really', 'totally', 'absolutely', 'definitely', 'certainly', 'obviously']
}

export function ConversationalFlowAnalyzer({
  script,
  onAnalysisComplete,
  onScriptImproved
}: ConversationalFlowAnalyzerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isImproving, setIsImproving] = useState(false)
  const [analysis, setAnalysis] = useState<ConversationalFlow | null>(null)
  const [showDetailedAnalysis, setShowDetailedAnalysis] = useState(false)

  // Analyze script for robotic patterns
  const analyzeConversationalFlow = useCallback(async () => {
    setIsAnalyzing(true)
    
    try {
      const roboticPatterns: ConversationalFlow['robotic_patterns'] = []
      
      // Check each robotic pattern type
      Object.entries(ROBOTIC_PATTERNS).forEach(([patternType, pattern]) => {
        pattern.indicators.forEach((regex, index) => {
          const matches = Array.from(script.matchAll(regex))
          
          matches.forEach(match => {
            const startChar = match.index || 0
            const endChar = startChar + match[0].length
            
            roboticPatterns.push({
              pattern_type: patternType as any,
              confidence_score: pattern.severity,
              suggested_fix: generateSuggestionForPattern(patternType, match[0]),
              text_location: {
                start_char: startChar,
                end_char: endChar
              }
            })
          })
        })
      })
      
      // Calculate natural flow score
      const naturalFlowScore = calculateNaturalFlowScore(script, roboticPatterns)
      
      // Generate improvement suggestions
      const improvementSuggestions = generateImprovementSuggestions(script, roboticPatterns)
      
      const flowAnalysis: ConversationalFlow = {
        robotic_patterns: roboticPatterns,
        natural_flow_score: naturalFlowScore,
        improvement_suggestions: improvementSuggestions
      }
      
      setAnalysis(flowAnalysis)
      onAnalysisComplete(flowAnalysis)
      
    } catch (error) {
      console.error('Failed to analyze conversational flow:', error)
      alert('Failed to analyze conversational flow. Please try again.')
    } finally {
      setIsAnalyzing(false)
    }
  }, [script, onAnalysisComplete])

  // Generate suggestion for specific pattern
  const generateSuggestionForPattern = (patternType: string, matchedText: string): string => {
    switch (patternType) {
      case 'monotone_delivery':
        return `Vary sentence structure and add conversational elements like "You know what's interesting?" or "Here's something cool:"`
      case 'unnatural_pauses':
        return `Remove excessive pauses and place them more naturally after complete thoughts or for emphasis`
      case 'robotic_transitions':
        return `Replace with natural transitions like "And here's the thing:" or "But wait, there's more:"`
      case 'lack_of_variation':
        return `Add variety with synonyms and different sentence structures. Use conversational connectors.`
      default:
        return `Make this section sound more conversational and natural`
    }
  }

  // Calculate overall natural flow score
  const calculateNaturalFlowScore = (text: string, roboticPatterns: any[]): number => {
    let score = 1.0
    
    // Reduce score for each robotic pattern
    roboticPatterns.forEach(pattern => {
      score -= (pattern.confidence_score * 0.1)
    })
    
    // Bonus points for natural elements
    const naturalElements = countNaturalElements(text)
    score += (naturalElements.total * 0.02)
    
    // Ensure score stays within bounds
    return Math.max(0, Math.min(1, score))
  }

  // Count natural conversational elements
  const countNaturalElements = (text: string) => {
    const counts = {
      conversational_starters: 0,
      natural_transitions: 0,
      engagement_phrases: 0,
      human_connectors: 0,
      total: 0
    }
    
    // Count each type of natural element
    Object.entries(NATURAL_PATTERNS).forEach(([type, patterns]) => {
      patterns.forEach(pattern => {
        const occurrences = (text.toLowerCase().match(new RegExp(pattern.toLowerCase(), 'g')) || []).length
        counts[type as keyof typeof counts] += occurrences
      })
    })
    
    counts.total = Object.values(counts).slice(0, -1).reduce((sum, count) => sum + count, 0)
    return counts
  }

  // Generate specific improvement suggestions
  const generateImprovementSuggestions = (text: string, roboticPatterns: any[]): string[] => {
    const suggestions: string[] = []
    
    // Specific suggestions based on analysis
    if (roboticPatterns.length > 5) {
      suggestions.push('Consider rewriting sections with high robotic pattern density')
    }
    
    if (roboticPatterns.some(p => p.pattern_type === 'monotone_delivery')) {
      suggestions.push('Add more conversational variety and natural speech patterns')
    }
    
    if (roboticPatterns.some(p => p.pattern_type === 'unnatural_pauses')) {
      suggestions.push('Improve pause placement for better natural flow')
    }
    
    if (roboticPatterns.some(p => p.pattern_type === 'robotic_transitions')) {
      suggestions.push('Replace formal transitions with conversational connectors')
    }
    
    const naturalElements = countNaturalElements(text)
    if (naturalElements.total < 3) {
      suggestions.push('Add more engaging, conversational elements to connect with viewers')
    }
    
    if (!text.includes('you') && !text.includes('your')) {
      suggestions.push('Include more direct address to the viewer ("you", "your") for engagement')
    }
    
    if (text.split('!').length < 2) {
      suggestions.push('Add more enthusiasm and exclamation points where appropriate')
    }
    
    return suggestions
  }

  // Improve script based on analysis
  const improveScript = useCallback(async () => {
    if (!analysis) return
    
    setIsImproving(true)
    
    try {
      let improvedScript = script
      
      // Apply improvements for each detected pattern
      analysis.robotic_patterns.forEach(pattern => {
        const problematicText = script.substring(
          pattern.text_location.start_char,
          pattern.text_location.end_char
        )
        
        const improvedText = applyImprovementForPattern(pattern.pattern_type, problematicText)
        improvedScript = improvedScript.replace(problematicText, improvedText)
      })
      
      // Add natural elements if score is low
      if (analysis.natural_flow_score < 0.7) {
        improvedScript = addNaturalElements(improvedScript)
      }
      
      // Final polish
      improvedScript = addConversationalPolish(improvedScript)
      
      onScriptImproved(improvedScript)
      
    } catch (error) {
      console.error('Failed to improve script:', error)
      alert('Failed to improve script. Please try again.')
    } finally {
      setIsImproving(false)
    }
  }, [analysis, script, onScriptImproved])

  // Apply specific improvements for pattern types
  const applyImprovementForPattern = (patternType: string, text: string): string => {
    switch (patternType) {
      case 'monotone_delivery':
        // Add conversational starters
        if (text.startsWith('The ') || text.startsWith('This ')) {
          const starters = NATURAL_PATTERNS.conversational_starters
          const randomStarter = starters[Math.floor(Math.random() * starters.length)]
          return `${randomStarter} ${text.toLowerCase()}`
        }
        return text
      
      case 'robotic_transitions':
        // Replace formal transitions
        const formalWords = ['Furthermore', 'Moreover', 'Additionally', 'Subsequently']
        let improved = text
        formalWords.forEach(word => {
          if (improved.includes(word)) {
            const natural = NATURAL_PATTERNS.natural_transitions
            const replacement = natural[Math.floor(Math.random() * natural.length)]
            improved = improved.replace(word, replacement)
          }
        })
        return improved
      
      case 'unnatural_pauses':
        // Fix pause placement
        return text.replace(/\[PAUSE\]\s*\[PAUSE\]/g, '[PAUSE]')
                  .replace(/\.\s*\[PAUSE\]/g, '. ')
                  .replace(/\,\s*\[PAUSE\]/g, ', ')
      
      default:
        return text
    }
  }

  // Add natural conversational elements
  const addNaturalElements = (text: string): string => {
    let improved = text
    
    // Add engagement phrases at the beginning if missing
    if (!NATURAL_PATTERNS.conversational_starters.some(starter => 
      improved.toLowerCase().includes(starter.toLowerCase())
    )) {
      const starter = NATURAL_PATTERNS.conversational_starters[0]
      improved = `${starter} ${improved}`
    }
    
    // Add human connectors
    improved = improved.replace(/\. ([A-Z])/g, (match, letter) => {
      const connectors = NATURAL_SPEECH_ELEMENTS.connectors
      const connector = connectors[Math.floor(Math.random() * connectors.length)]
      return `. ${connector.charAt(0).toUpperCase() + connector.slice(1)}, ${letter.toLowerCase()}`
    })
    
    return improved
  }

  // Add final conversational polish
  const addConversationalPolish = (text: string): string => {
    let polished = text
    
    // Add strategic "you know" and other natural elements
    polished = polished.replace(/\. (This|That|It)/g, '. You know, $1')
    
    // Add emphasis where appropriate
    polished = polished.replace(/important/g, 'really important')
    polished = polished.replace(/good/g, 'pretty good')
    
    // Add natural pauses in better places
    polished = polished.replace(/([.!?])\s+([A-Z])/g, '$1 [PAUSE] $2')
    
    return polished
  }

  // Auto-analyze when script changes
  useEffect(() => {
    if (script && script.length > 50) {
      const timeoutId = setTimeout(() => {
        analyzeConversationalFlow()
      }, 1000) // Debounce analysis
      
      return () => clearTimeout(timeoutId)
    }
  }, [script, analyzeConversationalFlow])

  const analysisStats = analysis ? {
    totalIssues: analysis.robotic_patterns.length,
    highSeverityIssues: analysis.robotic_patterns.filter(p => p.confidence_score > 0.7).length,
    flowScore: Math.round(analysis.natural_flow_score * 100),
    improvementCount: analysis.improvement_suggestions.length
  } : null

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Conversational Flow Analyzer</h3>
        <p className="text-sm text-gray-600">
          Detect and fix robotic speech patterns to make your AI avatar sound completely natural and conversational
        </p>
      </div>

      {/* Analysis Controls */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={analyzeConversationalFlow}
          disabled={isAnalyzing || !script.trim()}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
        >
          {isAnalyzing ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Analyzing Flow...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              Analyze Conversational Flow
            </>
          )}
        </Button>
        
        {analysis && (
          <Button
            onClick={improveScript}
            disabled={isImproving}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
          >
            {isImproving ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Improving Script...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Apply Natural Flow Improvements
              </>
            )}
          </Button>
        )}
        
        {analysis && (
          <Button
            variant="outline"
            onClick={() => setShowDetailedAnalysis(!showDetailedAnalysis)}
          >
            {showDetailedAnalysis ? 'Hide Details' : 'Show Details'}
          </Button>
        )}
      </div>

      {/* Analysis Results */}
      {analysis && analysisStats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Conversational Flow Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  analysisStats.flowScore >= 80 ? 'text-green-600' :
                  analysisStats.flowScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {analysisStats.flowScore}%
                </div>
                <div className="text-sm text-gray-600">Natural Flow Score</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  analysisStats.totalIssues === 0 ? 'text-green-600' :
                  analysisStats.totalIssues <= 3 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {analysisStats.totalIssues}
                </div>
                <div className="text-sm text-gray-600">Robotic Patterns</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  analysisStats.highSeverityIssues === 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {analysisStats.highSeverityIssues}
                </div>
                <div className="text-sm text-gray-600">High Severity</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analysisStats.improvementCount}
                </div>
                <div className="text-sm text-gray-600">Suggestions</div>
              </div>
            </div>

            {/* Overall Assessment */}
            {analysisStats.flowScore >= 80 ? (
              <Alert className="border-green-200 bg-green-50">
                <AlertDescription className="text-green-800">
                  ✅ Excellent conversational flow! Your script sounds natural and engaging.
                </AlertDescription>
              </Alert>
            ) : analysisStats.flowScore >= 60 ? (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertDescription className="text-yellow-800">
                  ⚠️ Good flow with room for improvement. Consider applying the suggested fixes.
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">
                  🚨 Script has robotic patterns that may sound unnatural. Improvements recommended.
                </AlertDescription>
              </Alert>
            )}

            {/* Improvement Suggestions */}
            {analysis.improvement_suggestions.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Key Improvement Areas:</h4>
                <ul className="space-y-1">
                  {analysis.improvement_suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Detailed Analysis */}
            {showDetailedAnalysis && analysis.robotic_patterns.length > 0 && (
              <div className="mt-6 border-t pt-4">
                <h4 className="font-medium mb-3">Detected Robotic Patterns:</h4>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {analysis.robotic_patterns.map((pattern, index) => (
                    <div key={index} className="border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="capitalize">
                          {pattern.pattern_type.replace('_', ' ')}
                        </Badge>
                        <Badge variant="outline" className={
                          pattern.confidence_score > 0.7 ? 'bg-red-100 text-red-800' :
                          pattern.confidence_score > 0.5 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }>
                          {Math.round(pattern.confidence_score * 100)}% confidence
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-700 mb-2">
                        <span className="font-medium">Issue:</span> {ROBOTIC_PATTERNS[pattern.pattern_type as keyof typeof ROBOTIC_PATTERNS]?.description}
                      </p>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Suggestion:</span> {pattern.suggested_fix}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Tips for Natural Conversation */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-base text-blue-800">Tips for Natural Conversational Flow</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 text-sm">
            <div>
              <h5 className="font-medium text-blue-700 mb-1">Use Natural Starters:</h5>
              <p className="text-blue-600">Try: "You know what?", "Here's the thing:", "Check this out:"</p>
            </div>
            <div>
              <h5 className="font-medium text-blue-700 mb-1">Add Engagement:</h5>
              <p className="text-blue-600">Include: "Picture this:", "Think about it:", "Sound familiar?"</p>
            </div>
            <div>
              <h5 className="font-medium text-blue-700 mb-1">Natural Transitions:</h5>
              <p className="text-blue-600">Replace "Furthermore" with "And here's why that matters:"</p>
            </div>
            <div>
              <h5 className="font-medium text-blue-700 mb-1">Direct Address:</h5>
              <p className="text-blue-600">Use "you" and "your" to connect directly with viewers</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}