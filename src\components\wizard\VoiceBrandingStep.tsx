'use client'

import { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { VoiceSelector } from '@/components/video/VoiceSelector'
import { useProjectWizard } from '@/context/project-wizard-context'

interface BrandingColors {
  primary: string
  secondary: string
  accent: string
}

const DEFAULT_BRAND_COLORS: BrandingColors = {
  primary: '#3B82F6',
  secondary: '#6366F1', 
  accent: '#F59E0B'
}

const WATERMARK_POSITIONS = [
  { id: 'top-left', label: 'Top Left', icon: '↖️' },
  { id: 'top-right', label: 'Top Right', icon: '↗️' },
  { id: 'bottom-left', label: 'Bottom Left', icon: '↙️' },
  { id: 'bottom-right', label: 'Bottom Right', icon: '↘️' }
] as const

type WatermarkPosition = typeof WATERMARK_POSITIONS[number]['id']

export function VoiceBrandingStep() {
  const { projectData, dispatch } = useProjectWizard()
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(
    projectData.custom_branding?.logo_url || null
  )
  const [isUploadingLogo, setIsUploadingLogo] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [brandColors, setBrandColors] = useState<BrandingColors>(
    projectData.custom_branding?.brand_colors || DEFAULT_BRAND_COLORS
  )
  
  const [watermarkPosition, setWatermarkPosition] = useState<WatermarkPosition>(
    projectData.custom_branding?.watermark_position || 'bottom-right'
  )

  const handleVoiceSelect = (voiceId: string) => {
    dispatch({ 
      type: 'UPDATE_VOICE_BRANDING', 
      payload: { 
        voice_id: voiceId,
        custom_branding: projectData.custom_branding 
      } 
    })
  }

  const handleLogoUpload = useCallback(async (file: File) => {
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please upload an image file (PNG, JPG, SVG)')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Logo file must be smaller than 5MB')
      return
    }

    setIsUploadingLogo(true)
    try {
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreview(result)
      }
      reader.readAsDataURL(file)

      // In a real app, you would upload to a cloud storage service
      // For now, we'll use the data URL as a placeholder
      const logoDataUrl = await new Promise<string>((resolve) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.readAsDataURL(file)
      })

      // Update project data
      const updatedBranding = {
        logo_url: logoDataUrl,
        brand_colors: brandColors,
        watermark_position: watermarkPosition
      }

      dispatch({
        type: 'UPDATE_VOICE_BRANDING',
        payload: {
          voice_id: projectData.voice_id,
          custom_branding: updatedBranding
        }
      })

      setLogoFile(file)
    } catch (error) {
      console.error('Error uploading logo:', error)
      alert('Failed to upload logo. Please try again.')
    } finally {
      setIsUploadingLogo(false)
    }
  }, [brandColors, watermarkPosition, projectData.voice_id, dispatch])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleLogoUpload(file)
    }
  }

  const handleColorChange = (colorType: keyof BrandingColors, value: string) => {
    const newColors = { ...brandColors, [colorType]: value }
    setBrandColors(newColors)
    
    const updatedBranding = {
      logo_url: logoPreview,
      brand_colors: newColors,
      watermark_position: watermarkPosition
    }

    dispatch({
      type: 'UPDATE_VOICE_BRANDING',
      payload: {
        voice_id: projectData.voice_id,
        custom_branding: updatedBranding
      }
    })
  }

  const handleWatermarkPositionChange = (position: WatermarkPosition) => {
    setWatermarkPosition(position)
    
    const updatedBranding = {
      logo_url: logoPreview,
      brand_colors: brandColors,
      watermark_position: position
    }

    dispatch({
      type: 'UPDATE_VOICE_BRANDING',
      payload: {
        voice_id: projectData.voice_id,
        custom_branding: updatedBranding
      }
    })
  }

  const removeLogo = () => {
    setLogoFile(null)
    setLogoPreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }

    const updatedBranding = {
      logo_url: undefined,
      brand_colors: brandColors,
      watermark_position: watermarkPosition
    }

    dispatch({
      type: 'UPDATE_VOICE_BRANDING',
      payload: {
        voice_id: projectData.voice_id,
        custom_branding: updatedBranding
      }
    })
  }

  const resetToDefaults = () => {
    setBrandColors(DEFAULT_BRAND_COLORS)
    setWatermarkPosition('bottom-right')
    setLogoFile(null)
    setLogoPreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }

    dispatch({
      type: 'UPDATE_VOICE_BRANDING',
      payload: {
        voice_id: projectData.voice_id,
        custom_branding: undefined
      }
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Voice & Branding</h3>
        <p className="text-sm text-gray-600 mb-6">
          Select your AI voice and customize your brand appearance in the video
        </p>
      </div>

      {/* Voice Selection */}
      <VoiceSelector
        selectedVoiceId={projectData.voice_id}
        onSelect={handleVoiceSelect}
        error={undefined}
      />

      {/* Custom Branding Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
            </svg>
            Custom Branding (Optional)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Upload */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">
              Brand Logo
            </label>
            <div className="flex items-start space-x-4">
              {/* Logo Preview */}
              <div className="flex-shrink-0">
                {logoPreview ? (
                  <div className="relative group">
                    <div className="w-24 h-24 border-2 border-gray-300 rounded-lg overflow-hidden bg-white">
                      <img 
                        src={logoPreview} 
                        alt="Logo preview" 
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={removeLogo}
                    >
                      ×
                    </Button>
                  </div>
                ) : (
                  <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </div>

              {/* Upload Controls */}
              <div className="flex-1 space-y-3">
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploadingLogo}
                    className="mr-3"
                  >
                    {isUploadingLogo ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        {logoPreview ? 'Change Logo' : 'Upload Logo'}
                      </>
                    )}
                  </Button>
                  {logoPreview && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={removeLogo}
                    >
                      Remove
                    </Button>
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  Recommended: PNG or SVG format, transparent background, max 5MB
                </p>
              </div>
            </div>
          </div>

          {/* Brand Colors */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">
              Brand Colors
            </label>
            <div className="grid gap-4 md:grid-cols-3">
              {/* Primary Color */}
              <div>
                <label className="text-xs text-gray-600 mb-2 block">Primary</label>
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-10 h-10 rounded-lg border-2 border-gray-300 cursor-pointer relative overflow-hidden"
                    style={{ backgroundColor: brandColors.primary }}
                  >
                    <input
                      type="color"
                      value={brandColors.primary}
                      onChange={(e) => handleColorChange('primary', e.target.value)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  <Input
                    value={brandColors.primary}
                    onChange={(e) => handleColorChange('primary', e.target.value)}
                    className="flex-1 text-sm"
                    placeholder="#3B82F6"
                  />
                </div>
              </div>

              {/* Secondary Color */}
              <div>
                <label className="text-xs text-gray-600 mb-2 block">Secondary</label>
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-10 h-10 rounded-lg border-2 border-gray-300 cursor-pointer relative overflow-hidden"
                    style={{ backgroundColor: brandColors.secondary }}
                  >
                    <input
                      type="color"
                      value={brandColors.secondary}
                      onChange={(e) => handleColorChange('secondary', e.target.value)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  <Input
                    value={brandColors.secondary}
                    onChange={(e) => handleColorChange('secondary', e.target.value)}
                    className="flex-1 text-sm"
                    placeholder="#6366F1"
                  />
                </div>
              </div>

              {/* Accent Color */}
              <div>
                <label className="text-xs text-gray-600 mb-2 block">Accent</label>
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-10 h-10 rounded-lg border-2 border-gray-300 cursor-pointer relative overflow-hidden"
                    style={{ backgroundColor: brandColors.accent }}
                  >
                    <input
                      type="color"
                      value={brandColors.accent}
                      onChange={(e) => handleColorChange('accent', e.target.value)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  <Input
                    value={brandColors.accent}
                    onChange={(e) => handleColorChange('accent', e.target.value)}
                    className="flex-1 text-sm"
                    placeholder="#F59E0B"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Watermark Position */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">
              Logo Position in Video
            </label>
            <div className="grid grid-cols-2 gap-3">
              {WATERMARK_POSITIONS.map((position) => (
                <Button
                  key={position.id}
                  variant={watermarkPosition === position.id ? 'default' : 'outline'}
                  className="flex items-center justify-center space-x-2 h-12"
                  onClick={() => handleWatermarkPositionChange(position.id)}
                >
                  <span className="text-lg">{position.icon}</span>
                  <span>{position.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Preview Section */}
          {(logoPreview || brandColors.primary !== DEFAULT_BRAND_COLORS.primary) && (
            <div>
              <label className="text-sm font-medium text-gray-700 mb-3 block">
                Brand Preview
              </label>
              <div className="relative bg-gray-100 rounded-lg p-6 min-h-[200px] overflow-hidden">
                {/* Mock video frame */}
                <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 rounded-md flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                      <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-600">Your AI Avatar will appear here</p>
                  </div>
                </div>

                {/* Logo watermark */}
                {logoPreview && (
                  <div className={`absolute w-16 h-16 ${
                    watermarkPosition === 'top-left' ? 'top-4 left-4' :
                    watermarkPosition === 'top-right' ? 'top-4 right-4' :
                    watermarkPosition === 'bottom-left' ? 'bottom-4 left-4' :
                    'bottom-4 right-4'
                  }`}>
                    <div className="w-full h-full bg-white/90 rounded-lg p-2 shadow-lg">
                      <img 
                        src={logoPreview} 
                        alt="Logo watermark" 
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </div>
                )}

                {/* Color accent bar */}
                <div 
                  className="absolute bottom-0 left-0 right-0 h-2"
                  style={{ 
                    background: `linear-gradient(90deg, ${brandColors.primary}, ${brandColors.secondary}, ${brandColors.accent})` 
                  }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Preview of how your branding will appear in the generated video
              </p>
            </div>
          )}

          {/* Reset Button */}
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={resetToDefaults}
              disabled={!logoPreview && brandColors.primary === DEFAULT_BRAND_COLORS.primary}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Branding Summary */}
      {projectData.custom_branding && (
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-purple-900">Custom Branding Applied</h4>
                <div className="flex items-center space-x-4 mt-1">
                  {projectData.custom_branding.logo_url && (
                    <Badge className="bg-purple-100 text-purple-800">
                      Logo Added
                    </Badge>
                  )}
                  <Badge className="bg-blue-100 text-blue-800">
                    Position: {watermarkPosition.replace('-', ' ')}
                  </Badge>
                  <div className="flex space-x-1">
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300" 
                      style={{ backgroundColor: brandColors.primary }}
                      title="Primary Color"
                    />
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300" 
                      style={{ backgroundColor: brandColors.secondary }}
                      title="Secondary Color"  
                    />
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300" 
                      style={{ backgroundColor: brandColors.accent }}
                      title="Accent Color"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}