import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const pricingTiers = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for trying out our platform',
    price: '$0',
    period: 'forever',
    credits: '5 videos/month',
    features: [
      '5 videos per month',
      'Basic avatars',
      'Standard voices',
      'HD video quality',
      'Community support',
      'Basic templates'
    ],
    limitations: [
      'Watermarked videos',
      'Limited export options'
    ],
    cta: 'Start Free',
    popular: false
  },
  {
    id: 'starter',
    name: 'Starter',
    description: 'Great for individuals and small teams',
    price: '$29',
    period: 'per month',
    credits: '100 videos/month',
    features: [
      '100 videos per month',
      'All avatars',
      'Premium voices',
      'HD video quality',
      'Email support',
      'Custom backgrounds',
      'No watermarks',
      'All export formats'
    ],
    cta: 'Start Trial',
    popular: false
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Perfect for growing businesses',
    price: '$99',
    period: 'per month',
    credits: '500 videos/month',
    features: [
      '500 videos per month',
      'All avatars',
      'Premium voices',
      '4K video quality',
      'Priority support',
      'Custom backgrounds',
      'API access',
      'Bulk generation',
      'Team collaboration',
      'Advanced analytics'
    ],
    cta: 'Start Trial',
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large organizations',
    price: 'Custom',
    period: 'per month',
    credits: 'Unlimited videos',
    features: [
      'Unlimited videos',
      'All avatars',
      'Premium voices',
      '4K video quality',
      'Dedicated support',
      'Custom backgrounds',
      'API access',
      'Bulk generation',
      'Team collaboration',
      'Advanced analytics',
      'Custom integrations',
      'White-label options',
      'SLA guarantee'
    ],
    cta: 'Contact Sales',
    popular: false
  }
];

const faqs = [
  {
    question: "How do video credits work?",
    answer: "Each video generation uses 1 credit. Credits reset monthly and unused credits don't roll over to the next month."
  },
  {
    question: "Can I change my plan anytime?",
    answer: "Yes! You can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle."
  },
  {
    question: "What happens if I exceed my credit limit?",
    answer: "You can purchase additional credits or upgrade to a higher plan. Your account won't be suspended."
  },
  {
    question: "Do you offer refunds?",
    answer: "We offer a 30-day money-back guarantee for all paid plans. No questions asked."
  },
  {
    question: "Is there a free trial?",
    answer: "Yes! All paid plans come with a 14-day free trial. No credit card required to start."
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards, PayPal, and bank transfers for enterprise customers."
  }
];

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-hero">
      {/* Navigation Header */}
      <nav className="glass-nav sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-gradient-primary rounded-sm"></div>
              </div>
              <span className="text-xl font-bold text-white">HeyGen Video</span>
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/#features" className="text-white/80 hover:text-white transition-colors">Features</Link>
              <Link href="/pricing" className="text-white hover:text-white transition-colors font-medium">Pricing</Link>
              <a href="#faq" className="text-white/80 hover:text-white transition-colors">FAQ</a>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link href="/login">
                <Button variant="ghost" className="text-white hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signup">
                <Button className="btn-gradient text-white border-0">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="px-6 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Simple, Transparent
            <br />
            <span className="text-gradient">Pricing</span>
          </h1>
          <p className="text-xl text-white/90 mb-8 leading-relaxed">
            Choose the perfect plan for your video creation needs. Start free and scale as you grow.
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <span className="text-white/80">Monthly</span>
            <div className="relative">
              <input
                type="checkbox"
                id="billing-toggle"
                className="sr-only"
              />
              <label
                htmlFor="billing-toggle"
                className="flex items-center cursor-pointer"
              >
                <div className="w-14 h-8 bg-white/20 rounded-full p-1 transition-colors">
                  <div className="w-6 h-6 bg-white rounded-full shadow-sm transition-transform"></div>
                </div>
              </label>
            </div>
            <span className="text-white/80">
              Annual
              <Badge className="ml-2 bg-green-500/20 text-green-400 border-green-500/30">
                Save 20%
              </Badge>
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="px-6 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {pricingTiers.map((tier) => (
              <Card
                key={tier.id}
                className={`glass-card shadow-card relative ${
                  tier.popular ? 'ring-2 ring-white/30' : ''
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-primary text-white px-4 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                    {tier.name}
                  </CardTitle>
                  <CardDescription className="text-gray-600 mb-4">
                    {tier.description}
                  </CardDescription>
                  
                  <div className="mb-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900">
                        {tier.price}
                      </span>
                      {tier.period !== 'forever' && (
                        <span className="text-gray-600 ml-2">/{tier.period}</span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 mt-2">{tier.credits}</p>
                  </div>
                  
                  <Link href={tier.id === 'enterprise' ? '/contact' : '/signup'}>
                    <Button
                      className={
                        tier.popular
                          ? "btn-gradient text-white w-full"
                          : "w-full border-gray-300"
                      }
                      variant={tier.popular ? "default" : "outline"}
                    >
                      {tier.cta}
                    </Button>
                  </Link>
                </CardHeader>
                
                <CardContent>
                  <ul className="space-y-3">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                    
                    {tier.limitations && tier.limitations.map((limitation, index) => (
                      <li key={`limitation-${index}`} className="flex items-center">
                        <svg className="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-500">{limitation}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <Card className="glass-card shadow-card">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-gray-900 mb-4">
                Compare Plans
              </CardTitle>
              <CardDescription className="text-lg">
                See what's included in each plan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-4 px-4 font-medium text-gray-900">Feature</th>
                      <th className="text-center py-4 px-4 font-medium text-gray-900">Free</th>
                      <th className="text-center py-4 px-4 font-medium text-gray-900">Starter</th>
                      <th className="text-center py-4 px-4 font-medium text-gray-900">Pro</th>
                      <th className="text-center py-4 px-4 font-medium text-gray-900">Enterprise</th>
                    </tr>
                  </thead>
                  <tbody className="text-sm">
                    <tr className="border-b border-gray-100">
                      <td className="py-3 px-4 text-gray-700">Videos per month</td>
                      <td className="py-3 px-4 text-center">5</td>
                      <td className="py-3 px-4 text-center">100</td>
                      <td className="py-3 px-4 text-center">500</td>
                      <td className="py-3 px-4 text-center">Unlimited</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-3 px-4 text-gray-700">Video quality</td>
                      <td className="py-3 px-4 text-center">HD</td>
                      <td className="py-3 px-4 text-center">HD</td>
                      <td className="py-3 px-4 text-center">4K</td>
                      <td className="py-3 px-4 text-center">4K</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-3 px-4 text-gray-700">AI Avatars</td>
                      <td className="py-3 px-4 text-center">Basic</td>
                      <td className="py-3 px-4 text-center">All</td>
                      <td className="py-3 px-4 text-center">All</td>
                      <td className="py-3 px-4 text-center">All + Custom</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-3 px-4 text-gray-700">API Access</td>
                      <td className="py-3 px-4 text-center">✗</td>
                      <td className="py-3 px-4 text-center">✗</td>
                      <td className="py-3 px-4 text-center">✓</td>
                      <td className="py-3 px-4 text-center">✓</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-3 px-4 text-gray-700">Support</td>
                      <td className="py-3 px-4 text-center">Community</td>
                      <td className="py-3 px-4 text-center">Email</td>
                      <td className="py-3 px-4 text-center">Priority</td>
                      <td className="py-3 px-4 text-center">Dedicated</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-white/80">
              Everything you need to know about our pricing
            </p>
          </div>
          
          <div className="grid gap-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="glass-card shadow-card">
                <CardHeader>
                  <CardTitle className="text-lg text-gray-900">
                    {faq.question}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="glass-card p-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of creators who are already using our platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/signup">
                <Button size="lg" className="btn-gradient text-white px-8 py-3 text-lg">
                  Start Free Trial
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-gray-300">
                  Contact Sales
                </Button>
              </Link>
            </div>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-12 border-t border-white/20">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <div className="w-4 h-4 bg-gradient-primary rounded-sm"></div>
                </div>
                <span className="text-xl font-bold text-white">HeyGen Video</span>
              </div>
              <p className="text-white/70">
                The future of video creation is here.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-2 text-white/70">
                <li><Link href="/#features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/templates" className="hover:text-white transition-colors">Templates</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-white/70">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/careers" className="hover:text-white transition-colors">Careers</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-white mb-4">Support</h4>
              <ul className="space-y-2 text-white/70">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-white/20 mt-12 pt-8 text-center text-white/70">
            <p>&copy; 2024 HeyGen Video SaaS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}