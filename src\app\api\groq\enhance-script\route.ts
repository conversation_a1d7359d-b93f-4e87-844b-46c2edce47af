import { NextRequest, NextResponse } from 'next/server'
import { groqClient, ScriptEnhancementOptions } from '@/lib/groq'
import { createSupabaseServerClient } from '@/lib/supabase'
import { z } from 'zod'

const enhanceScriptSchema = z.object({
  script: z.string().min(1, 'Script is required'),
  options: z.object({
    tone: z.enum(['professional', 'casual', 'enthusiastic', 'educational', 'sales']).optional(),
    targetLength: z.enum(['short', 'medium', 'long']).optional(),
    industry: z.string().optional(),
    callToAction: z.string().optional(),
    platform: z.enum(['youtube', 'tiktok', 'linkedin', 'instagram', 'general']).optional()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const { script, options = {} } = enhanceScriptSchema.parse(body)

    // Enhance script using Groq
    const enhancedScript = await groqClient.enhanceScript(script, options as ScriptEnhancementOptions)

    // Calculate improvement metrics
    const originalWordCount = script.split(' ').length
    const enhancedWordCount = enhancedScript.split(' ').length
    const improvements = [
      'Enhanced structure and flow',
      'Added strategic pauses and emphasis markers',
      'Optimized for AI avatar delivery',
      'Improved call-to-action'
    ]

    return NextResponse.json({
      success: true,
      data: {
        enhanced_script: enhancedScript,
        original_script: script,
        improvements,
        metrics: {
          original_word_count: originalWordCount,
          enhanced_word_count: enhancedWordCount,
          estimated_duration: Math.ceil(enhancedWordCount / 2.5) // Rough estimate: 2.5 words per second
        }
      }
    })

  } catch (error) {
    console.error('Error enhancing script:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to enhance script' },
      { status: 500 }
    )
  }
}