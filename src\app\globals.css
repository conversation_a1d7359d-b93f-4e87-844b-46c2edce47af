@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 270 91% 65%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 270 91% 65%;
    --radius: 0.75rem;
    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 48 96% 53%;
    --warning-foreground: 26 83.3% 14.1%;
    
    /* Premium gradient colors matching the reference design */
    --gradient-start: 270 91% 65%;        /* Deep purple */
    --gradient-middle: 285 89% 70%;       /* Purple-pink transition */
    --gradient-end: 300 87% 75%;          /* Light purple */
    --gradient-pink: 320 85% 80%;         /* Soft pink */
    --gradient-purple: 270 91% 65%;       /* Primary purple */
    --gradient-blue: 240 89% 70%;         /* Blue accent */

    /* Task management specific colors */
    --task-bg: 255 255 255 / 0.08;
    --task-border: 255 255 255 / 0.12;
    --task-hover: 255 255 255 / 0.15;
    --project-card-bg: 255 255 255 / 0.10;
    --sidebar-bg: 0 0 0 / 0.20;
    --sidebar-item-hover: 255 255 255 / 0.08;
    
    /* Glass morphism colors */
    --glass-bg: 255 255 255 / 0.08;
    --glass-border: 255 255 255 / 0.16;
    --glass-light: 255 255 255 / 0.12;
    --glass-dark: 0 0 0 / 0.08;
    
    /* 3D shadows and effects */
    --shadow-3d: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-3d-hover: 0 8px 24px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-premium: 0 20px 80px rgba(139, 92, 246, 0.15);
    --shadow-neon: 0 0 20px rgba(139, 92, 246, 0.6);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 90% 60%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 90% 60%;
    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 48 96% 53%;
    --warning-foreground: 26 83.3% 14.1%;
    
    /* Dark mode glass morphism */
    --glass-bg: 0 0 0 / 0.2;
    --glass-border: 255 255 255 / 0.1;
    --glass-light: 255 255 255 / 0.05;
    --glass-dark: 0 0 0 / 0.3;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-smoothing: antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Premium background patterns matching reference design */
  .bg-hero {
    background: linear-gradient(135deg,
      hsl(var(--gradient-purple)) 0%,
      hsl(var(--gradient-start)) 25%,
      hsl(var(--gradient-middle)) 50%,
      hsl(var(--gradient-end)) 75%,
      hsl(var(--gradient-pink)) 100%);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
  }

  .bg-dashboard {
    background: linear-gradient(135deg,
      hsl(var(--gradient-start)) 0%,
      hsl(var(--gradient-middle)) 50%,
      hsl(var(--gradient-end)) 100%);
  }

  /* Task management specific backgrounds */
  .bg-task-card {
    background: rgba(var(--task-bg));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(var(--task-border));
  }

  .bg-project-card {
    background: rgba(var(--project-card-bg));
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(var(--task-border));
  }

  .bg-sidebar {
    background: rgba(var(--sidebar-bg));
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border-right: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  .bg-mesh {
    background: linear-gradient(135deg, 
      hsl(270 91% 65%) 0%, 
      hsl(280 89% 70%) 30%,
      hsl(300 87% 75%) 60%,
      hsl(320 85% 80%) 100%);
  }
  
  /* Premium gradients */
  .gradient-primary {
    background: linear-gradient(135deg, 
      hsl(var(--gradient-start)), 
      hsl(var(--gradient-middle)) 50%, 
      hsl(var(--gradient-end)));
  }
  
  .gradient-premium {
    background: linear-gradient(135deg, 
      #8B5CF6 0%, 
      #A78BFA 25%, 
      #C084FC 50%, 
      #E879F9 75%, 
      #F0ABFC 100%);
  }
  
  .gradient-mesh {
    background: linear-gradient(135deg, 
      rgba(139, 92, 246, 0.9) 0%, 
      rgba(167, 139, 250, 0.8) 30%,
      rgba(192, 132, 252, 0.7) 60%,
      rgba(232, 121, 249, 0.6) 100%);
  }
  
  .gradient-text {
    background: linear-gradient(135deg, 
      hsl(var(--gradient-start)), 
      hsl(var(--gradient-middle)) 50%, 
      hsl(var(--gradient-end)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  
  /* Typography enhancements */
  .text-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--gradient-start)), 
      hsl(var(--gradient-end)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-premium {
    background: linear-gradient(135deg, 
      #8B5CF6 0%, 
      #A78BFA 50%, 
      #C084FC 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@layer utilities {
  .font-sans {
    font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  }
}

/* Advanced Glassmorphism Effects */
.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.16);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.24);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.glass-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.05);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-light {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 3D Card Effects */
.card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-3d:hover {
  transform: rotateY(5deg) rotateX(5deg);
}

.card-float {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-float:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.15);
}

.card-tilt {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* Premium Button Styles */
.btn-premium {
  background: linear-gradient(135deg, 
    hsl(var(--gradient-start)), 
    hsl(var(--gradient-middle)));
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Advanced Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float-up {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-float {
  animation: float-up 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0), 
    rgba(255, 255, 255, 0.2), 
    rgba(255, 255, 255, 0));
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Shadows */
.shadow-premium {
  box-shadow: 0 20px 80px rgba(139, 92, 246, 0.15);
}

.shadow-premium-lg {
  box-shadow: 0 32px 120px rgba(139, 92, 246, 0.20);
}

.shadow-floating {
  box-shadow: 0 12px 60px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.08);
}

.shadow-floating-lg {
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.20), 0 8px 24px rgba(0, 0, 0, 0.12);
}

.shadow-neon {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
}

.shadow-neon-lg {
  box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
}

/* Custom Scrollbars */
.scrollbar-premium {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.6) transparent;
}

.scrollbar-premium::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-premium::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.scrollbar-premium::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.6), 
    rgba(167, 139, 250, 0.8));
  border-radius: 4px;
}

.scrollbar-premium::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.8), 
    rgba(167, 139, 250, 1));
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1), 
    rgba(255, 255, 255, 0.2), 
    rgba(255, 255, 255, 0.1));
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Interactive Elements */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.15);
}

.interactive-card:active {
  transform: translateY(-2px) scale(1.01);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .card-3d:hover {
    transform: none;
  }
  
  .card-float:hover {
    transform: translateY(-4px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.focus-premium:focus {
  outline: 2px solid rgba(139, 92, 246, 0.6);
  outline-offset: 2px;
}

.focus-premium:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.8);
  outline-offset: 2px;
}
