'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { LoadingSkeleton } from '@/components/ui/loading'

interface Avatar {
  avatar_id: string
  avatar_name: string
  preview_image_url: string
  gender: 'male' | 'female'
  age_range?: string
  style?: string
  is_public: boolean
}

interface AvatarSelectorProps {
  selectedAvatarId: string
  onSelect: (avatarId: string) => void
  error?: string
}

export function AvatarSelector({ selectedAvatarId, onSelect, error }: AvatarSelectorProps) {
  const [avatars, setAvatars] = useState<Avatar[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [genderFilter, setGenderFilter] = useState<'all' | 'male' | 'female'>('all')
  const [showPreview, setShowPreview] = useState<string | null>(null)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadAvatars()
    // Load favorites from localStorage
    const savedFavorites = localStorage.getItem('favorite-avatars')
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)))
    }
  }, [])

  const loadAvatars = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/heygen/avatars')
      const result = await response.json()

      if (result.success) {
        setAvatars(result.data)
      } else {
        throw new Error(result.error || 'Failed to load avatars')
      }
    } catch (error) {
      console.error('Failed to load avatars:', error)
      // Fallback with sample data for development
      setAvatars([
        {
          avatar_id: 'avatar-1',
          avatar_name: 'Sarah',
          preview_image_url: '/api/placeholder/200/300',
          gender: 'female',
          age_range: '25-35',
          style: 'professional',
          is_public: true
        },
        {
          avatar_id: 'avatar-2',
          avatar_name: 'David',
          preview_image_url: '/api/placeholder/200/300',
          gender: 'male',
          age_range: '30-40',
          style: 'business',
          is_public: true
        },
        {
          avatar_id: 'avatar-3',
          avatar_name: 'Emma',
          preview_image_url: '/api/placeholder/200/300',
          gender: 'female',
          age_range: '20-30',
          style: 'casual',
          is_public: true
        },
        {
          avatar_id: 'avatar-4',
          avatar_name: 'Michael',
          preview_image_url: '/api/placeholder/200/300',
          gender: 'male',
          age_range: '35-45',
          style: 'executive',
          is_public: true
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = (avatarId: string) => {
    const newFavorites = new Set(favorites)
    if (newFavorites.has(avatarId)) {
      newFavorites.delete(avatarId)
    } else {
      newFavorites.add(avatarId)
    }
    setFavorites(newFavorites)
    localStorage.setItem('favorite-avatars', JSON.stringify(Array.from(newFavorites)))
  }

  const filteredAvatars = avatars.filter(avatar => {
    const matchesSearch = avatar.avatar_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (avatar.style && avatar.style.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesGender = genderFilter === 'all' || avatar.gender === genderFilter
    return matchesSearch && matchesGender
  })

  // Sort avatars: favorites first, then by name
  const sortedAvatars = [...filteredAvatars].sort((a, b) => {
    const aFavorite = favorites.has(a.avatar_id)
    const bFavorite = favorites.has(b.avatar_id)
    if (aFavorite && !bFavorite) return -1
    if (!aFavorite && bFavorite) return 1
    return a.avatar_name.localeCompare(b.avatar_name)
  })

  const selectedAvatar = avatars.find(avatar => avatar.avatar_id === selectedAvatarId)

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Choose Your AI Avatar</h3>
          <LoadingSkeleton className="h-10 w-64 mb-4" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
            <div key={i} className="space-y-2">
              <LoadingSkeleton className="aspect-[3/4] w-full" />
              <LoadingSkeleton className="h-4 w-3/4" />
              <LoadingSkeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Choose Your AI Avatar</h3>
        <p className="text-sm text-gray-600 mb-6">
          Select an AI presenter that best represents your brand and message
        </p>
      </div>

      {/* Selected Avatar Display */}
      {selectedAvatar && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <img
                src={selectedAvatar.preview_image_url}
                alt={selectedAvatar.avatar_name}
                className="w-16 h-20 object-cover rounded-lg"
              />
              <div>
                <h4 className="font-medium text-blue-900">Selected Avatar</h4>
                <p className="text-blue-700">{selectedAvatar.avatar_name}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {selectedAvatar.gender}
                  </Badge>
                  {selectedAvatar.style && (
                    <Badge variant="outline" className="text-xs">
                      {selectedAvatar.style}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search avatars by name or style..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={genderFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('all')}
          >
            All
          </Button>
          <Button
            variant={genderFilter === 'male' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('male')}
          >
            Male
          </Button>
          <Button
            variant={genderFilter === 'female' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGenderFilter('female')}
          >
            Female
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {error}
        </div>
      )}

      {/* Avatar Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {sortedAvatars.map((avatar) => (
          <div
            key={avatar.avatar_id}
            className={`relative group cursor-pointer rounded-lg border-2 transition-all ${
              selectedAvatarId === avatar.avatar_id
                ? 'border-blue-500 ring-2 ring-blue-200 shadow-lg'
                : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
            }`}
            onClick={() => onSelect(avatar.avatar_id)}
          >
            <div className="relative overflow-hidden rounded-lg">
              <img
                src={avatar.preview_image_url}
                alt={avatar.avatar_name}
                className="w-full aspect-[3/4] object-cover"
                onError={(e) => {
                  // Fallback image
                  e.currentTarget.src = '/api/placeholder/200/300'
                }}
              />
              
              {/* Overlay with actions */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation()
                      setShowPreview(avatar.avatar_id)
                    }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </Button>
                  <Button
                    size="sm"
                    variant={favorites.has(avatar.avatar_id) ? 'default' : 'secondary'}
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleFavorite(avatar.avatar_id)
                    }}
                  >
                    <svg className="w-4 h-4" fill={favorites.has(avatar.avatar_id) ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </Button>
                </div>
              </div>

              {/* Favorite indicator */}
              {favorites.has(avatar.avatar_id) && (
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              )}

              {/* Selection indicator */}
              {selectedAvatarId === avatar.avatar_id && (
                <div className="absolute top-2 left-2">
                  <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              )}
            </div>

            {/* Avatar Info */}
            <div className="p-3">
              <h4 className="font-medium text-gray-900">{avatar.avatar_name}</h4>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {avatar.gender}
                </Badge>
                {avatar.style && (
                  <Badge variant="outline" className="text-xs">
                    {avatar.style}
                  </Badge>
                )}
                {avatar.age_range && (
                  <Badge variant="outline" className="text-xs">
                    {avatar.age_range}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {sortedAvatars.length === 0 && (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No avatars found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            {(() => {
              const previewAvatar = avatars.find(a => a.avatar_id === showPreview)
              return previewAvatar ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Avatar Preview</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowPreview(null)}
                    >
                      ✕
                    </Button>
                  </div>
                  <img
                    src={previewAvatar.preview_image_url}
                    alt={previewAvatar.avatar_name}
                    className="w-full max-w-xs mx-auto rounded-lg"
                  />
                  <div className="text-center">
                    <h4 className="font-medium">{previewAvatar.avatar_name}</h4>
                    <div className="flex justify-center space-x-2 mt-2">
                      <Badge variant="outline">{previewAvatar.gender}</Badge>
                      {previewAvatar.style && (
                        <Badge variant="outline">{previewAvatar.style}</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => setShowPreview(null)}
                    >
                      Close
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={() => {
                        onSelect(previewAvatar.avatar_id)
                        setShowPreview(null)
                      }}
                    >
                      Select Avatar
                    </Button>
                  </div>
                </div>
              ) : null
            })()}
          </div>
        </div>
      )}
    </div>
  )
}