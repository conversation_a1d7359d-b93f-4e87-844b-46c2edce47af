'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading'
import { useProjectWizard } from '@/context/project-wizard-context'

interface ResearchResult {
  documentation_links: string[]
  tutorial_steps: string[]
  common_issues: string[]
  best_practices: string[]
  estimated_difficulty: 'beginner' | 'intermediate' | 'advanced'
  keywords: string[]
  related_topics: string[]
}

interface TopicSuggestion {
  topic: string
  description: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  popularity: number
}

const popularTopics: TopicSuggestion[] = [
  {
    topic: "n8n automation basics",
    description: "Introduction to workflow automation with n8n",
    difficulty: "beginner",
    popularity: 95
  },
  {
    topic: "Zapier vs n8n comparison",
    description: "Compare automation platforms and their features",
    difficulty: "intermediate",
    popularity: 88
  },
  {
    topic: "API integration with n8n",
    description: "Connect external APIs using n8n workflows",
    difficulty: "intermediate",
    popularity: 82
  },
  {
    topic: "Advanced n8n workflows",
    description: "Complex automation scenarios and best practices",
    difficulty: "advanced",
    popularity: 76
  },
  {
    topic: "No-code automation trends",
    description: "Latest trends in no-code automation tools",
    difficulty: "beginner",
    popularity: 89
  },
  {
    topic: "Database automation with n8n",
    description: "Automate database operations using n8n",
    difficulty: "intermediate",
    popularity: 73
  }
]

export function TopicResearchStep() {
  const { projectData, dispatch } = useProjectWizard()
  const [isResearching, setIsResearching] = useState(false)
  const [researchResults, setResearchResults] = useState<ResearchResult | null>(null)
  const [showSuggestions, setShowSuggestions] = useState(true)
  const [customTopic, setCustomTopic] = useState(projectData.topic || '')

  useEffect(() => {
    if (projectData.researched_content) {
      setResearchResults({
        documentation_links: projectData.researched_content.documentation_links,
        tutorial_steps: projectData.researched_content.tutorial_steps,
        common_issues: projectData.researched_content.common_issues,
        best_practices: projectData.researched_content.best_practices,
        estimated_difficulty: projectData.researched_content.estimated_difficulty,
        keywords: [],
        related_topics: []
      })
    }
  }, [projectData.researched_content])

  const handleTopicSelect = (topic: string) => {
    setCustomTopic(topic)
    dispatch({ type: 'UPDATE_TOPIC', payload: topic })
    setShowSuggestions(false)
  }

  const handleCustomTopicChange = (value: string) => {
    setCustomTopic(value)
    dispatch({ type: 'UPDATE_TOPIC', payload: value })
    if (value.length === 0) {
      setShowSuggestions(true)
      setResearchResults(null)
    }
  }

  const handleResearch = async () => {
    if (!customTopic.trim()) return

    setIsResearching(true)
    try {
      const response = await fetch('/api/research/topic', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ topic: customTopic })
      })

      if (response.ok) {
        const results = await response.json()
        setResearchResults(results.data)
        
        // Update context with research results
        dispatch({
          type: 'UPDATE_RESEARCH',
          payload: {
            documentation_links: results.data.documentation_links,
            tutorial_steps: results.data.tutorial_steps,
            common_issues: results.data.common_issues,
            best_practices: results.data.best_practices,
            estimated_difficulty: results.data.estimated_difficulty
          }
        })
      } else {
        // Fallback with mock data for development
        const mockResults: ResearchResult = {
          documentation_links: [
            "https://docs.n8n.io/getting-started/",
            "https://docs.n8n.io/workflows/",
            "https://community.n8n.io/"
          ],
          tutorial_steps: [
            "Introduction to the topic and what viewers will learn",
            "Setting up the environment and prerequisites",
            "Step-by-step walkthrough of core concepts",
            "Practical examples and common use cases",
            "Troubleshooting common issues",
            "Advanced tips and best practices",
            "Conclusion and next steps"
          ],
          common_issues: [
            "Authentication problems with external services",
            "Workflow execution errors and debugging",
            "Performance optimization for complex workflows",
            "Data transformation and mapping challenges"
          ],
          best_practices: [
            "Always test workflows in development environment first",
            "Use descriptive names for nodes and workflows",
            "Implement proper error handling",
            "Document complex logic and transformations",
            "Regular backup of workflow configurations"
          ],
          estimated_difficulty: 'intermediate',
          keywords: ['automation', 'workflow', 'no-code', 'integration'],
          related_topics: ['Zapier alternatives', 'API automation', 'Business process automation']
        }
        
        setResearchResults(mockResults)
        dispatch({
          type: 'UPDATE_RESEARCH',
          payload: {
            documentation_links: mockResults.documentation_links,
            tutorial_steps: mockResults.tutorial_steps,
            common_issues: mockResults.common_issues,
            best_practices: mockResults.best_practices,
            estimated_difficulty: mockResults.estimated_difficulty
          }
        })
      }
    } catch (error) {
      console.error('Research failed:', error)
    } finally {
      setIsResearching(false)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'advanced': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Topic & Research</h3>
        <p className="text-sm text-gray-600 mb-6">
          Enter your video topic and let AI research the best content structure and key points to cover
        </p>
      </div>

      {/* Topic Input */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            What would you like to teach?
          </label>
          <div className="flex gap-3">
            <Input
              placeholder="e.g., 'Teach n8n basics', 'Create Zapier workflows', 'Build automation'"
              value={customTopic}
              onChange={(e) => handleCustomTopicChange(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={handleResearch}
              disabled={!customTopic.trim() || isResearching}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isResearching ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Researching...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Research Topic
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Topic Suggestions */}
        {showSuggestions && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Popular Topics</CardTitle>
              <CardDescription>
                Get started with these trending educational topics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2">
                {popularTopics.map((suggestion, index) => (
                  <div
                    key={index}
                    className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleTopicSelect(suggestion.topic)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{suggestion.topic}</h4>
                      <Badge className={getDifficultyColor(suggestion.difficulty)}>
                        {suggestion.difficulty}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${suggestion.popularity}%` }}
                        />
                      </div>
                      <span className="ml-2 text-xs text-gray-500">{suggestion.popularity}% popular</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Research Results */}
      {researchResults && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium">Research Results</h4>
            <Badge className={getDifficultyColor(researchResults.estimated_difficulty)}>
              {researchResults.estimated_difficulty} level
            </Badge>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* Tutorial Structure */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Recommended Structure</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {researchResults.tutorial_steps.map((step, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="text-sm text-gray-700">{step}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Key Resources */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Key Resources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Documentation Links */}
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Documentation</h5>
                  <div className="space-y-1">
                    {researchResults.documentation_links.slice(0, 3).map((link, index) => (
                      <a
                        key={index}
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block text-sm text-blue-600 hover:text-blue-800 truncate"
                      >
                        {link}
                      </a>
                    ))}
                  </div>
                </div>

                {/* Best Practices */}
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Best Practices</h5>
                  <div className="space-y-1">
                    {researchResults.best_practices.slice(0, 3).map((practice, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{practice}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Common Issues */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-base">Common Issues to Address</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2 md:grid-cols-2">
                  {researchResults.common_issues.map((issue, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-gray-700">{issue}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleResearch}
              disabled={isResearching}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Research Again
            </Button>
            <Button
              onClick={() => {
                // Export research results for later use
                const blob = new Blob([JSON.stringify(researchResults, null, 2)], { type: 'application/json' })
                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = `${customTopic.replace(/\s+/g, '-')}-research.json`
                a.click()
                URL.revokeObjectURL(url)
              }}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export Research
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}