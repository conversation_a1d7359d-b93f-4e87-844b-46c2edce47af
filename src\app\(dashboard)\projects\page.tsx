'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'

export default function ProjectsPage() {
  const projects = [
    {
      id: 1,
      name: 'Website Redesign',
      description: 'Complete overhaul of the company website with modern design and improved UX',
      status: 'active',
      progress: 75,
      dueDate: '2024-02-15',
      team: [
        { id: 1, name: '<PERSON>', avatar: 'bg-gradient-to-br from-purple-400 to-pink-400', initials: 'JD' },
        { id: 2, name: '<PERSON>', avatar: 'bg-gradient-to-br from-blue-400 to-purple-400', initials: 'SC' },
        { id: 3, name: '<PERSON>', avatar: 'bg-gradient-to-br from-green-400 to-blue-400', initials: 'MT' },
        { id: 4, name: '<PERSON>', avatar: 'bg-gradient-to-br from-yellow-400 to-orange-400', initials: '<PERSON><PERSON>' },
      ],
      tasks: {
        total: 24,
        completed: 18,
        inProgress: 4,
        todo: 2
      },
      priority: 'high',
      category: 'Design'
    },
    {
      id: 2,
      name: 'Mobile App Development',
      description: 'Native iOS and Android app with cross-platform functionality',
      status: 'active',
      progress: 45,
      dueDate: '2024-03-20',
      team: [
        { id: 5, name: 'Alex Rivera', avatar: 'bg-gradient-to-br from-red-400 to-pink-400', initials: 'AR' },
        { id: 6, name: 'Emma Wilson', avatar: 'bg-gradient-to-br from-indigo-400 to-purple-400', initials: 'EW' },
        { id: 7, name: 'David Kim', avatar: 'bg-gradient-to-br from-teal-400 to-green-400', initials: 'DK' },
      ],
      tasks: {
        total: 32,
        completed: 14,
        inProgress: 8,
        todo: 10
      },
      priority: 'high',
      category: 'Development'
    },
    {
      id: 3,
      name: 'Marketing Campaign Q1',
      description: 'Comprehensive marketing strategy for Q1 2024 product launch',
      status: 'planning',
      progress: 20,
      dueDate: '2024-04-01',
      team: [
        { id: 8, name: 'Rachel Green', avatar: 'bg-gradient-to-br from-pink-400 to-red-400', initials: 'RG' },
        { id: 9, name: 'Tom Brown', avatar: 'bg-gradient-to-br from-cyan-400 to-blue-400', initials: 'TB' },
      ],
      tasks: {
        total: 16,
        completed: 3,
        inProgress: 2,
        todo: 11
      },
      priority: 'medium',
      category: 'Marketing'
    },
    {
      id: 4,
      name: 'API Documentation',
      description: 'Complete API documentation with examples and integration guides',
      status: 'completed',
      progress: 100,
      dueDate: '2024-01-10',
      team: [
        { id: 10, name: 'Chris Lee', avatar: 'bg-gradient-to-br from-violet-400 to-purple-400', initials: 'CL' },
      ],
      tasks: {
        total: 12,
        completed: 12,
        inProgress: 0,
        todo: 0
      },
      priority: 'low',
      category: 'Documentation'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300'
      case 'planning': return 'bg-yellow-500/20 text-yellow-300'
      case 'completed': return 'bg-blue-500/20 text-blue-300'
      case 'on-hold': return 'bg-gray-500/20 text-gray-300'
      default: return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-300'
      case 'medium': return 'bg-yellow-500/20 text-yellow-300'
      case 'low': return 'bg-green-500/20 text-green-300'
      default: return 'bg-gray-500/20 text-gray-300'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Projects</h1>
          <p className="text-white/70 mt-1">Manage and track your team projects</p>
        </div>
        <Button className="bg-gradient-premium text-white border-0">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Project
        </Button>
      </div>

      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Total Projects</p>
              <p className="text-2xl font-bold text-white">{projects.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-premium rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Active</p>
              <p className="text-2xl font-bold text-white">{projects.filter(p => p.status === 'active').length}</p>
            </div>
            <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Completed</p>
              <p className="text-2xl font-bold text-white">{projects.filter(p => p.status === 'completed').length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="bg-task-card p-6 border border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Avg Progress</p>
              <p className="text-2xl font-bold text-white">{Math.round(projects.reduce((acc, p) => acc + p.progress, 0) / projects.length)}%</p>
            </div>
            <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {projects.map((project) => (
          <Card key={project.id} className="bg-project-card p-6 border border-white/10 hover:bg-white/5 transition-all duration-200">
            <div className="space-y-4">
              {/* Project Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-semibold text-white">{project.name}</h3>
                    <Badge className={`${getStatusColor(project.status)} border-0 capitalize`}>
                      {project.status}
                    </Badge>
                  </div>
                  <p className="text-white/70 text-sm mb-3">{project.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-white/60">
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span>Due {project.dueDate}</span>
                    </div>
                    <Badge className={`${getPriorityColor(project.priority)} border-0 capitalize text-xs`}>
                      {project.priority}
                    </Badge>
                    <Badge className="bg-white/10 text-white/80 border-0 text-xs">
                      {project.category}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Team Members */}
              <div className="flex items-center space-x-3">
                <span className="text-sm text-white/70">Team:</span>
                <div className="flex -space-x-2">
                  {project.team.slice(0, 4).map((member) => (
                    <div
                      key={member.id}
                      className={`w-8 h-8 ${member.avatar} rounded-full border-2 border-white flex items-center justify-center`}
                      title={member.name}
                    >
                      <span className="text-xs text-white font-medium">{member.initials}</span>
                    </div>
                  ))}
                  {project.team.length > 4 && (
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white flex items-center justify-center">
                      <span className="text-xs text-white font-medium">+{project.team.length - 4}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-white/70">Progress</span>
                  <span className="text-sm text-white/70">{project.progress}%</span>
                </div>
                <Progress value={project.progress} className="h-2" />
              </div>

              {/* Task Summary */}
              <div className="grid grid-cols-4 gap-4 pt-4 border-t border-white/10">
                <div className="text-center">
                  <p className="text-lg font-semibold text-white">{project.tasks.total}</p>
                  <p className="text-xs text-white/60">Total</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-green-400">{project.tasks.completed}</p>
                  <p className="text-xs text-white/60">Done</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-blue-400">{project.tasks.inProgress}</p>
                  <p className="text-xs text-white/60">Active</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-semibold text-gray-400">{project.tasks.todo}</p>
                  <p className="text-xs text-white/60">Todo</p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4">
                <Button variant="glass" size="sm">
                  View Details
                </Button>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </Button>
                  <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
