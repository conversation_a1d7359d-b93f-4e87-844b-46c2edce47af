// This file now only contains shared utilities and types
// For client-side operations, use supabase-client.ts
// For server-side operations, use supabase-server.ts

import { Database } from '../types/database'

// Shared types and utilities
export type SupabaseClient = any // You can define more specific types here if needed

// Environment validation
export const validateSupabaseConfig = () => {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
  }
  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
  }
}

// Re-export client functions for backwards compatibility
export { createSupabaseClient, createSupabaseAdminClient } from './supabase-client'

// Re-export server functions for API routes
export { createSupabaseServerClient } from './supabase-server'

