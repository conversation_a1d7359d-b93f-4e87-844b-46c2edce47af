import { NextRequest, NextResponse } from 'next/server'
import { heygenClient } from '@/lib/heygen'
import { createSupabaseServerClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get avatars from HeyGen
    const avatars = await heygenClient.getAvatars()

    return NextResponse.json({
      success: true,
      data: avatars
    })
  } catch (error) {
    console.error('Error fetching avatars:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch avatars' },
      { status: 500 }
    )
  }
}