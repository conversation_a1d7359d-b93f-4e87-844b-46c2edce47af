'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { LoadingSkeleton } from '@/components/ui/loading'
import { VideoPlayer } from '@/components/video/VideoPlayer'
import { VideoShareModal } from '@/components/video/VideoShareModal'

interface VideoDetails {
  id: string
  title: string
  description?: string
  status: 'processing' | 'completed' | 'failed'
  thumbnail_url?: string
  video_url?: string
  duration?: number
  created_at: string
  updated_at: string
  aspect_ratio: '16:9' | '9:16' | '1:1'
  avatar_name?: string
  voice_name?: string
  script: string
  view_count?: number
  file_size?: number
  share_url?: string
  is_public: boolean
}

interface VideoDetailsPageProps {
  params: { id: string }
}

export default function VideoDetailsPage({ params }: VideoDetailsPageProps) {
  const router = useRouter()
  const [video, setVideo] = useState<VideoDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    title: '',
    description: ''
  })
  const [showShareModal, setShowShareModal] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    loadVideo()
  }, [params.id])

  const loadVideo = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/videos/${params.id}`)
      const result = await response.json()

      if (result.success) {
        setVideo(result.data)
        setEditForm({
          title: result.data.title,
          description: result.data.description || ''
        })
      } else {
        throw new Error(result.error || 'Failed to load video')
      }
    } catch (error) {
      console.error('Failed to load video:', error)
      // Fallback with sample data for development
      const sampleVideo: VideoDetails = {
        id: params.id,
        title: 'Welcome to Our Product',
        description: 'This is a sample video description that explains the content and purpose of the video.',
        status: 'completed',
        thumbnail_url: '/api/placeholder/800/450',
        video_url: '/api/placeholder/video.mp4',
        duration: 45,
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:05:00Z',
        aspect_ratio: '16:9',
        avatar_name: 'Sarah',
        voice_name: 'English Female Professional',
        script: 'Welcome to our amazing platform! Today I want to show you how our revolutionary product can transform your business. With our cutting-edge AI technology, you can create professional videos in minutes, not hours. Let me walk you through the key features that make us the industry leader.',
        view_count: 156,
        file_size: 2.3 * 1024 * 1024,
        share_url: `https://yourdomain.com/share/${params.id}`,
        is_public: false
      }
      setVideo(sampleVideo)
      setEditForm({
        title: sampleVideo.title,
        description: sampleVideo.description || ''
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = async () => {
    if (!video) return

    try {
      const response = await fetch(`/api/videos/${video.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editForm)
      })

      if (response.ok) {
        const result = await response.json()
        setVideo(result.data)
        setIsEditing(false)
      } else {
        throw new Error('Failed to update video')
      }
    } catch (error) {
      console.error('Failed to update video:', error)
      alert('Failed to update video. Please try again.')
    }
  }

  const handleDelete = async () => {
    if (!video || !confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      return
    }

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/videos/${video.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        router.push('/videos')
      } else {
        throw new Error('Failed to delete video')
      }
    } catch (error) {
      console.error('Failed to delete video:', error)
      alert('Failed to delete video. Please try again.')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleTogglePublic = async () => {
    if (!video) return

    try {
      const response = await fetch(`/api/videos/${video.id}/visibility`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_public: !video.is_public
        })
      })

      if (response.ok) {
        const result = await response.json()
        setVideo(result.data)
      }
    } catch (error) {
      console.error('Failed to update visibility:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'processing': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <LoadingSkeleton className="h-8 w-64" />
          <LoadingSkeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <LoadingSkeleton className="aspect-video w-full" />
            <LoadingSkeleton className="h-48 w-full" />
          </div>
          <div className="space-y-6">
            <LoadingSkeleton className="h-64 w-full" />
            <LoadingSkeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (!video) {
    return (
      <div className="text-center py-12">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Video not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The video you're looking for doesn't exist or has been deleted.
        </p>
        <div className="mt-6">
          <Button onClick={() => router.push('/videos')}>
            Back to Videos
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/videos')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Videos
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{video.title}</h1>
            <div className="flex items-center space-x-2 mt-1">
              <Badge className={getStatusColor(video.status)}>
                {video.status}
              </Badge>
              <Badge variant="outline">
                {video.aspect_ratio}
              </Badge>
              {video.is_public && (
                <Badge variant="outline" className="bg-blue-100 text-blue-800">
                  Public
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowShareModal(true)}
            disabled={video.status !== 'completed'}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            Share
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsEditing(!isEditing)}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            {isEditing ? 'Cancel' : 'Edit'}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Deleting...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Video Player */}
          <Card>
            <CardContent className="p-0">
              {video.status === 'completed' && video.video_url ? (
                <VideoPlayer
                  videoUrl={video.video_url}
                  thumbnailUrl={video.thumbnail_url}
                  title={video.title}
                  aspectRatio={video.aspect_ratio}
                />
              ) : (
                <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                  {video.status === 'processing' ? (
                    <div className="text-center">
                      <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
                      <p className="text-gray-600">Video is being processed...</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-600">Video not available</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Video Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Title
                    </label>
                    <Input
                      value={editForm.title}
                      onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Enter video title"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Description
                    </label>
                    <Textarea
                      value={editForm.description}
                      onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter video description"
                      rows={4}
                    />
                  </div>
                  <div className="flex space-x-3">
                    <Button onClick={handleEdit}>
                      Save Changes
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">{video.title}</h3>
                    {video.description && (
                      <p className="text-gray-600">{video.description}</p>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    <p>Created: {formatDate(video.created_at)}</p>
                    <p>Last updated: {formatDate(video.updated_at)}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Script */}
          <Card>
            <CardHeader>
              <CardTitle>Script</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700 whitespace-pre-wrap">
                  {video.script}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Video Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Duration</div>
                  <div className="font-medium">
                    {video.duration ? formatDuration(video.duration) : 'N/A'}
                  </div>
                </div>
                <div>
                  <div className="text-gray-600">Views</div>
                  <div className="font-medium">{video.view_count || 0}</div>
                </div>
                <div>
                  <div className="text-gray-600">File Size</div>
                  <div className="font-medium">
                    {video.file_size ? formatFileSize(video.file_size) : 'N/A'}
                  </div>
                </div>
                <div>
                  <div className="text-gray-600">Format</div>
                  <div className="font-medium">{video.aspect_ratio}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Card>
            <CardHeader>
              <CardTitle>Technical Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {video.avatar_name && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Avatar:</span>
                  <span className="font-medium">{video.avatar_name}</span>
                </div>
              )}
              {video.voice_name && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Voice:</span>
                  <span className="font-medium">{video.voice_name}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Video ID:</span>
                <span className="font-mono text-xs">{video.id}</span>
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Public Access</div>
                  <div className="text-sm text-gray-600">
                    {video.is_public ? 'Anyone can view this video' : 'Only you can view this video'}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTogglePublic}
                >
                  {video.is_public ? 'Make Private' : 'Make Public'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {video.video_url && (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => window.open(video.video_url, '_blank')}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download Video
                </Button>
              )}
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowShareModal(true)}
                disabled={video.status !== 'completed'}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
                Share Video
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigator.clipboard.writeText(video.id)}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Copy Video ID
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Share Modal */}
      {showShareModal && video.share_url && (
        <VideoShareModal
          video={video}
          onClose={() => setShowShareModal(false)}
        />
      )}
    </div>
  )
}