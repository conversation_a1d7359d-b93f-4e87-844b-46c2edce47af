import { NextRequest, NextResponse } from 'next/server'

interface PlatformConfig {
  platform: string
  title: string
  description: string
  hashtags: string[]
}

interface OptimizationRequest {
  topic: string
  targetAudience: string
  videoStyle: string
  platforms: string[]
  researchData?: {
    tutorial_steps: string[]
    best_practices: string[]
    keywords: string[]
  }
}

const PLATFORM_TEMPLATES = {
  youtube: {
    maxTitle: 100,
    maxDescription: 5000,
    titleTemplate: '{topic} Tutorial - Complete Guide for {audience}s | {year}',
    descriptionTemplate: `🎯 In this {style}, you'll learn everything about {topic}.

Perfect for {audience} learners who want to master this topic quickly and efficiently.

📚 What you'll learn:
{tutorial_steps}

🔗 Useful resources:
• Documentation and guides
• Community forums and support
• Additional learning materials

⏰ Timestamps:
0:00 Introduction
1:30 Core concepts
3:00 Practical examples
5:00 Best practices
7:00 Conclusion

👍 Like and subscribe for more tutorials like this one!

#tutorial #education #learning`,
    hashtags: ['#tutorial', '#education', '#learning', '#howto', '#guide']
  },
  tiktok: {
    maxTitle: 150,
    maxDescription: 2200,
    titleTemplate: '{topic} in 60 seconds! Quick {style} for beginners ⚡',
    descriptionTemplate: `Quick {style} about {topic}! Perfect for {audience} learners 🔥

Follow for more quick tutorials! 

{hashtags}`,
    hashtags: ['#tutorial', '#quicktips', '#learning', '#fyp', '#viral']
  },
  linkedin: {
    maxTitle: 200,
    maxDescription: 3000,
    titleTemplate: 'Professional Guide: {topic} Best Practices for {audience} Professionals',
    descriptionTemplate: `In today's rapidly evolving landscape, understanding {topic} is crucial for {audience} professionals.

This {style} covers:
{tutorial_steps}

Key takeaways:
• Practical implementation strategies
• Industry best practices
• Common pitfalls to avoid

What's your experience with {topic}? Share your thoughts in the comments below.

#professional #business #learning`,
    hashtags: ['#professional', '#business', '#learning', '#skills', '#career']
  },
  twitter: {
    maxTitle: 280,
    maxDescription: 280,
    titleTemplate: '{topic} explained in {duration} minutes 🧵',
    descriptionTemplate: `🧵 Thread: Everything you need to know about {topic}

Perfect for {audience} looking to level up their skills

{hashtags}`,
    hashtags: ['#thread', '#learning', '#tips', '#tech']
  },
  facebook: {
    maxTitle: 255,
    maxDescription: 63206,
    titleTemplate: 'Learn {topic}: A Comprehensive Guide for Everyone',
    descriptionTemplate: `🎓 New tutorial alert! 

Today I'm sharing everything I know about {topic}. Whether you're a {audience} or just getting started, this {style} has something for you.

What you'll discover:
{tutorial_steps}

💡 Pro tip: Save this post for later reference!

What topics would you like me to cover next? Let me know in the comments! 👇

{hashtags}`,
    hashtags: ['#tutorial', '#education', '#learning', '#tips']
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      topic,
      targetAudience,
      videoStyle,
      platforms,
      researchData
    }: OptimizationRequest = await request.json()

    if (!topic || !targetAudience || !videoStyle || !platforms?.length) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const optimizedConfigs: PlatformConfig[] = platforms.map(platform => {
      const template = PLATFORM_TEMPLATES[platform as keyof typeof PLATFORM_TEMPLATES]
      
      if (!template) {
        return {
          platform,
          title: `${topic} - ${videoStyle}`,
          description: `Learn about ${topic} in this ${videoStyle} for ${targetAudience} learners.`,
          hashtags: ['#tutorial', '#learning']
        }
      }

      // Generate optimized title
      const title = template.titleTemplate
        .replace('{topic}', topic)
        .replace('{audience}', targetAudience)
        .replace('{style}', videoStyle)
        .replace('{year}', new Date().getFullYear().toString())
        .slice(0, template.maxTitle)

      // Generate tutorial steps text
      const tutorialStepsText = researchData?.tutorial_steps
        ?.slice(0, 5)
        .map((step, index) => `• ${step}`)
        .join('\n') || '• Key concepts and fundamentals\n• Practical implementation\n• Best practices and tips'

      // Generate description
      let description = template.descriptionTemplate
        .replace('{topic}', topic)
        .replace('{audience}', targetAudience)
        .replace('{style}', videoStyle)
        .replace('{tutorial_steps}', tutorialStepsText)

      // Add hashtags to description for platforms that include them
      if (platform === 'tiktok' || platform === 'twitter' || platform === 'facebook') {
        const hashtagsText = template.hashtags.join(' ')
        description = description.replace('{hashtags}', hashtagsText)
      }

      description = description.slice(0, template.maxDescription)

      // Generate platform-specific hashtags
      let platformHashtags = [...template.hashtags]
      
      // Add topic-specific hashtags
      const topicWords = topic.toLowerCase().split(' ')
      if (topicWords.some(word => ['tech', 'programming', 'code', 'software'].includes(word))) {
        platformHashtags.push('#tech', '#programming', '#coding')
      }
      if (topicWords.some(word => ['business', 'marketing', 'sales'].includes(word))) {
        platformHashtags.push('#business', '#marketing', '#entrepreneur')
      }
      if (topicWords.some(word => ['design', 'ui', 'ux', 'creative'].includes(word))) {
        platformHashtags.push('#design', '#creative', '#ui')
      }

      // Add research keywords as hashtags
      if (researchData?.keywords) {
        const keywordHashtags = researchData.keywords
          .slice(0, 3)
          .map(keyword => `#${keyword.replace(/\s+/g, '').toLowerCase()}`)
        platformHashtags.push(...keywordHashtags)
      }

      // Remove duplicates and limit count
      platformHashtags = [...new Set(platformHashtags)].slice(0, platform === 'tiktok' ? 5 : 3)

      return {
        platform,
        title,
        description,
        hashtags: platformHashtags
      }
    })

    // Generate SEO keywords
    const seoKeywords = [
      topic,
      `${topic} tutorial`,
      `${topic} guide`,
      `${topic} ${targetAudience}`,
      `${topic} ${videoStyle}`,
      `learn ${topic}`,
      `${topic} tips`,
      ...(researchData?.keywords || [])
    ].filter((keyword, index, arr) => 
      keyword && 
      keyword.length > 2 && 
      arr.indexOf(keyword) === index
    ).slice(0, 10)

    return NextResponse.json({
      success: true,
      data: {
        optimizedConfigs,
        seoKeywords,
        metadata: {
          optimizedAt: new Date().toISOString(),
          platformCount: platforms.length,
          totalHashtags: optimizedConfigs.reduce((sum, config) => sum + config.hashtags.length, 0)
        }
      }
    })

  } catch (error) {
    console.error('Content optimization error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}