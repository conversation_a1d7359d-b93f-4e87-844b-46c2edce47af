// Enhanced Workflow Types
// These types correspond to the enhanced database schema

export interface VideoProject {
  id: string
  user_id: string
  
  // Project metadata
  project_name: string
  topic: string
  target_audience: 'beginner' | 'intermediate' | 'advanced' | 'mixed'
  video_style: 'explainer' | 'tutorial' | 'review' | 'comparison' | 'walkthrough'
  
  // Research data
  researched_content?: ResearchedContent
  
  // Script and content
  title?: string
  script?: string
  script_segments?: ScriptSegment[]
  
  // Avatar and voice
  avatar_id?: string
  voice_id?: string
  custom_branding?: CustomBranding
  
  // Visual content
  visual_content?: VisualContent
  
  // Video settings
  aspect_ratio: '16:9' | '9:16' | '1:1'
  background?: Background
  timeline_settings?: TimelineSettings
  
  // Publishing configuration
  platforms: string[]
  publishing_config?: PublishingConfig
  
  // Project status
  status: 'draft' | 'in_progress' | 'completed' | 'published' | 'archived'
  workflow_step: number
  
  // Timestamps
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface ResearchedContent {
  documentation_links: string[]
  tutorial_steps: string[]
  common_issues: string[]
  best_practices: string[]
  estimated_difficulty: 'beginner' | 'intermediate' | 'advanced'
  keywords?: string[]
  related_topics?: string[]
}

export interface ScriptSegment {
  id: string
  type: 'intro' | 'step' | 'tip' | 'outro'
  content: string
  visual_cues: string[]
  estimated_duration: number
}

export interface CustomBranding {
  logo_url?: string
  brand_colors: {
    primary: string
    secondary: string
    accent: string
  }
  watermark_position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export interface VisualContent {
  type: 'upload' | 'ai_generated'
  screen_recording?: ScreenRecording
  ai_walkthrough?: AIWalkthrough
}

export interface ScreenRecording {
  file_url: string
  duration: number
  file_size: number
  filename: string
  scenes: VideoScene[]
}

export interface VideoScene {
  start_time: number
  end_time: number
  title: string
  annotations: Annotation[]
}

export interface Annotation {
  type: 'highlight' | 'arrow' | 'text' | 'callout'
  position: { x: number; y: number }
  content?: string
}

export interface AIWalkthrough {
  website_url: string
  automation_steps: AutomationStep[]
}

export interface AutomationStep {
  action: 'click' | 'type' | 'hover' | 'scroll' | 'wait'
  selector: string
  value?: string
  description: string
}

export interface Background {
  type: 'color' | 'image'
  value: string
}

export interface TimelineSettings {
  avatar_position: 'side' | 'corner' | 'overlay' | 'fullscreen'
  avatar_size: 'small' | 'medium' | 'large'
  transition_style: 'cut' | 'fade' | 'slide' | 'zoom'
  background_music?: string
}

export interface PublishingConfig {
  platforms: PlatformConfig[]
  seo_optimization: SEOOptimization
}

export interface PlatformConfig {
  platform: string
  title: string
  description: string
  hashtags: string[]
  scheduled_time?: Date
  platform_specific?: Record<string, any>
}

export interface SEOOptimization {
  keywords: string[]
  thumbnail_style: 'auto' | 'custom'
  custom_thumbnail_url?: string
}

// Database table types
export interface VideoResearch {
  id: string
  project_id: string
  
  topic: string
  research_type: string
  
  documentation_links: string[]
  tutorial_steps: string[]
  common_issues: string[]
  best_practices: string[]
  keywords: string[]
  related_topics: string[]
  estimated_difficulty: 'beginner' | 'intermediate' | 'advanced'
  
  confidence_score: number
  source_count: number
  
  created_at: string
  updated_at: string
}

export interface VideoAutomationStep {
  id: string
  project_id: string
  
  website_url: string
  step_order: number
  
  action: 'click' | 'type' | 'hover' | 'scroll' | 'wait'
  selector: string
  value?: string
  description: string
  
  estimated_duration: number
  screenshot_url?: string
  
  created_at: string
}

export interface VideoPublishingPlatform {
  id: string
  project_id: string
  
  platform: 'youtube' | 'tiktok' | 'linkedin' | 'twitter' | 'facebook' | 'instagram'
  
  title: string
  description?: string
  hashtags: string[]
  
  scheduled_time?: string
  publish_status: 'draft' | 'scheduled' | 'published' | 'failed'
  
  platform_settings?: Record<string, any>
  
  published_url?: string
  platform_post_id?: string
  engagement_metrics?: Record<string, any>
  
  created_at: string
  updated_at: string
  published_at?: string
}

export interface VideoAnalytics {
  id: string
  project_id: string
  video_id?: string
  
  platform?: string
  metric_date: string
  
  views: number
  likes: number
  comments: number
  shares: number
  watch_time_seconds: number
  engagement_rate: number
  
  unique_viewers: number
  subscriber_gain: number
  click_through_rate: number
  
  platform_metrics?: Record<string, any>
  
  created_at: string
  updated_at: string
}

export interface WorkflowTemplate {
  id: string
  user_id: string
  
  template_name: string
  description?: string
  category?: string
  is_public: boolean
  usage_count: number
  
  template_data: Record<string, any>
  
  created_at: string
  updated_at: string
}

// View types
export interface ProjectSummary {
  id: string
  user_id: string
  project_name: string
  topic: string
  target_audience: string
  video_style: string
  status: string
  workflow_step: number
  created_at: string
  updated_at: string
  configured_platforms: number
  analytics_records: number
  progress_status: 'Completed' | 'Ready to Generate' | 'In Progress' | 'Getting Started'
}

// API Response types
export interface ResearchApiResponse {
  success: boolean
  data?: ResearchedContent
  error?: string
}

export interface AutomationApiResponse {
  success: boolean
  data?: {
    websiteUrl: string
    automationSteps: AutomationStep[]
    metadata: {
      estimatedDuration: number
      complexity: string
      generatedAt: string
    }
  }
  error?: string
}

export interface PublishingOptimizationResponse {
  success: boolean
  data?: {
    optimizedConfigs: PlatformConfig[]
    seoKeywords: string[]
    metadata: {
      optimizedAt: string
      platformCount: number
      totalHashtags: number
    }
  }
  error?: string
}

export interface SchedulingResponse {
  success: boolean
  data?: {
    videoId: string
    scheduledJobs: ScheduledJob[]
    recommendations: Record<string, OptimalPostingTime>
    summary: {
      totalPlatforms: number
      earliestPublish: string
      latestPublish: string
    }
  }
  error?: string
}

export interface ScheduledJob {
  platform: string
  scheduledTime: string
  jobId: string
  status: string
  estimatedPublishTime: string
}

export interface OptimalPostingTime {
  bestTimes: string[]
  bestDays: string[]
  timeZone: string
}

// Enhanced database types that extend the existing database schema
export interface EnhancedDatabase {
  public: {
    Tables: {
      video_projects: {
        Row: VideoProject
        Insert: Omit<VideoProject, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<VideoProject, 'id' | 'user_id' | 'created_at'>>
      }
      video_research: {
        Row: VideoResearch
        Insert: Omit<VideoResearch, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<VideoResearch, 'id' | 'project_id' | 'created_at'>>
      }
      video_automation_steps: {
        Row: VideoAutomationStep
        Insert: Omit<VideoAutomationStep, 'id' | 'created_at'>
        Update: Partial<Omit<VideoAutomationStep, 'id' | 'project_id' | 'created_at'>>
      }
      video_publishing_platforms: {
        Row: VideoPublishingPlatform
        Insert: Omit<VideoPublishingPlatform, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<VideoPublishingPlatform, 'id' | 'project_id' | 'created_at'>>
      }
      video_analytics: {
        Row: VideoAnalytics
        Insert: Omit<VideoAnalytics, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<VideoAnalytics, 'id' | 'project_id' | 'created_at'>>
      }
      workflow_templates: {
        Row: WorkflowTemplate
        Insert: Omit<WorkflowTemplate, 'id' | 'created_at' | 'updated_at' | 'usage_count'>
        Update: Partial<Omit<WorkflowTemplate, 'id' | 'user_id' | 'created_at'>>
      }
    }
    Views: {
      project_summary: {
        Row: ProjectSummary
      }
    }
    Functions: {
      // Add any custom database functions here
    }
    Enums: {
      // Add any custom enums here
    }
  }
}