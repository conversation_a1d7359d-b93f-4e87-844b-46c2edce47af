'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'

interface Video {
  id: string
  title: string
  status: 'processing' | 'completed' | 'failed'
  thumbnail_url?: string
  video_url?: string
  duration?: number
  created_at: string
  updated_at: string
  aspect_ratio: '16:9' | '9:16' | '1:1'
  avatar_name?: string
  view_count?: number
  file_size?: number
}

interface VideoLibraryProps {
  videos: Video[]
  viewMode: 'grid' | 'list'
  onVideoUpdate?: () => void
}

export function VideoLibrary({ videos, viewMode, onVideoUpdate }: VideoLibraryProps) {
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set())

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'processing': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleVideoAction = async (action: string, videoId: string) => {
    try {
      const response = await fetch(`/api/videos/${videoId}/${action}`, {
        method: 'POST'
      })

      if (response.ok) {
        onVideoUpdate?.()
      }
    } catch (error) {
      console.error(`Failed to ${action} video:`, error)
    }
  }

  const handleBulkAction = async (action: string) => {
    try {
      const response = await fetch(`/api/videos/bulk/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_ids: Array.from(selectedVideos)
        })
      })

      if (response.ok) {
        setSelectedVideos(new Set())
        onVideoUpdate?.()
      }
    } catch (error) {
      console.error(`Failed to ${action} videos:`, error)
    }
  }

  const toggleVideoSelection = (videoId: string) => {
    const newSelection = new Set(selectedVideos)
    if (newSelection.has(videoId)) {
      newSelection.delete(videoId)
    } else {
      newSelection.add(videoId)
    }
    setSelectedVideos(newSelection)
  }

  const selectAllVideos = () => {
    if (selectedVideos.size === videos.length) {
      setSelectedVideos(new Set())
    } else {
      setSelectedVideos(new Set(videos.map(v => v.id)))
    }
  }

  if (viewMode === 'grid') {
    return (
      <div className="space-y-4">
        {/* Bulk Actions */}
        {selectedVideos.size > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedVideos.size} video{selectedVideos.size > 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedVideos(new Set())}
              >
                Clear Selection
              </Button>
            </div>
          </div>
        )}

        {/* Grid View */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {videos.map((video) => (
            <Card
              key={video.id}
              className={`group cursor-pointer transition-all hover:shadow-lg ${
                selectedVideos.has(video.id) ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              <CardContent className="p-0">
                {/* Thumbnail */}
                <div className="relative">
                  {video.thumbnail_url ? (
                    <img
                      src={video.thumbnail_url}
                      alt={video.title}
                      className="w-full h-48 object-cover rounded-t-lg"
                      onError={(e) => {
                        e.currentTarget.src = '/api/placeholder/320/180'
                      }}
                    />
                  ) : (
                    <div className="w-full h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
                      <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}

                  {/* Status Badge */}
                  <div className="absolute top-2 left-2">
                    <Badge className={getStatusColor(video.status)}>
                      {video.status}
                    </Badge>
                  </div>

                  {/* Duration */}
                  {video.duration && (
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                      {formatDuration(video.duration)}
                    </div>
                  )}

                  {/* Selection Checkbox */}
                  <div className="absolute top-2 right-2">
                    <input
                      type="checkbox"
                      className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
                      checked={selectedVideos.has(video.id)}
                      onChange={() => toggleVideoSelection(video.id)}
                    />
                  </div>

                  {/* Overlay Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex space-x-2">
                      {video.status === 'completed' && video.video_url && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={(e) => {
                            e.stopPropagation()
                            window.open(video.video_url, '_blank')
                          }}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                          </svg>
                        </Button>
                      )}
                      <Link href={`/videos/${video.id}`}>
                        <Button size="sm" variant="secondary">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Video Info */}
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">
                    {video.title}
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>{formatDate(video.created_at)}</span>
                    <span>{video.aspect_ratio}</span>
                  </div>
                  {video.avatar_name && (
                    <div className="text-xs text-gray-500 mb-2">
                      Avatar: {video.avatar_name}
                    </div>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    {video.view_count !== undefined && (
                      <span>{video.view_count} views</span>
                    )}
                    {video.file_size && (
                      <span>{formatFileSize(video.file_size)}</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // List View
  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedVideos.size > 0 && (
        <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <span className="text-sm text-blue-700">
            {selectedVideos.size} video{selectedVideos.size > 1 ? 's' : ''} selected
          </span>
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkAction('delete')}
            >
              Delete
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSelectedVideos(new Set())}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* List Header */}
      <Card>
        <CardContent className="p-3">
          <div className="flex items-center text-sm font-medium text-gray-700">
            <div className="w-8">
              <input
                type="checkbox"
                className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
                checked={selectedVideos.size === videos.length && videos.length > 0}
                onChange={selectAllVideos}
              />
            </div>
            <div className="flex-1 grid grid-cols-12 gap-4">
              <div className="col-span-4">Title</div>
              <div className="col-span-2">Status</div>
              <div className="col-span-2">Created</div>
              <div className="col-span-2">Duration</div>
              <div className="col-span-1">Views</div>
              <div className="col-span-1">Actions</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* List Items */}
      <div className="space-y-2">
        {videos.map((video) => (
          <Card
            key={video.id}
            className={`hover:shadow-md transition-all ${
              selectedVideos.has(video.id) ? 'ring-2 ring-blue-500' : ''
            }`}
          >
            <CardContent className="p-3">
              <div className="flex items-center">
                <div className="w-8">
                  <input
                    type="checkbox"
                    className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500"
                    checked={selectedVideos.has(video.id)}
                    onChange={() => toggleVideoSelection(video.id)}
                  />
                </div>
                <div className="flex-1 grid grid-cols-12 gap-4 items-center">
                  <div className="col-span-4 flex items-center space-x-3">
                    {video.thumbnail_url ? (
                      <img
                        src={video.thumbnail_url}
                        alt={video.title}
                        className="w-16 h-10 object-cover rounded"
                        onError={(e) => {
                          e.currentTarget.src = '/api/placeholder/80/45'
                        }}
                      />
                    ) : (
                      <div className="w-16 h-10 bg-gray-200 rounded flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                    <div>
                      <div className="font-medium text-gray-900">{video.title}</div>
                      <div className="text-xs text-gray-500">{video.aspect_ratio}</div>
                    </div>
                  </div>
                  <div className="col-span-2">
                    <Badge className={getStatusColor(video.status)}>
                      {video.status}
                    </Badge>
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {formatDate(video.created_at)}
                  </div>
                  <div className="col-span-2 text-sm text-gray-600">
                    {video.duration ? formatDuration(video.duration) : '-'}
                  </div>
                  <div className="col-span-1 text-sm text-gray-600">
                    {video.view_count || 0}
                  </div>
                  <div className="col-span-1">
                    <div className="flex space-x-1">
                      {video.status === 'completed' && video.video_url && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => window.open(video.video_url, '_blank')}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                          </svg>
                        </Button>
                      )}
                      <Link href={`/videos/${video.id}`}>
                        <Button size="sm" variant="ghost">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}