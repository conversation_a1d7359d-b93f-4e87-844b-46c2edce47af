'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON>lider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { LoadingSpinner } from '@/components/ui/loading'
import { GestureIntelligence } from '@/types/humanization'

interface GestureIntelligenceEngineProps {
  script: string
  duration: number
  avatarPosition: 'side' | 'corner' | 'overlay' | 'fullscreen'
  onGesturesGenerated: (gestures: GestureIntelligence[]) => void
  existingGestures?: GestureIntelligence[]
}

interface GestureSettings {
  frequency: number // gestures per minute
  naturalness: number // 0.1 to 1.0
  contextSensitivity: number // 0.1 to 1.0
  enableHandGestures: boolean
  enableHeadMovements: boolean
  enableBodyLanguage: boolean
  enablePointingGestures: boolean
  gestureIntensity: 'subtle' | 'moderate' | 'pronounced'
  coordinateWithVoice: boolean
}

const GESTURE_PATTERNS = {
  hand_movement: {
    contexts: ['explanation', 'enumeration', 'description', 'emphasis'],
    keywords: ['first', 'second', 'next', 'then', 'also', 'important', 'key', 'main'],
    minDuration: 1.5,
    maxDuration: 3.0,
    naturalFrequency: 4 // per minute
  },
  head_gesture: {
    contexts: ['agreement', 'emphasis', 'transition', 'engagement'],
    keywords: ['yes', 'exactly', 'right', 'correct', 'absolutely', 'definitely'],
    minDuration: 0.8,
    maxDuration: 1.5,
    naturalFrequency: 6 // per minute
  },
  body_lean: {
    contexts: ['emphasis', 'intimacy', 'engagement', 'explanation'],
    keywords: ['listen', 'important', 'secret', 'personal', 'between us', 'confidential'],
    minDuration: 2.0,
    maxDuration: 4.0,
    naturalFrequency: 2 // per minute
  },
  finger_point: {
    contexts: ['direction', 'specific', 'identification', 'emphasis'],
    keywords: ['this', 'that', 'here', 'there', 'see', 'look', 'notice', 'specific'],
    minDuration: 1.0,
    maxDuration: 2.0,
    naturalFrequency: 3 // per minute
  },
  open_palm: {
    contexts: ['welcome', 'inclusion', 'honesty', 'openness'],
    keywords: ['welcome', 'everyone', 'together', 'honest', 'truth', 'open', 'transparent'],
    minDuration: 1.5,
    maxDuration: 2.5,
    naturalFrequency: 2 // per minute
  },
  thinking_pose: {
    contexts: ['contemplation', 'problem', 'consideration', 'pause'],
    keywords: ['think', 'consider', 'problem', 'challenge', 'difficult', 'complex', 'hmm'],
    minDuration: 2.0,
    maxDuration: 3.5,
    naturalFrequency: 1.5 // per minute
  }
}

const GESTURE_COORDINATION_RULES = {
  // When to coordinate gestures with voice emphasis
  voice_emphasis: {
    hand_movement: 0.8, // 80% chance to coordinate
    head_gesture: 0.9,
    body_lean: 0.6,
    finger_point: 0.95,
    open_palm: 0.7,
    thinking_pose: 0.3
  },
  // Timing offset from voice emphasis (seconds)
  timing_offset: {
    hand_movement: -0.2, // start slightly before voice
    head_gesture: 0.1, // start slightly after voice
    body_lean: -0.5,
    finger_point: -0.1,
    open_palm: -0.3,
    thinking_pose: 0.0
  }
}

const AVATAR_POSITION_CONSTRAINTS = {
  side: {
    hand_movement: 1.0, // full visibility
    head_gesture: 1.0,
    body_lean: 0.8, // partially visible
    finger_point: 0.9,
    open_palm: 0.9,
    thinking_pose: 0.7
  },
  corner: {
    hand_movement: 0.6, // limited space
    head_gesture: 1.0,
    body_lean: 0.4,
    finger_point: 0.7,
    open_palm: 0.6,
    thinking_pose: 0.5
  },
  overlay: {
    hand_movement: 0.9,
    head_gesture: 1.0,
    body_lean: 0.7,
    finger_point: 0.8,
    open_palm: 0.8,
    thinking_pose: 0.6
  },
  fullscreen: {
    hand_movement: 1.0, // full visibility and space
    head_gesture: 1.0,
    body_lean: 1.0,
    finger_point: 1.0,
    open_palm: 1.0,
    thinking_pose: 1.0
  }
}

export function GestureIntelligenceEngine({
  script,
  duration,
  avatarPosition,
  onGesturesGenerated,
  existingGestures = []
}: GestureIntelligenceEngineProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [settings, setSettings] = useState<GestureSettings>({
    frequency: 6, // gestures per minute
    naturalness: 0.8,
    contextSensitivity: 0.9,
    enableHandGestures: true,
    enableHeadMovements: true,
    enableBodyLanguage: true,
    enablePointingGestures: true,
    gestureIntensity: 'moderate',
    coordinateWithVoice: true
  })
  
  const [generatedGestures, setGeneratedGestures] = useState<GestureIntelligence[]>(existingGestures)
  const [gestureAnalysis, setGestureAnalysis] = useState<{
    contextMatches: Array<{ context: string; keywords: string[]; timing: number }>
    gestureOpportunities: number
    coordinationPoints: number
  } | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  // Analyze script for gesture opportunities
  const analyzeGestureContext = useCallback((text: string) => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const contextMatches: Array<{ context: string; keywords: string[]; timing: number }> = []
    
    sentences.forEach((sentence, index) => {
      const timePerSentence = duration / sentences.length
      const timing = index * timePerSentence
      
      // Check each gesture pattern for keyword matches
      Object.entries(GESTURE_PATTERNS).forEach(([gestureType, pattern]) => {
        const foundKeywords = pattern.keywords.filter(keyword =>
          sentence.toLowerCase().includes(keyword.toLowerCase())
        )
        
        if (foundKeywords.length > 0) {
          // Determine context based on sentence structure and keywords
          let context = 'general'
          if (/[!]/.test(sentence)) context = 'emphasis'
          else if (/[?]/.test(sentence)) context = 'engagement'
          else if (/first|second|then|next/.test(sentence.toLowerCase())) context = 'enumeration'
          else if (/explain|show|demonstrate/.test(sentence.toLowerCase())) context = 'explanation'
          
          contextMatches.push({
            context: gestureType,
            keywords: foundKeywords,
            timing: timing
          })
        }
      })
    })
    
    // Find emphasis markers for coordination
    const emphasisMarkers = Array.from(script.matchAll(/\[EMPHASIS\]/g))
    const coordinationPoints = emphasisMarkers.length
    
    return {
      contextMatches,
      gestureOpportunities: contextMatches.length,
      coordinationPoints
    }
  }, [script, duration])

  // Generate intelligent gestures based on context analysis
  const generateGestures = useCallback(async () => {
    setIsGenerating(true)
    
    try {
      const analysis = analyzeGestureContext(script)
      setGestureAnalysis(analysis)
      
      const gestures: GestureIntelligence[] = []
      const positionConstraints = AVATAR_POSITION_CONSTRAINTS[avatarPosition]
      
      // Generate context-aware gestures
      analysis.contextMatches.forEach((match, index) => {
        const gestureType = match.context as keyof typeof GESTURE_PATTERNS
        const pattern = GESTURE_PATTERNS[gestureType]
        
        // Check if this gesture type is enabled
        if (!isGestureTypeEnabled(gestureType)) return
        
        // Apply position constraints
        const positionMultiplier = positionConstraints[gestureType] || 1.0
        if (Math.random() > positionMultiplier * settings.contextSensitivity) return
        
        // Calculate gesture timing and duration
        const baseTime = match.timing
        const jitter = (Math.random() - 0.5) * 1.0 // Add natural timing variation
        const timing = Math.max(0, Math.min(duration - pattern.maxDuration, baseTime + jitter))
        
        const duration_gesture = pattern.minDuration + 
          (Math.random() * (pattern.maxDuration - pattern.minDuration))
        
        // Determine intensity based on context and settings
        const intensityMultiplier = getIntensityMultiplier()
        const baseIntensity = match.keywords.length * 0.3 + 0.4 // base on keyword relevance
        const finalIntensity = Math.min(1.0, baseIntensity * intensityMultiplier * settings.naturalness)
        
        // Create gesture
        const gesture: GestureIntelligence = {
          id: `gesture_${Date.now()}_${index}`,
          timing: timing,
          duration: duration_gesture,
          gestureType: gestureType,
          context: script.substring(
            Math.max(0, Math.floor(timing * 10)), 
            Math.min(script.length, Math.floor((timing + duration_gesture) * 10))
          ),
          intensity: finalIntensity > 0.7 ? 'pronounced' : 
                   finalIntensity > 0.4 ? 'moderate' : 'subtle',
          coordination: {
            matches_voice_emphasis: isNearEmphasisMarker(timing),
            relates_to_content: true,
            natural_flow: calculateNaturalFlow(timing, gestures)
          }
        }
        
        gestures.push(gesture)
      })
      
      // Generate additional natural gestures to reach target frequency
      const targetGestureCount = Math.floor((duration / 60) * settings.frequency)
      const additionalGesturesNeeded = Math.max(0, targetGestureCount - gestures.length)
      
      for (let i = 0; i < additionalGesturesNeeded; i++) {
        const randomTime = Math.random() * (duration - 3)
        
        // Avoid overlapping with existing gestures
        if (gestures.some(g => Math.abs(g.timing - randomTime) < 2)) continue
        
        // Select random gesture type based on enabled settings
        const enabledGestureTypes = getEnabledGestureTypes()
        if (enabledGestureTypes.length === 0) break
        
        const randomGestureType = enabledGestureTypes[
          Math.floor(Math.random() * enabledGestureTypes.length)
        ] as keyof typeof GESTURE_PATTERNS
        
        const pattern = GESTURE_PATTERNS[randomGestureType]
        
        // Apply position constraints
        const positionMultiplier = positionConstraints[randomGestureType] || 1.0
        if (Math.random() > positionMultiplier) continue
        
        const gesture: GestureIntelligence = {
          id: `natural_gesture_${Date.now()}_${i}`,
          timing: randomTime,
          duration: pattern.minDuration + (Math.random() * (pattern.maxDuration - pattern.minDuration)),
          gestureType: randomGestureType,
          context: 'natural_variation',
          intensity: settings.gestureIntensity,
          coordination: {
            matches_voice_emphasis: false,
            relates_to_content: false,
            natural_flow: true
          }
        }
        
        gestures.push(gesture)
      }
      
      // Sort by timing and apply natural flow adjustments
      gestures.sort((a, b) => a.timing - b.timing)
      
      // Adjust for natural flow (avoid clustering)
      const adjustedGestures = adjustGestureFlow(gestures)
      
      setGeneratedGestures(adjustedGestures)
      onGesturesGenerated(adjustedGestures)
      
    } catch (error) {
      console.error('Failed to generate gestures:', error)
      alert('Failed to generate gestures. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }, [script, duration, avatarPosition, settings])

  // Helper functions
  const isGestureTypeEnabled = (gestureType: string): boolean => {
    switch (gestureType) {
      case 'hand_movement': return settings.enableHandGestures
      case 'head_gesture': return settings.enableHeadMovements
      case 'body_lean': return settings.enableBodyLanguage
      case 'finger_point': return settings.enablePointingGestures
      case 'open_palm': return settings.enableHandGestures
      case 'thinking_pose': return settings.enableBodyLanguage
      default: return true
    }
  }

  const getIntensityMultiplier = (): number => {
    switch (settings.gestureIntensity) {
      case 'subtle': return 0.6
      case 'moderate': return 1.0
      case 'pronounced': return 1.4
      default: return 1.0
    }
  }

  const isNearEmphasisMarker = (timing: number): boolean => {
    const emphasisMarkers = Array.from(script.matchAll(/\[EMPHASIS\]/g))
    return emphasisMarkers.some(match => {
      const relativePosition = (match.index || 0) / script.length
      const emphasisTime = relativePosition * duration
      return Math.abs(emphasisTime - timing) < 2 // within 2 seconds
    })
  }

  const calculateNaturalFlow = (timing: number, existingGestures: GestureIntelligence[]): boolean => {
    // Check if this gesture fits naturally with others
    const nearbyGestures = existingGestures.filter(g => Math.abs(g.timing - timing) < 3)
    return nearbyGestures.length < 2 // avoid clustering more than 2 gestures
  }

  const getEnabledGestureTypes = (): string[] => {
    const types: string[] = []
    if (settings.enableHandGestures) types.push('hand_movement', 'open_palm')
    if (settings.enableHeadMovements) types.push('head_gesture')
    if (settings.enableBodyLanguage) types.push('body_lean', 'thinking_pose')
    if (settings.enablePointingGestures) types.push('finger_point')
    return types
  }

  const adjustGestureFlow = (gestures: GestureIntelligence[]): GestureIntelligence[] => {
    // Ensure natural spacing between gestures
    const minGap = 1.5 // minimum seconds between gestures
    const adjusted = [...gestures]
    
    for (let i = 1; i < adjusted.length; i++) {
      const prevGesture = adjusted[i - 1]
      const currentGesture = adjusted[i]
      
      if (currentGesture.timing - (prevGesture.timing + prevGesture.duration) < minGap) {
        // Adjust timing to maintain natural flow
        adjusted[i] = {
          ...currentGesture,
          timing: prevGesture.timing + prevGesture.duration + minGap
        }
      }
    }
    
    return adjusted.filter(g => g.timing + g.duration <= duration) // Remove gestures that exceed duration
  }

  const clearGestures = () => {
    setGeneratedGestures([])
    setGestureAnalysis(null)
    onGesturesGenerated([])
  }

  const gestureStats = {
    totalGestures: generatedGestures.length,
    handGestures: generatedGestures.filter(g => ['hand_movement', 'open_palm', 'finger_point'].includes(g.gestureType)).length,
    headGestures: generatedGestures.filter(g => g.gestureType === 'head_gesture').length,
    bodyLanguage: generatedGestures.filter(g => ['body_lean', 'thinking_pose'].includes(g.gestureType)).length,
    contextualGestures: generatedGestures.filter(g => g.coordination.relates_to_content).length,
    gesturesPerMinute: (generatedGestures.length / duration) * 60
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Context-Aware Gesture Intelligence</h3>
        <p className="text-sm text-gray-600">
          Generate natural, context-aware gestures that coordinate with speech content and voice emphasis
        </p>
      </div>

      {/* Gesture Settings Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Gesture Intelligence Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Gesture Frequency: {settings.frequency}/min
              </label>
              <Slider
                value={[settings.frequency]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, frequency: value }))}
                min={2}
                max={15}
                step={1}
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">Number of gestures per minute</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Naturalness: {Math.round(settings.naturalness * 100)}%
              </label>
              <Slider
                value={[settings.naturalness]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, naturalness: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">How natural gestures should appear</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Context Sensitivity: {Math.round(settings.contextSensitivity * 100)}%
              </label>
              <Slider
                value={[settings.contextSensitivity]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, contextSensitivity: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">How much content context influences gestures</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Gesture Intensity
              </label>
              <select
                value={settings.gestureIntensity}
                onChange={(e) => setSettings(prev => ({ ...prev, gestureIntensity: e.target.value as any }))}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="subtle">Subtle</option>
                <option value="moderate">Moderate</option>
                <option value="pronounced">Pronounced</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Overall intensity of gestures</p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Hand Gestures</label>
                <Switch
                  checked={settings.enableHandGestures}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableHandGestures: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Head Movements</label>
                <Switch
                  checked={settings.enableHeadMovements}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableHeadMovements: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Body Language</label>
                <Switch
                  checked={settings.enableBodyLanguage}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableBodyLanguage: checked }))}
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Pointing Gestures</label>
                <Switch
                  checked={settings.enablePointingGestures}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enablePointingGestures: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Coordinate with Voice</label>
                <Switch
                  checked={settings.coordinateWithVoice}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, coordinateWithVoice: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Avatar Position Info */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 mb-2">
            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium text-blue-800">Avatar Position: {avatarPosition}</span>
          </div>
          <p className="text-xs text-blue-700">
            {avatarPosition === 'fullscreen' && 'All gesture types fully visible and effective'}
            {avatarPosition === 'side' && 'Most gestures visible, body language partially limited'}
            {avatarPosition === 'corner' && 'Hand gestures limited by space, head movements optimal'}
            {avatarPosition === 'overlay' && 'Good balance of gesture visibility and effectiveness'}
          </p>
        </CardContent>
      </Card>

      {/* Generation Controls */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={generateGestures}
          disabled={isGenerating || !script.trim()}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
        >
          {isGenerating ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Analyzing Gesture Context...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
              </svg>
              Generate Intelligent Gestures
            </>
          )}
        </Button>
        
        {generatedGestures.length > 0 && (
          <Button variant="outline" onClick={clearGestures}>
            Clear Gestures
          </Button>
        )}
        
        <Button
          variant="outline"
          onClick={() => setPreviewMode(!previewMode)}
          disabled={generatedGestures.length === 0}
        >
          {previewMode ? 'Hide Preview' : 'Preview Timeline'}
        </Button>
      </div>

      {/* Generated Results */}
      {generatedGestures.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Generated Gesture Intelligence</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{gestureStats.totalGestures}</div>
                <div className="text-sm text-gray-600">Total Gestures</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{gestureStats.handGestures}</div>
                <div className="text-sm text-gray-600">Hand Gestures</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{gestureStats.headGestures}</div>
                <div className="text-sm text-gray-600">Head Movements</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{gestureStats.bodyLanguage}</div>
                <div className="text-sm text-gray-600">Body Language</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-pink-600">{gestureStats.contextualGestures}</div>
                <div className="text-sm text-gray-600">Contextual</div>
              </div>
            </div>

            {previewMode && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium mb-3">Gesture Timeline Preview</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {generatedGestures.slice(0, 10).map((gesture) => (
                    <div key={gesture.id} className="flex items-center justify-between text-sm">
                      <span className="font-medium capitalize">{gesture.gestureType.replace('_', ' ')}</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{gesture.timing.toFixed(1)}s</Badge>
                        <Badge variant="outline">{gesture.intensity}</Badge>
                        {gesture.coordination.relates_to_content && (
                          <Badge variant="outline" className="bg-green-50">contextual</Badge>
                        )}
                        {gesture.coordination.matches_voice_emphasis && (
                          <Badge variant="outline" className="bg-blue-50">voice-sync</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {generatedGestures.length > 10 && (
                    <div className="text-xs text-gray-500 text-center pt-2">
                      ... and {generatedGestures.length - 10} more gestures
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Gesture Quality Assessment */}
      {generatedGestures.length > 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardHeader>
            <CardTitle className="text-base text-purple-800">Gesture Intelligence Quality Assessment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div>
                <div className="text-lg font-bold text-purple-700">
                  {Math.min(95, 70 + (gestureStats.contextualGestures / gestureStats.totalGestures * 25)).toFixed(0)}%
                </div>
                <div className="text-sm text-purple-600">Context Relevance</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-700">
                  {Math.min(98, 75 + (settings.naturalness * 23)).toFixed(0)}%
                </div>
                <div className="text-sm text-blue-600">Natural Movement</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-700">
                  {Math.min(96, 60 + (settings.contextSensitivity * 36)).toFixed(0)}%
                </div>
                <div className="text-sm text-green-600">Content Coordination</div>
              </div>
              <div>
                <div className="text-lg font-bold text-pink-700">
                  {Math.min(94, 85 - Math.abs(gestureStats.gesturesPerMinute - 6) * 3).toFixed(0)}%
                </div>
                <div className="text-sm text-pink-600">Frequency Balance</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}