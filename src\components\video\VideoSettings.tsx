'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'

interface VideoSettingsProps {
  aspectRatio: '16:9' | '9:16' | '1:1'
  background?: {
    type: 'color' | 'image'
    value: string
  }
  onUpdate: (data: { 
    aspect_ratio?: '16:9' | '9:16' | '1:1'
    background?: { type: 'color' | 'image'; value: string }
  }) => void
  error?: string
}

const aspectRatioOptions = [
  {
    value: '16:9' as const,
    label: 'Landscape (16:9)',
    description: 'Perfect for YouTube, presentations',
    icon: '📺',
    width: 160,
    height: 90
  },
  {
    value: '9:16' as const,
    label: 'Portrait (9:16)',
    description: 'Great for TikTok, Instagram Stories',
    icon: '📱',
    width: 90,
    height: 160
  },
  {
    value: '1:1' as const,
    label: 'Square (1:1)',
    description: 'Ideal for Instagram posts, LinkedIn',
    icon: '⬜',
    width: 120,
    height: 120
  }
]

const backgroundPresets = [
  { type: 'color' as const, value: '#ffffff', name: 'White' },
  { type: 'color' as const, value: '#f8fafc', name: 'Light Gray' },
  { type: 'color' as const, value: '#1e293b', name: 'Dark Gray' },
  { type: 'color' as const, value: '#3b82f6', name: 'Blue' },
  { type: 'color' as const, value: '#10b981', name: 'Green' },
  { type: 'color' as const, value: '#8b5cf6', name: 'Purple' },
  { type: 'color' as const, value: '#ef4444', name: 'Red' },
  { type: 'color' as const, value: '#f59e0b', name: 'Orange' }
]

const gradientPresets = [
  { type: 'color' as const, value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', name: 'Blue Purple' },
  { type: 'color' as const, value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', name: 'Pink Red' },
  { type: 'color' as const, value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', name: 'Blue Cyan' },
  { type: 'color' as const, value: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', name: 'Green Mint' },
  { type: 'color' as const, value: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', name: 'Pink Yellow' },
  { type: 'color' as const, value: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', name: 'Mint Pink' }
]

export function VideoSettings({ aspectRatio, background, onUpdate, error }: VideoSettingsProps) {
  const [backgroundType, setBackgroundType] = useState<'solid' | 'gradient' | 'custom' | 'image'>(
    background?.value.startsWith('linear-gradient') ? 'gradient' :
    background?.value.startsWith('#') ? 'solid' :
    background?.value.startsWith('http') ? 'image' : 'custom'
  )
  const [customColor, setCustomColor] = useState(
    background?.value.startsWith('#') ? background.value : '#ffffff'
  )
  const [customGradient, setCustomGradient] = useState(
    background?.value.startsWith('linear-gradient') ? background.value : 
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  )

  const handleAspectRatioChange = (newRatio: '16:9' | '9:16' | '1:1') => {
    onUpdate({ aspect_ratio: newRatio })
  }

  const handleBackgroundChange = (newBackground: { type: 'color' | 'image'; value: string }) => {
    onUpdate({ background: newBackground })
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Video Settings</h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure your video format and visual settings
        </p>
      </div>

      {/* Aspect Ratio Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Aspect Ratio</CardTitle>
          <CardDescription>
            Choose the format that best fits your platform and audience
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {aspectRatioOptions.map((option) => (
              <div
                key={option.value}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                  aspectRatio === option.value
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleAspectRatioChange(option.value)}
              >
                <div className="text-center">
                  <div className="mx-auto mb-3 flex items-center justify-center">
                    <div
                      className="bg-gray-300 rounded border border-gray-400 flex items-center justify-center text-xs text-gray-600"
                      style={{
                        width: option.width / 4,
                        height: option.height / 4,
                        minWidth: '30px',
                        minHeight: '30px'
                      }}
                    >
                      {option.icon}
                    </div>
                  </div>
                  <h4 className="font-medium text-sm mb-1">{option.label}</h4>
                  <p className="text-xs text-gray-500">{option.description}</p>
                  {aspectRatio === option.value && (
                    <Badge className="mt-2" variant="default">Selected</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Background Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Background</CardTitle>
          <CardDescription>
            Customize the background to match your brand
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Background Type Selector */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={backgroundType === 'solid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setBackgroundType('solid')}
            >
              Solid Colors
            </Button>
            <Button
              variant={backgroundType === 'gradient' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setBackgroundType('gradient')}
            >
              Gradients
            </Button>
            <Button
              variant={backgroundType === 'custom' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setBackgroundType('custom')}
            >
              Custom
            </Button>
            <Button
              variant={backgroundType === 'image' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setBackgroundType('image')}
              disabled
            >
              Images (Pro)
            </Button>
          </div>

          {/* Solid Colors */}
          {backgroundType === 'solid' && (
            <div>
              <h4 className="text-sm font-medium mb-3">Choose a solid color</h4>
              <div className="grid grid-cols-4 sm:grid-cols-8 gap-2">
                {backgroundPresets.map((preset) => (
                  <button
                    key={preset.name}
                    className={`w-10 h-10 rounded-lg border-2 transition-all hover:scale-105 ${
                      background?.value === preset.value
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    style={{ backgroundColor: preset.value }}
                    onClick={() => handleBackgroundChange(preset)}
                    title={preset.name}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Gradients */}
          {backgroundType === 'gradient' && (
            <div>
              <h4 className="text-sm font-medium mb-3">Choose a gradient</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {gradientPresets.map((preset) => (
                  <button
                    key={preset.name}
                    className={`h-16 rounded-lg border-2 transition-all hover:scale-105 ${
                      background?.value === preset.value
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    style={{ background: preset.value }}
                    onClick={() => handleBackgroundChange(preset)}
                    title={preset.name}
                  >
                    <span className="text-white text-xs font-medium bg-black bg-opacity-30 px-2 py-1 rounded">
                      {preset.name}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Custom Color/Gradient */}
          {backgroundType === 'custom' && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Custom Color</label>
                <div className="flex space-x-3">
                  <Input
                    type="color"
                    value={customColor}
                    onChange={(e) => setCustomColor(e.target.value)}
                    className="w-16 h-10 p-1 border rounded"
                  />
                  <Input
                    type="text"
                    value={customColor}
                    onChange={(e) => setCustomColor(e.target.value)}
                    placeholder="#ffffff"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    onClick={() => handleBackgroundChange({ type: 'color', value: customColor })}
                  >
                    Apply
                  </Button>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Custom Gradient</label>
                <div className="space-y-2">
                  <div
                    className="h-16 rounded-lg border border-gray-300"
                    style={{ background: customGradient }}
                  />
                  <Input
                    type="text"
                    value={customGradient}
                    onChange={(e) => setCustomGradient(e.target.value)}
                    placeholder="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                  />
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleBackgroundChange({ type: 'color', value: customGradient })}
                  >
                    Apply Gradient
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Image Background (Pro Feature) */}
          {backgroundType === 'image' && (
            <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center">
              <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-sm text-gray-600 mb-2">Custom background images</p>
              <Badge variant="outline">Pro Feature</Badge>
              <p className="text-xs text-gray-500 mt-2">
                Upgrade to Pro to use custom background images
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Preview</CardTitle>
          <CardDescription>
            See how your video will look with these settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <div className="relative">
              <div
                className="border border-gray-300 rounded-lg flex items-center justify-center"
                style={{
                  width: aspectRatioOptions.find(o => o.value === aspectRatio)?.width || 160,
                  height: aspectRatioOptions.find(o => o.value === aspectRatio)?.height || 90,
                  background: background?.value || '#f0f0f0'
                }}
              >
                <div className="text-center">
                  <div className="w-8 h-10 bg-gray-400 rounded-sm mx-auto mb-1 flex items-center justify-center">
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className="text-xs text-gray-600">Avatar</div>
                </div>
              </div>
              <div className="text-center mt-2">
                <div className="text-xs text-gray-500">
                  {aspectRatio} • {aspectRatioOptions.find(o => o.value === aspectRatio)?.label}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {error}
        </div>
      )}

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Pro Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Use 16:9 for YouTube, presentations, and landscape content</li>
            <li>• Choose 9:16 for TikTok, Instagram Stories, and mobile content</li>
            <li>• Square 1:1 works great for Instagram posts and LinkedIn</li>
            <li>• Light backgrounds work better with dark text overlays</li>
            <li>• Gradients can add visual interest without being distracting</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}