'use client'

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { VideoSettings } from '@/components/video/VideoSettings'
import { useProjectWizard } from '@/context/project-wizard-context'

interface TimelineItem {
  id: string
  type: 'avatar' | 'visual' | 'text' | 'transition'
  startTime: number
  duration: number
  content: any
  position: { x: number; y: number; width: number; height: number }
  zIndex: number
}

interface TimelineLayer {
  id: string
  name: string
  type: 'video' | 'audio' | 'text' | 'effects'
  items: TimelineItem[]
  visible: boolean
  locked: boolean
}

const TIMELINE_SETTINGS = {
  secondsPerPixel: 0.1, // 10 pixels per second
  minDuration: 1, // minimum 1 second
  maxDuration: 300, // maximum 5 minutes
  snapThreshold: 5 // pixels
}

const AVATAR_POSITIONS = [
  { id: 'side', label: 'Side View', x: 70, y: 20, width: 25, height: 60 },
  { id: 'corner', label: 'Corner', x: 75, y: 70, width: 20, height: 25 },
  { id: 'overlay', label: 'Overlay', x: 40, y: 30, width: 20, height: 40 },
  { id: 'fullscreen', label: 'Fullscreen', x: 0, y: 0, width: 100, height: 100 }
]

const AVATAR_SIZES = [
  { id: 'small', label: 'Small', scale: 0.8 },
  { id: 'medium', label: 'Medium', scale: 1.0 },
  { id: 'large', label: 'Large', scale: 1.2 }
]

const TRANSITION_TYPES = [
  { id: 'cut', label: 'Cut', duration: 0 },
  { id: 'fade', label: 'Fade', duration: 0.5 },
  { id: 'slide', label: 'Slide', duration: 0.8 },
  { id: 'zoom', label: 'Zoom', duration: 1.0 }
]

export function TimelineEditor() {
  const { projectData, dispatch } = useProjectWizard()
  const [layers, setLayers] = useState<TimelineLayer[]>([])
  const [selectedItem, setSelectedItem] = useState<string | null>(null)
  const [playheadPosition, setPlayheadPosition] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [totalDuration, setTotalDuration] = useState(30) // Default 30 seconds
  const timelineRef = useRef<HTMLDivElement>(null)
  const playheadRef = useRef<HTMLDivElement>(null)

  // Initialize timeline with default layers
  useEffect(() => {
    const defaultLayers: TimelineLayer[] = [
      {
        id: 'avatar-layer',
        name: 'AI Avatar',
        type: 'video',
        visible: true,
        locked: false,
        items: [{
          id: 'avatar-1',
          type: 'avatar',
          startTime: 0,
          duration: totalDuration,
          content: {
            avatarId: projectData.avatar_id,
            voiceId: projectData.voice_id,
            script: projectData.script
          },
          position: AVATAR_POSITIONS.find(p => p.id === projectData.timeline_settings.avatar_position) || AVATAR_POSITIONS[1],
          zIndex: 2
        }]
      },
      {
        id: 'visual-layer',
        name: 'Visual Content',
        type: 'video',
        visible: true,
        locked: false,
        items: projectData.visual_content.screen_recording ? [{
          id: 'visual-1',
          type: 'visual',
          startTime: 0,
          duration: Math.min(projectData.visual_content.screen_recording.duration, totalDuration),
          content: {
            url: projectData.visual_content.screen_recording.file_url,
            scenes: projectData.visual_content.screen_recording.scenes
          },
          position: { x: 0, y: 0, width: 100, height: 100 },
          zIndex: 1
        }] : []
      },
      {
        id: 'text-layer',
        name: 'Text & Captions',
        type: 'text',
        visible: true,
        locked: false,
        items: []
      },
      {
        id: 'effects-layer',
        name: 'Transitions & Effects',
        type: 'effects',
        visible: true,
        locked: false,
        items: []
      }
    ]
    
    setLayers(defaultLayers)
  }, [projectData, totalDuration])

  // Calculate timeline width based on duration and zoom
  const timelineWidth = totalDuration / TIMELINE_SETTINGS.secondsPerPixel * zoom

  // Convert time to pixel position
  const timeToPixel = (time: number) => {
    return (time / TIMELINE_SETTINGS.secondsPerPixel) * zoom
  }

  // Convert pixel position to time
  const pixelToTime = (pixel: number) => {
    return (pixel / zoom) * TIMELINE_SETTINGS.secondsPerPixel
  }

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 100)
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
  }

  // Handle timeline click to move playhead
  const handleTimelineClick = (event: React.MouseEvent) => {
    if (!timelineRef.current) return
    
    const rect = timelineRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const time = pixelToTime(x)
    setPlayheadPosition(Math.max(0, Math.min(time, totalDuration)))
  }

  // Handle item drag
  const handleItemDrag = useCallback((itemId: string, layerId: string, newStartTime: number) => {
    setLayers(prevLayers => 
      prevLayers.map(layer => {
        if (layer.id !== layerId) return layer
        
        return {
          ...layer,
          items: layer.items.map(item => {
            if (item.id !== itemId) return item
            
            // Snap to grid
            const snappedTime = Math.round(newStartTime / 0.5) * 0.5
            const clampedTime = Math.max(0, Math.min(snappedTime, totalDuration - item.duration))
            
            return { ...item, startTime: clampedTime }
          })
        }
      })
    )
  }, [totalDuration])

  // Handle item resize
  const handleItemResize = useCallback((itemId: string, layerId: string, newDuration: number) => {
    setLayers(prevLayers => 
      prevLayers.map(layer => {
        if (layer.id !== layerId) return layer
        
        return {
          ...layer,
          items: layer.items.map(item => {
            if (item.id !== itemId) return item
            
            const clampedDuration = Math.max(TIMELINE_SETTINGS.minDuration, 
              Math.min(newDuration, totalDuration - item.startTime))
            
            return { ...item, duration: clampedDuration }
          })
        }
      })
    )
  }, [totalDuration])

  // Add transition between clips
  const addTransition = (layerId: string, position: number) => {
    const newTransition: TimelineItem = {
      id: `transition-${Date.now()}`,
      type: 'transition',
      startTime: position,
      duration: 0.5,
      content: {
        type: projectData.timeline_settings.transition_style,
        properties: {}
      },
      position: { x: 0, y: 0, width: 100, height: 100 },
      zIndex: 10
    }

    setLayers(prevLayers =>
      prevLayers.map(layer =>
        layer.id === 'effects-layer'
          ? { ...layer, items: [...layer.items, newTransition] }
          : layer
      )
    )
  }

  // Toggle layer visibility
  const toggleLayerVisibility = (layerId: string) => {
    setLayers(prevLayers =>
      prevLayers.map(layer =>
        layer.id === layerId
          ? { ...layer, visible: !layer.visible }
          : layer
      )
    )
  }

  // Toggle layer lock
  const toggleLayerLock = (layerId: string) => {
    setLayers(prevLayers =>
      prevLayers.map(layer =>
        layer.id === layerId
          ? { ...layer, locked: !layer.locked }
          : layer
      )
    )
  }

  // Update timeline settings in project data
  const updateTimelineSettings = (updates: Partial<typeof projectData.timeline_settings>) => {
    const newSettings = { ...projectData.timeline_settings, ...updates }
    
    dispatch({
      type: 'UPDATE_VIDEO_SETTINGS',
      payload: {
        timeline_settings: newSettings
      }
    })

    // Update avatar position in timeline
    if (updates.avatar_position) {
      const newPosition = AVATAR_POSITIONS.find(p => p.id === updates.avatar_position)
      if (newPosition) {
        setLayers(prevLayers =>
          prevLayers.map(layer =>
            layer.id === 'avatar-layer'
              ? {
                  ...layer,
                  items: layer.items.map(item =>
                    item.type === 'avatar'
                      ? { ...item, position: newPosition }
                      : item
                  )
                }
              : layer
          )
        )
      }
    }
  }

  // Play/pause preview
  const togglePreview = () => {
    setIsPlaying(!isPlaying)
    // In a real implementation, this would control video playback
  }

  // Render timeline item
  const renderTimelineItem = (item: TimelineItem, layerId: string) => {
    const left = timeToPixel(item.startTime)
    const width = timeToPixel(item.duration)
    const isSelected = selectedItem === item.id

    let backgroundColor = '#3B82F6'
    let icon = '📺'
    
    switch (item.type) {
      case 'avatar':
        backgroundColor = '#8B5CF6'
        icon = '👤'
        break
      case 'visual':
        backgroundColor = '#10B981'
        icon = '🎬'
        break
      case 'text':
        backgroundColor = '#F59E0B'
        icon = '📝'
        break
      case 'transition':
        backgroundColor = '#EF4444'
        icon = '✨'
        break
    }

    return (
      <div
        key={item.id}
        className={`absolute top-1 bottom-1 rounded cursor-pointer transition-all hover:shadow-lg ${
          isSelected ? 'ring-2 ring-blue-400' : ''
        }`}
        style={{
          left: `${left}px`,
          width: `${width}px`,
          backgroundColor,
          opacity: layers.find(l => l.id === layerId)?.visible ? 1 : 0.5
        }}
        onClick={() => setSelectedItem(item.id)}
        onMouseDown={(e) => {
          e.preventDefault()
          // Handle drag start logic here
        }}
      >
        <div className="flex items-center h-full px-2 text-white text-xs">
          <span className="mr-1">{icon}</span>
          <span className="truncate">
            {item.type === 'avatar' ? 'AI Avatar' : 
             item.type === 'visual' ? 'Screen Recording' :
             item.type === 'text' ? 'Text Layer' :
             'Transition'}
          </span>
        </div>
        
        {/* Resize handles */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-white/30 cursor-w-resize hover:bg-white/50" />
        <div className="absolute right-0 top-0 bottom-0 w-1 bg-white/30 cursor-e-resize hover:bg-white/50" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Timeline Editor</h3>
        <p className="text-sm text-gray-600 mb-6">
          Arrange and customize your video timeline with advanced editing controls
        </p>
      </div>

      {/* Video Settings Integration */}
      <VideoSettings
        aspectRatio={projectData.aspect_ratio}
        background={projectData.background}
        onUpdate={(updates) => {
          dispatch({ type: 'UPDATE_VIDEO_SETTINGS', payload: updates })
        }}
        error={undefined}
      />

      {/* Timeline Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center justify-between">
            <span className="flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Timeline Controls
            </span>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {formatTime(playheadPosition)} / {formatTime(totalDuration)}
              </Badge>
              <Button size="sm" variant="outline" onClick={togglePreview}>
                {isPlaying ? '⏸️' : '▶️'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Timeline Settings */}
          <div className="grid gap-4 md:grid-cols-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Avatar Position
              </label>
              <select
                value={projectData.timeline_settings.avatar_position}
                onChange={(e) => updateTimelineSettings({ 
                  avatar_position: e.target.value as any 
                })}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                {AVATAR_POSITIONS.map((pos) => (
                  <option key={pos.id} value={pos.id}>{pos.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Avatar Size
              </label>
              <select
                value={projectData.timeline_settings.avatar_size}
                onChange={(e) => updateTimelineSettings({ 
                  avatar_size: e.target.value as any 
                })}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                {AVATAR_SIZES.map((size) => (
                  <option key={size.id} value={size.id}>{size.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Transitions
              </label>
              <select
                value={projectData.timeline_settings.transition_style}
                onChange={(e) => updateTimelineSettings({ 
                  transition_style: e.target.value as any 
                })}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                {TRANSITION_TYPES.map((transition) => (
                  <option key={transition.id} value={transition.id}>{transition.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Duration (seconds)
              </label>
              <Input
                type="number"
                min={5}
                max={300}
                value={totalDuration}
                onChange={(e) => setTotalDuration(Math.max(5, Math.min(300, parseInt(e.target.value) || 30)))}
                className="text-sm"
              />
            </div>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Zoom:</span>
              <Button size="sm" variant="outline" onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}>
                -
              </Button>
              <span className="text-sm font-medium w-12 text-center">{Math.round(zoom * 100)}%</span>
              <Button size="sm" variant="outline" onClick={() => setZoom(Math.min(3, zoom + 0.25))}>
                +
              </Button>
            </div>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => addTransition('effects-layer', playheadPosition)}
              disabled={playheadPosition >= totalDuration}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Transition
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Timeline Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Video Timeline</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* Layer Controls */}
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center space-x-4 text-sm">
              <span className="text-gray-600 w-20">Layers:</span>
              {layers.map((layer) => (
                <div key={layer.id} className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => toggleLayerVisibility(layer.id)}
                    className="p-1"
                  >
                    {layer.visible ? '👁️' : '🙈'}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => toggleLayerLock(layer.id)}
                    className="p-1"
                  >
                    {layer.locked ? '🔒' : '🔓'}
                  </Button>
                  <span className={`${layer.visible ? 'text-gray-900' : 'text-gray-400'}`}>
                    {layer.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Timeline */}
          <div className="relative overflow-x-auto">
            {/* Time ruler */}
            <div className="h-8 border-b border-gray-200 bg-gray-50 relative">
              {Array.from({ length: Math.ceil(totalDuration) + 1 }, (_, i) => (
                <div
                  key={i}
                  className="absolute top-0 bottom-0 border-l border-gray-300"
                  style={{ left: `${timeToPixel(i)}px` }}
                >
                  <span className="absolute top-1 left-1 text-xs text-gray-600">
                    {formatTime(i)}
                  </span>
                </div>
              ))}
            </div>

            {/* Timeline layers */}
            <div
              ref={timelineRef}
              className="relative bg-gray-100"
              style={{ width: `${timelineWidth}px`, minWidth: '100%' }}
              onClick={handleTimelineClick}
            >
              {layers.map((layer, layerIndex) => (
                <div
                  key={layer.id}
                  className="relative h-16 border-b border-gray-200 bg-white"
                  style={{ opacity: layer.visible ? 1 : 0.5 }}
                >
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs font-medium text-gray-600">
                    {layer.name}
                  </div>
                  
                  {/* Layer items */}
                  <div className="ml-20 h-full relative">
                    {layer.items.map((item) => renderTimelineItem(item, layer.id))}
                  </div>
                </div>
              ))}

              {/* Playhead */}
              <div
                ref={playheadRef}
                className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-50 pointer-events-none"
                style={{ left: `${timeToPixel(playheadPosition)}px` }}
              >
                <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Video Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div 
            className="relative bg-gray-900 rounded-lg overflow-hidden mx-auto"
            style={{ 
              aspectRatio: projectData.aspect_ratio.replace(':', '/'),
              maxWidth: '600px',
              maxHeight: '400px'
            }}
          >
            {/* Background */}
            <div 
              className="absolute inset-0"
              style={{
                backgroundColor: projectData.background?.type === 'color' ? projectData.background.value : '#1F2937',
                backgroundImage: projectData.background?.type === 'image' ? `url(${projectData.background.value})` : 'none',
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            />

            {/* Visual content layer */}
            {projectData.visual_content.screen_recording && (
              <div className="absolute inset-0">
                <div className="w-full h-full bg-gray-700 flex items-center justify-center text-white">
                  <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                  </svg>
                  <p className="text-sm">Screen Recording Preview</p>
                </div>
              </div>
            )}

            {/* Avatar layer */}
            {projectData.avatar_id && (
              <div 
                className="absolute bg-white/90 rounded-lg shadow-lg flex items-center justify-center"
                style={{
                  left: `${AVATAR_POSITIONS.find(p => p.id === projectData.timeline_settings.avatar_position)?.x || 75}%`,
                  top: `${AVATAR_POSITIONS.find(p => p.id === projectData.timeline_settings.avatar_position)?.y || 70}%`,
                  width: `${(AVATAR_POSITIONS.find(p => p.id === projectData.timeline_settings.avatar_position)?.width || 20) * 
                    (AVATAR_SIZES.find(s => s.id === projectData.timeline_settings.avatar_size)?.scale || 1)}%`,
                  height: `${(AVATAR_POSITIONS.find(p => p.id === projectData.timeline_settings.avatar_position)?.height || 25) * 
                    (AVATAR_SIZES.find(s => s.id === projectData.timeline_settings.avatar_size)?.scale || 1)}%`,
                  transform: 'translate(-50%, -50%)'
                }}
              >
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <p className="text-xs text-gray-600">AI Avatar</p>
                </div>
              </div>
            )}

            {/* Brand logo */}
            {projectData.custom_branding?.logo_url && (
              <div 
                className={`absolute w-12 h-12 ${
                  projectData.custom_branding.watermark_position === 'top-left' ? 'top-4 left-4' :
                  projectData.custom_branding.watermark_position === 'top-right' ? 'top-4 right-4' :
                  projectData.custom_branding.watermark_position === 'bottom-left' ? 'bottom-4 left-4' :
                  'bottom-4 right-4'
                }`}
              >
                <div className="w-full h-full bg-white/90 rounded-lg p-2 shadow-lg">
                  <img 
                    src={projectData.custom_branding.logo_url} 
                    alt="Brand logo" 
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            )}

            {/* Progress bar */}
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30">
              <div 
                className="h-full bg-blue-500 transition-all duration-100"
                style={{ width: `${(playheadPosition / totalDuration) * 100}%` }}
              />
            </div>
          </div>
          
          <div className="mt-4 text-center text-sm text-gray-600">
            Preview of your video layout and timeline settings
          </div>
        </CardContent>
      </Card>
    </div>
  )
}