interface HeyGenAvatar {
  avatar_id: string
  avatar_name: string
  preview_image_url: string
  gender: 'male' | 'female'
  voice_id?: string
}

interface HeyGenVoice {
  voice_id: string
  language: string
  gender: 'male' | 'female'
  preview_audio_url: string
  language_code: string
}

interface CreateVideoRequest {
  video_inputs: Array<{
    character: {
      type: 'avatar'
      avatar_id: string
      avatar_style: 'normal' | 'circle'
    }
    voice: {
      type: 'text'
      input_text: string
      voice_id: string
    }
    background?: {
      type: 'color' | 'image'
      value: string
    }
  }>
  dimension?: {
    width: number
    height: number
  }
  aspect_ratio?: '16:9' | '9:16' | '1:1'
  test?: boolean
}

interface CreateVideoResponse {
  code: number
  data: {
    video_id: string
  }
  message: string
}

interface VideoStatus {
  code: number
  data: {
    video_id: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    video_url?: string
    video_url_caption?: string
    thumbnail_url?: string
    duration?: number
    callback_id?: string
    error?: {
      code: string
      message: string
    }
  }
  message: string
}

class HeyGenClient {
  private apiKey: string
  private baseURL: string

  constructor() {
    this.apiKey = process.env.HEYGEN_API_KEY!
    this.baseURL = process.env.HEYGEN_API_URL || 'https://api.heygen.com/v2'
  }

  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`HeyGen API error: ${response.status} - ${errorData.message || 'Unknown error'}`)
    }

    return response.json()
  }

  async getAvatars(): Promise<HeyGenAvatar[]> {
    const response = await this.makeRequest<{ code: number; data: HeyGenAvatar[] }>('/avatar/list')
    return response.data
  }

  async getVoices(): Promise<HeyGenVoice[]> {
    const response = await this.makeRequest<{ code: number; data: HeyGenVoice[] }>('/voice/list')
    return response.data
  }

  async createVideo(request: CreateVideoRequest): Promise<CreateVideoResponse> {
    return this.makeRequest<CreateVideoResponse>('/video/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async getVideoStatus(videoId: string): Promise<VideoStatus> {
    return this.makeRequest<VideoStatus>(`/video/status/${videoId}`)
  }

  async deleteVideo(videoId: string): Promise<{ code: number; message: string }> {
    return this.makeRequest(`/video/${videoId}`, {
      method: 'DELETE',
    })
  }

  // Helper method to create a simple talking head video
  async createTalkingHeadVideo({
    script,
    avatarId,
    voiceId,
    aspectRatio = '16:9',
    background = { type: 'color' as const, value: '#f0f0f0' },
    test = false
  }: {
    script: string
    avatarId: string
    voiceId: string
    aspectRatio?: '16:9' | '9:16' | '1:1'
    background?: { type: 'color' | 'image'; value: string }
    test?: boolean
  }): Promise<CreateVideoResponse> {
    const request: CreateVideoRequest = {
      video_inputs: [
        {
          character: {
            type: 'avatar',
            avatar_id: avatarId,
            avatar_style: 'normal'
          },
          voice: {
            type: 'text',
            input_text: script,
            voice_id: voiceId
          },
          background
        }
      ],
      aspect_ratio: aspectRatio,
      test
    }

    return this.createVideo(request)
  }

  // Method to check credit usage
  async getCredits(): Promise<{ remaining: number; total: number }> {
    try {
      const response = await this.makeRequest<{ 
        code: number
        data: { 
          remaining: number
          total: number 
        }
      }>('/user/credits')
      return response.data
    } catch (error) {
      console.error('Failed to fetch HeyGen credits:', error)
      return { remaining: 0, total: 0 }
    }
  }
}

export const heygenClient = new HeyGenClient()

export type {
  HeyGenAvatar,
  HeyGenVoice,
  CreateVideoRequest,
  CreateVideoResponse,
  VideoStatus
}