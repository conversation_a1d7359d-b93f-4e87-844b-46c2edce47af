'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { LoadingSpinner } from '@/components/ui/loading'
import { useProjectWizard } from '@/context/project-wizard-context'

interface PlatformConfig {
  platform: string
  title: string
  description: string
  hashtags: string[]
  scheduled_time?: Date
  platform_specific?: {
    [key: string]: any
  }
}

interface SEOConfig {
  keywords: string[]
  thumbnail_style: 'auto' | 'custom'
  custom_thumbnail_url?: string
}

const PLATFORM_TEMPLATES = {
  youtube: {
    name: 'YouTube',
    icon: '🎬',
    color: 'bg-red-100 text-red-800 border-red-200',
    maxTitleLength: 100,
    maxDescriptionLength: 5000,
    recommendedHashtags: 3,
    features: ['Chapters', 'End screens', 'Cards', 'Playlist placement'],
    tips: [
      'Use keyword-rich titles for better SEO',
      'Add timestamps for better viewer experience',
      'Include call-to-action in description',
      'Use relevant tags and categories'
    ]
  },
  tiktok: {
    name: 'TikTok',
    icon: '🎵',
    color: 'bg-pink-100 text-pink-800 border-pink-200',
    maxTitleLength: 150,
    maxDescriptionLength: 2200,
    recommendedHashtags: 5,
    features: ['Trending sounds', 'Effects', 'Duets', 'Shorts format'],
    tips: [
      'Hook viewers in first 3 seconds',
      'Use trending hashtags',
      'Keep it vertical (9:16)',
      'Add captions for accessibility'
    ]
  },
  linkedin: {
    name: 'LinkedIn',
    icon: '💼',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    maxTitleLength: 200,
    maxDescriptionLength: 3000,
    recommendedHashtags: 3,
    features: ['Professional network', 'B2B focus', 'Thought leadership'],
    tips: [
      'Focus on professional value',
      'Use industry-specific language',
      'Include business insights',
      'Tag relevant professionals'
    ]
  },
  twitter: {
    name: 'Twitter/X',
    icon: '🐦',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    maxTitleLength: 280,
    maxDescriptionLength: 280,
    recommendedHashtags: 2,
    features: ['Thread potential', 'Retweets', 'Spaces', 'Live updates'],
    tips: [
      'Create thread-worthy content',
      'Use relevant hashtags sparingly',
      'Engage with comments quickly',
      'Share at optimal times'
    ]
  },
  facebook: {
    name: 'Facebook',
    icon: '📘',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    maxTitleLength: 255,
    maxDescriptionLength: 63206,
    recommendedHashtags: 2,
    features: ['Community sharing', 'Groups', 'Events', 'Live streaming'],
    tips: [
      'Write engaging captions',
      'Use community-focused language',
      'Share in relevant groups',
      'Encourage sharing and comments'
    ]
  }
}

const POPULAR_HASHTAGS = {
  tech: ['#tech', '#technology', '#innovation', '#digital', '#AI', '#programming', '#coding'],
  business: ['#business', '#entrepreneur', '#startup', '#leadership', '#marketing', '#productivity'],
  education: ['#education', '#learning', '#tutorial', '#howto', '#tips', '#knowledge', '#training'],
  creative: ['#creative', '#design', '#art', '#video', '#content', '#inspiration', '#digital'],
  lifestyle: ['#lifestyle', '#motivation', '#success', '#mindset', '#personal', '#growth']
}

export function PublishingStep() {
  const { projectData, dispatch } = useProjectWizard()
  const [platformConfigs, setPlatformConfigs] = useState<PlatformConfig[]>(
    projectData.publishing_config?.platforms || []
  )
  const [seoConfig, setSeoConfig] = useState<SEOConfig>(
    projectData.publishing_config?.seo_optimization || {
      keywords: [],
      thumbnail_style: 'auto'
    }
  )
  const [selectedPlatforms, setSelectedPlatforms] = useState<Set<string>>(
    new Set(platformConfigs.map(config => config.platform))
  )
  const [isGeneratingContent, setIsGeneratingContent] = useState(false)
  const [newKeyword, setNewKeyword] = useState('')

  // Initialize with platforms from audience step
  useEffect(() => {
    const audiencePlatforms = projectData.platforms || []
    const existingPlatforms = new Set(platformConfigs.map(config => config.platform))
    
    // Add missing platforms from audience selection
    const missingConfigs = audiencePlatforms
      .filter(platform => !existingPlatforms.has(platform))
      .map(platform => createDefaultConfig(platform))
    
    if (missingConfigs.length > 0) {
      const updatedConfigs = [...platformConfigs, ...missingConfigs]
      setPlatformConfigs(updatedConfigs)
      setSelectedPlatforms(new Set(updatedConfigs.map(config => config.platform)))
    }
  }, [projectData.platforms, platformConfigs])

  const createDefaultConfig = (platform: string): PlatformConfig => {
    const template = PLATFORM_TEMPLATES[platform as keyof typeof PLATFORM_TEMPLATES]
    
    return {
      platform,
      title: `${projectData.topic} - ${projectData.video_style}`,
      description: `Learn about ${projectData.topic} in this comprehensive ${projectData.video_style} designed for ${projectData.target_audience} learners.`,
      hashtags: [],
      platform_specific: {}
    }
  }

  const togglePlatform = (platform: string) => {
    const newSelected = new Set(selectedPlatforms)
    
    if (newSelected.has(platform)) {
      newSelected.delete(platform)
      setPlatformConfigs(configs => configs.filter(config => config.platform !== platform))
    } else {
      newSelected.add(platform)
      const newConfig = createDefaultConfig(platform)
      setPlatformConfigs(configs => [...configs, newConfig])
    }
    
    setSelectedPlatforms(newSelected)
    updateProjectData([...platformConfigs], seoConfig)
  }

  const updatePlatformConfig = (platform: string, updates: Partial<PlatformConfig>) => {
    const updatedConfigs = platformConfigs.map(config =>
      config.platform === platform ? { ...config, ...updates } : config
    )
    setPlatformConfigs(updatedConfigs)
    updateProjectData(updatedConfigs, seoConfig)
  }

  const addHashtag = (platform: string, hashtag: string) => {
    const config = platformConfigs.find(c => c.platform === platform)
    if (!config || config.hashtags.includes(hashtag)) return

    const template = PLATFORM_TEMPLATES[platform as keyof typeof PLATFORM_TEMPLATES]
    if (config.hashtags.length >= template.recommendedHashtags * 2) return

    updatePlatformConfig(platform, {
      hashtags: [...config.hashtags, hashtag]
    })
  }

  const removeHashtag = (platform: string, hashtag: string) => {
    const config = platformConfigs.find(c => c.platform === platform)
    if (!config) return

    updatePlatformConfig(platform, {
      hashtags: config.hashtags.filter(tag => tag !== hashtag)
    })
  }

  const addKeyword = () => {
    if (!newKeyword.trim() || seoConfig.keywords.includes(newKeyword.trim())) return
    
    const updatedSEO = {
      ...seoConfig,
      keywords: [...seoConfig.keywords, newKeyword.trim()]
    }
    setSeoConfig(updatedSEO)
    setNewKeyword('')
    updateProjectData(platformConfigs, updatedSEO)
  }

  const removeKeyword = (keyword: string) => {
    const updatedSEO = {
      ...seoConfig,
      keywords: seoConfig.keywords.filter(k => k !== keyword)
    }
    setSeoConfig(updatedSEO)
    updateProjectData(platformConfigs, updatedSEO)
  }

  const updateProjectData = (configs: PlatformConfig[], seo: SEOConfig) => {
    dispatch({
      type: 'UPDATE_PUBLISHING',
      payload: {
        platforms: configs,
        seo_optimization: seo
      }
    })
  }

  const generateOptimizedContent = async () => {
    setIsGeneratingContent(true)
    try {
      // Simulate AI-powered content optimization
      await new Promise(resolve => setTimeout(resolve, 2000))

      const optimizedConfigs = platformConfigs.map(config => {
        const template = PLATFORM_TEMPLATES[config.platform as keyof typeof PLATFORM_TEMPLATES]
        
        // Generate platform-optimized title
        let optimizedTitle = config.title
        if (config.platform === 'youtube') {
          optimizedTitle = `${projectData.topic} Tutorial - Complete Guide for ${projectData.target_audience}s`
        } else if (config.platform === 'tiktok') {
          optimizedTitle = `${projectData.topic} in 60 seconds! #${projectData.topic.replace(/\s+/g, '').toLowerCase()}`
        } else if (config.platform === 'linkedin') {
          optimizedTitle = `Professional Guide: ${projectData.topic} Best Practices`
        }

        // Generate platform-optimized description
        let optimizedDescription = config.description
        if (config.platform === 'youtube') {
          optimizedDescription = `🎯 In this ${projectData.video_style}, you'll learn everything about ${projectData.topic}.\n\n` +
            `Perfect for ${projectData.target_audience} learners who want to master this topic quickly and efficiently.\n\n` +
            `📚 What you'll learn:\n${projectData.researched_content?.tutorial_steps.slice(0, 3).map(step => `• ${step}`).join('\n') || '• Key concepts and fundamentals\n• Practical implementation\n• Best practices'}\n\n` +
            `🔗 Resources mentioned in this video:\n${projectData.researched_content?.documentation_links.slice(0, 2).join('\n') || ''}\n\n` +
            `⏰ Timestamps:\n0:00 Introduction\n1:30 Core concepts\n3:00 Practical examples\n\n` +
            `👍 Like and subscribe for more tutorials!`
        }

        // Generate platform-specific hashtags
        const topicWords = projectData.topic.toLowerCase().split(' ')
        const suggestedHashtags = []
        
        if (topicWords.some(word => ['tech', 'programming', 'code', 'software', 'ai', 'data'].includes(word))) {
          suggestedHashtags.push(...POPULAR_HASHTAGS.tech.slice(0, template.recommendedHashtags))
        } else if (topicWords.some(word => ['business', 'marketing', 'startup', 'sales'].includes(word))) {
          suggestedHashtags.push(...POPULAR_HASHTAGS.business.slice(0, template.recommendedHashtags))
        } else if (topicWords.some(word => ['learn', 'tutorial', 'education', 'course'].includes(word))) {
          suggestedHashtags.push(...POPULAR_HASHTAGS.education.slice(0, template.recommendedHashtags))
        } else {
          suggestedHashtags.push(...POPULAR_HASHTAGS.creative.slice(0, template.recommendedHashtags))
        }

        return {
          ...config,
          title: optimizedTitle.slice(0, template.maxTitleLength),
          description: optimizedDescription.slice(0, template.maxDescriptionLength),
          hashtags: [...new Set([...config.hashtags, ...suggestedHashtags])].slice(0, template.recommendedHashtags * 2)
        }
      })

      setPlatformConfigs(optimizedConfigs)

      // Generate SEO keywords
      const optimizedKeywords = [
        projectData.topic,
        `${projectData.topic} tutorial`,
        `${projectData.topic} guide`,
        `${projectData.topic} ${projectData.target_audience}`,
        projectData.video_style,
        ...projectData.researched_content?.keywords.slice(0, 3) || []
      ].filter(keyword => keyword && keyword.length > 2)

      const optimizedSEO = {
        ...seoConfig,
        keywords: [...new Set([...seoConfig.keywords, ...optimizedKeywords])].slice(0, 10)
      }

      setSeoConfig(optimizedSEO)
      updateProjectData(optimizedConfigs, optimizedSEO)

    } catch (error) {
      console.error('Error generating optimized content:', error)
    } finally {
      setIsGeneratingContent(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Publishing & Distribution</h3>
        <p className="text-sm text-gray-600 mb-6">
          Configure your video for optimal distribution across multiple platforms
        </p>
      </div>

      {/* Platform Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center justify-between">
            <span className="flex items-center">
              <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Publishing Platforms
            </span>
            <Button
              onClick={generateOptimizedContent}
              disabled={isGeneratingContent || selectedPlatforms.size === 0}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              {isGeneratingContent ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Optimizing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  AI Optimize All
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(PLATFORM_TEMPLATES).map(([platform, template]) => (
              <Card
                key={platform}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedPlatforms.has(platform)
                    ? 'ring-2 ring-green-500 border-green-500 bg-green-50'
                    : 'hover:border-gray-300'
                }`}
                onClick={() => togglePlatform(platform)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{template.icon}</span>
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <Badge className={template.color}>
                          {template.recommendedHashtags} hashtags
                        </Badge>
                      </div>
                    </div>
                    {selectedPlatforms.has(platform) && (
                      <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-xs text-gray-600">
                      Max title: {template.maxTitleLength} chars
                    </div>
                    <div className="space-y-1">
                      {template.tips.slice(0, 2).map((tip, index) => (
                        <div key={index} className="flex items-start space-x-1 text-xs text-gray-600">
                          <span>•</span>
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Platform Configurations */}
      {platformConfigs.map((config) => {
        const template = PLATFORM_TEMPLATES[config.platform as keyof typeof PLATFORM_TEMPLATES]
        
        return (
          <Card key={config.platform}>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <span className="text-xl mr-2">{template.icon}</span>
                {template.name} Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Title */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Title ({config.title.length}/{template.maxTitleLength})
                </label>
                <Input
                  value={config.title}
                  onChange={(e) => updatePlatformConfig(config.platform, { 
                    title: e.target.value.slice(0, template.maxTitleLength) 
                  })}
                  placeholder={`Enter ${template.name} title...`}
                  className={config.title.length > template.maxTitleLength * 0.9 ? 'border-yellow-400' : ''}
                />
              </div>

              {/* Description */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Description ({config.description.length}/{template.maxDescriptionLength})
                </label>
                <Textarea
                  value={config.description}
                  onChange={(e) => updatePlatformConfig(config.platform, { 
                    description: e.target.value.slice(0, template.maxDescriptionLength) 
                  })}
                  placeholder={`Write your ${template.name} description...`}
                  rows={4}
                  className={config.description.length > template.maxDescriptionLength * 0.9 ? 'border-yellow-400' : ''}
                />
              </div>

              {/* Hashtags */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Hashtags ({config.hashtags.length}/{template.recommendedHashtags * 2} max)
                </label>
                
                {/* Current hashtags */}
                <div className="flex flex-wrap gap-2 mb-3">
                  {config.hashtags.map((hashtag) => (
                    <Badge
                      key={hashtag}
                      variant="outline"
                      className="cursor-pointer hover:bg-red-50 hover:border-red-300"
                      onClick={() => removeHashtag(config.platform, hashtag)}
                    >
                      {hashtag} ×
                    </Badge>
                  ))}
                </div>

                {/* Suggested hashtags */}
                <div className="space-y-2">
                  <p className="text-xs text-gray-600">Suggested for your topic:</p>
                  <div className="flex flex-wrap gap-1">
                    {Object.values(POPULAR_HASHTAGS).flat()
                      .filter(hashtag => 
                        !config.hashtags.includes(hashtag) &&
                        (hashtag.toLowerCase().includes(projectData.topic.toLowerCase()) ||
                         hashtag.toLowerCase().includes(projectData.video_style.toLowerCase()) ||
                         hashtag.toLowerCase().includes('tutorial') ||
                         hashtag.toLowerCase().includes('education'))
                      )
                      .slice(0, 8)
                      .map((hashtag) => (
                        <Button
                          key={hashtag}
                          size="sm"
                          variant="ghost"
                          className="text-xs h-6 px-2"
                          onClick={() => addHashtag(config.platform, hashtag)}
                          disabled={config.hashtags.length >= template.recommendedHashtags * 2}
                        >
                          {hashtag}
                        </Button>
                      ))}
                  </div>
                </div>
              </div>

              {/* Platform-specific tips */}
              <div className="bg-gray-50 rounded-lg p-3">
                <h5 className="text-sm font-medium text-gray-700 mb-2">
                  {template.name} Optimization Tips:
                </h5>
                <div className="space-y-1">
                  {template.tips.map((tip, index) => (
                    <div key={index} className="flex items-start space-x-2 text-xs text-gray-600">
                      <span className="text-blue-500">•</span>
                      <span>{tip}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}

      {/* SEO Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            SEO Optimization
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Keywords */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              SEO Keywords
            </label>
            
            <div className="flex gap-2 mb-3">
              <Input
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Add SEO keyword..."
                onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                className="flex-1"
              />
              <Button onClick={addKeyword} disabled={!newKeyword.trim()}>
                Add
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {seoConfig.keywords.map((keyword) => (
                <Badge
                  key={keyword}
                  variant="outline"
                  className="cursor-pointer hover:bg-red-50 hover:border-red-300"
                  onClick={() => removeKeyword(keyword)}
                >
                  {keyword} ×
                </Badge>
              ))}
            </div>
          </div>

          {/* Thumbnail */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Thumbnail Generation
            </label>
            <div className="space-y-3">
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="thumbnail"
                    checked={seoConfig.thumbnail_style === 'auto'}
                    onChange={() => setSeoConfig({ ...seoConfig, thumbnail_style: 'auto' })}
                    className="rounded"
                  />
                  <span className="text-sm">Auto-generate from video</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="thumbnail"
                    checked={seoConfig.thumbnail_style === 'custom'}
                    onChange={() => setSeoConfig({ ...seoConfig, thumbnail_style: 'custom' })}
                    className="rounded"
                  />
                  <span className="text-sm">Custom thumbnail</span>
                </label>
              </div>

              {seoConfig.thumbnail_style === 'custom' && (
                <div>
                  <Input
                    placeholder="Custom thumbnail URL..."
                    value={seoConfig.custom_thumbnail_url || ''}
                    onChange={(e) => setSeoConfig({ 
                      ...seoConfig, 
                      custom_thumbnail_url: e.target.value 
                    })}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended: 1280x720px, JPG/PNG format
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Publishing Summary */}
      {selectedPlatforms.size > 0 && (
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Publishing Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h5 className="font-medium text-green-900 mb-2">Selected Platforms</h5>
                <div className="space-y-1">
                  {Array.from(selectedPlatforms).map((platform) => {
                    const template = PLATFORM_TEMPLATES[platform as keyof typeof PLATFORM_TEMPLATES]
                    const config = platformConfigs.find(c => c.platform === platform)
                    
                    return (
                      <div key={platform} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span>{template.icon}</span>
                          <span className="text-sm">{template.name}</span>
                        </div>
                        <div className="flex space-x-1">
                          <Badge variant="outline" className="text-xs">
                            {config?.hashtags.length || 0} tags
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {config?.title.length || 0} chars
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-blue-900 mb-2">SEO Configuration</h5>
                <div className="space-y-1 text-sm text-blue-800">
                  <div>{seoConfig.keywords.length} SEO keywords configured</div>
                  <div>Thumbnail: {seoConfig.thumbnail_style} generation</div>
                  <div>Ready for multi-platform publishing</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}