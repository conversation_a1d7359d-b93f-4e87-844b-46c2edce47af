'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { LoadingSpinner } from '@/components/ui/loading'
import { EmotionMapping, VoiceModulation, MicroExpression, GestureIntelligence } from '@/types/humanization'

interface EmotionMappingEngineProps {
  script: string
  duration: number
  onEmotionMappingComplete: (emotionMaps: EmotionMapping[]) => void
  existingEmotionMaps?: EmotionMapping[]
}

interface EmotionSettings {
  emotionSensitivity: number // 0.1 to 1.0
  adaptiveTone: boolean
  contextualEmotions: boolean
  subtleTransitions: boolean
  emphasisDetection: boolean
  audienceAdaptation: 'children' | 'teens' | 'adults' | 'seniors' | 'professional'
  contentType: 'educational' | 'entertainment' | 'business' | 'personal' | 'promotional'
}

// Emotion detection patterns and their characteristics
const EMOTION_PATTERNS = {
  excited: {
    keywords: ['amazing', 'fantastic', 'incredible', 'awesome', 'love', 'thrilled', 'excited', '!'],
    contextClues: ['achievement', 'success', 'discovery', 'breakthrough'],
    voiceParams: { pitch_variation: 0.15, pace_multiplier: 1.1, volume_adjustment: 0.1 },
    expressions: ['subtle_smile', 'eyebrow_raise', 'eye_movement'],
    gestures: ['hand_movement', 'open_palm'],
    intensity_range: [0.6, 1.0]
  },
  confident: {
    keywords: ['definitely', 'certainly', 'guaranteed', 'proven', 'sure', 'confident', 'absolutely'],
    contextClues: ['facts', 'evidence', 'research', 'proven'],
    voiceParams: { pitch_variation: 0.08, pace_multiplier: 0.95, volume_adjustment: 0.05 },
    expressions: ['micro_nod', 'direct_gaze'],
    gestures: ['finger_point', 'body_lean'],
    intensity_range: [0.5, 0.8]
  },
  curious: {
    keywords: ['wonder', 'what if', 'how', 'why', 'interesting', 'curious', '?'],
    contextClues: ['question', 'exploration', 'investigation', 'mystery'],
    voiceParams: { pitch_variation: 0.12, pace_multiplier: 0.9, volume_adjustment: 0.02 },
    expressions: ['head_tilt', 'eyebrow_raise', 'eye_movement'],
    gestures: ['thinking_pose', 'head_gesture'],
    intensity_range: [0.4, 0.7]
  },
  serious: {
    keywords: ['important', 'crucial', 'critical', 'serious', 'warning', 'careful', 'attention'],
    contextClues: ['warning', 'caution', 'danger', 'significant'],
    voiceParams: { pitch_variation: 0.06, pace_multiplier: 0.85, volume_adjustment: -0.02 },
    expressions: ['direct_gaze', 'slight_frown'],
    gestures: ['body_lean', 'finger_point'],
    intensity_range: [0.6, 0.9]
  },
  friendly: {
    keywords: ['welcome', 'hello', 'glad', 'happy', 'nice', 'pleasure', 'thanks'],
    contextClues: ['greeting', 'introduction', 'appreciation', 'courtesy'],
    voiceParams: { pitch_variation: 0.1, pace_multiplier: 1.0, volume_adjustment: 0.03 },
    expressions: ['subtle_smile', 'eye_movement'],
    gestures: ['open_palm', 'hand_movement'],
    intensity_range: [0.3, 0.6]
  },
  explanatory: {
    keywords: ['because', 'since', 'therefore', 'explain', 'understand', 'see', 'example'],
    contextClues: ['explanation', 'clarification', 'example', 'demonstration'],
    voiceParams: { pitch_variation: 0.09, pace_multiplier: 0.9, volume_adjustment: 0.0 },
    expressions: ['micro_nod', 'eye_movement'],
    gestures: ['hand_movement', 'finger_point'],
    intensity_range: [0.3, 0.6]
  },
  urgent: {
    keywords: ['now', 'immediately', 'quickly', 'urgent', 'hurry', 'soon', 'deadline'],
    contextClues: ['time pressure', 'deadline', 'immediate action', 'urgency'],
    voiceParams: { pitch_variation: 0.13, pace_multiplier: 1.15, volume_adjustment: 0.08 },
    expressions: ['alert_gaze', 'micro_nod'],
    gestures: ['finger_point', 'hand_movement'],
    intensity_range: [0.7, 1.0]
  }
}

// Content type emotional adaptations
const CONTENT_ADAPTATIONS = {
  educational: {
    primary_emotions: ['explanatory', 'friendly', 'confident'],
    tone_modulation: 0.7,
    emphasis_frequency: 0.8
  },
  entertainment: {
    primary_emotions: ['excited', 'friendly', 'curious'],
    tone_modulation: 1.0,
    emphasis_frequency: 1.0
  },
  business: {
    primary_emotions: ['confident', 'serious', 'explanatory'],
    tone_modulation: 0.6,
    emphasis_frequency: 0.7
  },
  personal: {
    primary_emotions: ['friendly', 'excited', 'curious'],
    tone_modulation: 0.9,
    emphasis_frequency: 0.8
  },
  promotional: {
    primary_emotions: ['excited', 'urgent', 'confident'],
    tone_modulation: 1.0,
    emphasis_frequency: 1.2
  }
}

// Audience adaptations
const AUDIENCE_ADAPTATIONS = {
  children: {
    emotion_intensity_multiplier: 1.2,
    preferred_emotions: ['excited', 'friendly', 'curious'],
    voice_adjustment: { pitch_variation: 0.15, pace_multiplier: 0.9 }
  },
  teens: {
    emotion_intensity_multiplier: 1.1,
    preferred_emotions: ['excited', 'confident', 'curious'],
    voice_adjustment: { pitch_variation: 0.12, pace_multiplier: 1.05 }
  },
  adults: {
    emotion_intensity_multiplier: 1.0,
    preferred_emotions: ['confident', 'explanatory', 'friendly'],
    voice_adjustment: { pitch_variation: 0.1, pace_multiplier: 1.0 }
  },
  seniors: {
    emotion_intensity_multiplier: 0.8,
    preferred_emotions: ['friendly', 'explanatory', 'confident'],
    voice_adjustment: { pitch_variation: 0.08, pace_multiplier: 0.9 }
  },
  professional: {
    emotion_intensity_multiplier: 0.7,
    preferred_emotions: ['confident', 'serious', 'explanatory'],
    voice_adjustment: { pitch_variation: 0.06, pace_multiplier: 0.95 }
  }
}

export function EmotionMappingEngine({
  script,
  duration,
  onEmotionMappingComplete,
  existingEmotionMaps = []
}: EmotionMappingEngineProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [settings, setSettings] = useState<EmotionSettings>({
    emotionSensitivity: 0.8,
    adaptiveTone: true,
    contextualEmotions: true,
    subtleTransitions: true,
    emphasisDetection: true,
    audienceAdaptation: 'adults',
    contentType: 'educational'
  })
  
  const [generatedEmotionMaps, setGeneratedEmotionMaps] = useState<EmotionMapping[]>(existingEmotionMaps)
  const [emotionAnalysis, setEmotionAnalysis] = useState<{
    dominantEmotions: Array<{ emotion: string; percentage: number }>
    emotionalVariation: number
    transitionQuality: number
    audienceAlignment: number
  } | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  // Analyze script and generate emotion mappings
  const generateEmotionMapping = useCallback(async () => {
    setIsGenerating(true)
    
    try {
      const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 0)
      const emotionMaps: EmotionMapping[] = []
      
      // Get content and audience adaptations
      const contentAdaptation = CONTENT_ADAPTATIONS[settings.contentType]
      const audienceAdaptation = AUDIENCE_ADAPTATIONS[settings.audienceAdaptation]
      
      sentences.forEach((sentence, index) => {
        const timePerSentence = duration / sentences.length
        const startTime = index * timePerSentence
        const endTime = startTime + timePerSentence
        
        // Detect emotions in this sentence
        const detectedEmotion = detectPrimaryEmotion(
          sentence, 
          contentAdaptation.primary_emotions,
          audienceAdaptation.preferred_emotions
        )
        
        // Calculate emotion intensity
        const baseIntensity = calculateEmotionIntensity(sentence, detectedEmotion)
        const adjustedIntensity = baseIntensity * 
          settings.emotionSensitivity * 
          audienceAdaptation.emotion_intensity_multiplier *
          contentAdaptation.tone_modulation
        
        // Generate voice adjustments
        const voiceAdjustments = generateVoiceAdjustments(
          detectedEmotion,
          adjustedIntensity,
          startTime,
          endTime,
          audienceAdaptation.voice_adjustment
        )
        
        // Generate expression adjustments
        const expressionAdjustments = generateExpressionAdjustments(
          detectedEmotion,
          adjustedIntensity,
          startTime
        )
        
        // Generate gesture suggestions
        const gestureSuggestions = generateGestureSuggestions(
          detectedEmotion,
          adjustedIntensity,
          startTime,
          sentence
        )
        
        emotionMaps.push({
          segment_id: `emotion_${index}`,
          start_time: startTime,
          end_time: endTime,
          detected_emotion: detectedEmotion,
          emotion_intensity: Math.min(1.0, adjustedIntensity),
          voice_adjustments: voiceAdjustments,
          expression_adjustments: expressionAdjustments,
          gesture_suggestions: gestureSuggestions
        })
      })
      
      // Apply emotion transitions if enabled
      if (settings.subtleTransitions) {
        applyEmotionTransitions(emotionMaps)
      }
      
      // Generate analysis
      const analysis = analyzeEmotionalContent(emotionMaps)
      setEmotionAnalysis(analysis)
      
      setGeneratedEmotionMaps(emotionMaps)
      onEmotionMappingComplete(emotionMaps)
      
    } catch (error) {
      console.error('Failed to generate emotion mapping:', error)
      alert('Failed to generate emotion mapping. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }, [script, duration, settings, onEmotionMappingComplete])

  // Detect primary emotion in text segment
  const detectPrimaryEmotion = (
    text: string, 
    primaryEmotions: string[], 
    preferredEmotions: string[]
  ): EmotionMapping['detected_emotion'] => {
    let bestMatch: { emotion: string; score: number } = { emotion: 'neutral', score: 0 }
    
    Object.entries(EMOTION_PATTERNS).forEach(([emotion, pattern]) => {
      let score = 0
      
      // Keyword matching
      pattern.keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
        const matches = (text.match(regex) || []).length
        score += matches * 2
      })
      
      // Context clue matching
      pattern.contextClues.forEach(clue => {
        if (text.toLowerCase().includes(clue.toLowerCase())) {
          score += 1
        }
      })
      
      // Bonus for primary emotions (content type)
      if (primaryEmotions.includes(emotion)) {
        score *= 1.5
      }
      
      // Bonus for preferred emotions (audience type)
      if (preferredEmotions.includes(emotion)) {
        score *= 1.3
      }
      
      if (score > bestMatch.score) {
        bestMatch = { emotion, score }
      }
    })
    
    return bestMatch.emotion as EmotionMapping['detected_emotion']
  }

  // Calculate emotion intensity based on context
  const calculateEmotionIntensity = (text: string, emotion: string): number => {
    const pattern = EMOTION_PATTERNS[emotion as keyof typeof EMOTION_PATTERNS]
    if (!pattern) return 0.3
    
    let intensity = 0.3 // Base intensity
    
    // Punctuation indicators
    const exclamationCount = (text.match(/!/g) || []).length
    const questionCount = (text.match(/\?/g) || []).length
    intensity += exclamationCount * 0.2 + questionCount * 0.1
    
    // Emphasis markers
    if (text.includes('[EMPHASIS]')) {
      intensity += 0.3
    }
    
    // ALL CAPS words
    const capsWords = (text.match(/\b[A-Z]{3,}\b/g) || []).length
    intensity += capsWords * 0.15
    
    // Multiple keyword occurrences
    const keywordCount = pattern.keywords.filter(keyword =>
      text.toLowerCase().includes(keyword.toLowerCase())
    ).length
    intensity += keywordCount * 0.1
    
    // Ensure within emotion's natural range
    const [minIntensity, maxIntensity] = pattern.intensity_range
    return Math.max(minIntensity, Math.min(maxIntensity, intensity))
  }

  // Generate voice adjustments for emotion
  const generateVoiceAdjustments = (
    emotion: string,
    intensity: number,
    startTime: number,
    endTime: number,
    audienceAdjustment: any
  ): VoiceModulation[] => {
    const pattern = EMOTION_PATTERNS[emotion as keyof typeof EMOTION_PATTERNS]
    if (!pattern) return []
    
    const voiceModulation: VoiceModulation = {
      id: `voice_emotion_${Date.now()}_${startTime}`,
      startTime,
      endTime,
      modulationType: 'emotion',
      intensity,
      parameters: {
        emotion_type: emotion as any,
        pitch_variation: (pattern.voiceParams.pitch_variation + (audienceAdjustment.pitch_variation || 0)) * intensity,
        pace_multiplier: pattern.voiceParams.pace_multiplier + ((audienceAdjustment.pace_multiplier || 1) - 1) * intensity,
        volume_adjustment: pattern.voiceParams.volume_adjustment * intensity
      }
    }
    
    return [voiceModulation]
  }

  // Generate micro-expressions for emotion
  const generateExpressionAdjustments = (
    emotion: string,
    intensity: number,
    startTime: number
  ): MicroExpression[] => {
    const pattern = EMOTION_PATTERNS[emotion as keyof typeof EMOTION_PATTERNS]
    if (!pattern) return []
    
    return pattern.expressions.map((expressionType, index) => ({
      id: `expr_emotion_${Date.now()}_${startTime}_${index}`,
      type: expressionType as MicroExpression['type'],
      timing: startTime + (index * 0.5), // Stagger expressions
      duration: expressionType === 'blink' ? 0.15 : 1.0,
      intensity: intensity * 0.8, // Slightly reduce for subtlety
      context: 'emotion' as MicroExpression['context']
    }))
  }

  // Generate gesture suggestions for emotion
  const generateGestureSuggestions = (
    emotion: string,
    intensity: number,
    startTime: number,
    context: string
  ): GestureIntelligence[] => {
    const pattern = EMOTION_PATTERNS[emotion as keyof typeof EMOTION_PATTERNS]
    if (!pattern) return []
    
    return pattern.gestures.slice(0, intensity > 0.7 ? 2 : 1).map((gestureType, index) => ({
      id: `gesture_emotion_${Date.now()}_${startTime}_${index}`,
      timing: startTime + (index * 1.0),
      duration: 1.5 + (intensity * 0.5),
      gestureType: gestureType as GestureIntelligence['gestureType'],
      context: context.substring(0, 50),
      intensity: intensity > 0.7 ? 'pronounced' : intensity > 0.4 ? 'moderate' : 'subtle',
      coordination: {
        matches_voice_emphasis: true,
        relates_to_content: true,
        natural_flow: true
      }
    }))
  }

  // Apply smooth transitions between emotions
  const applyEmotionTransitions = (emotionMaps: EmotionMapping[]) => {
    for (let i = 1; i < emotionMaps.length; i++) {
      const prevEmotion = emotionMaps[i - 1]
      const currentEmotion = emotionMaps[i]
      
      // If emotions are very different, add a transition
      const emotionDistance = calculateEmotionDistance(
        prevEmotion.detected_emotion,
        currentEmotion.detected_emotion
      )
      
      if (emotionDistance > 0.5) {
        // Reduce intensity slightly for smoother transition
        currentEmotion.emotion_intensity *= 0.8
        
        // Add transition voice modulation
        const transitionModulation: VoiceModulation = {
          id: `transition_${Date.now()}_${i}`,
          startTime: prevEmotion.end_time - 0.2,
          endTime: currentEmotion.start_time + 0.2,
          modulationType: 'emotion',
          intensity: 0.3,
          parameters: {
            emotion_type: 'confidence' as any,
            pitch_variation: 0.05,
            pace_multiplier: 0.98,
            volume_adjustment: -0.02
          }
        }
        
        currentEmotion.voice_adjustments.unshift(transitionModulation)
      }
    }
  }

  // Calculate distance between emotions (for transitions)
  const calculateEmotionDistance = (emotion1: string, emotion2: string): number => {
    if (emotion1 === emotion2) return 0
    
    // Define emotion similarity clusters
    const clusters = [
      ['excited', 'friendly', 'curious'],
      ['confident', 'serious', 'explanatory'],
      ['urgent', 'serious', 'confident']
    ]
    
    // Find if emotions are in the same cluster
    for (const cluster of clusters) {
      if (cluster.includes(emotion1) && cluster.includes(emotion2)) {
        return 0.3 // Similar emotions
      }
    }
    
    return 1.0 // Very different emotions
  }

  // Analyze emotional content distribution
  const analyzeEmotionalContent = (emotionMaps: EmotionMapping[]) => {
    const emotionCounts: Record<string, number> = {}
    let totalIntensity = 0
    
    emotionMaps.forEach(map => {
      emotionCounts[map.detected_emotion] = (emotionCounts[map.detected_emotion] || 0) + 1
      totalIntensity += map.emotion_intensity
    })
    
    const dominantEmotions = Object.entries(emotionCounts)
      .map(([emotion, count]) => ({
        emotion,
        percentage: (count / emotionMaps.length) * 100
      }))
      .sort((a, b) => b.percentage - a.percentage)
      .slice(0, 3)
    
    // Calculate emotional variation (good for engagement)
    const uniqueEmotions = Object.keys(emotionCounts).length
    const emotionalVariation = Math.min(1.0, uniqueEmotions / 5)
    
    // Calculate transition quality
    let transitionScore = 1.0
    for (let i = 1; i < emotionMaps.length; i++) {
      const distance = calculateEmotionDistance(
        emotionMaps[i - 1].detected_emotion,
        emotionMaps[i].detected_emotion
      )
      if (distance > 0.8) transitionScore -= 0.1 // Penalty for jarring transitions
    }
    
    // Calculate audience alignment
    const audienceAdaptation = AUDIENCE_ADAPTATIONS[settings.audienceAdaptation]
    const alignmentScore = dominantEmotions.reduce((score, { emotion, percentage }) => {
      return score + (audienceAdaptation.preferred_emotions.includes(emotion) ? percentage / 100 : 0)
    }, 0)
    
    return {
      dominantEmotions,
      emotionalVariation,
      transitionQuality: Math.max(0, transitionScore),
      audienceAlignment: alignmentScore
    }
  }

  const clearEmotionMaps = () => {
    setGeneratedEmotionMaps([])
    setEmotionAnalysis(null)
    onEmotionMappingComplete([])
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Emotion Mapping Engine</h3>
        <p className="text-sm text-gray-600">
          Automatically detect and map emotions throughout your script to create natural, engaging AI avatar delivery
        </p>
      </div>

      {/* Emotion Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Emotion Intelligence Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Emotion Sensitivity: {Math.round(settings.emotionSensitivity * 100)}%
              </label>
              <Slider
                value={[settings.emotionSensitivity]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, emotionSensitivity: value }))}
                min={0.1}
                max={1.0}
                step={0.1}
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">How strongly emotions are expressed</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Content Type
              </label>
              <select
                value={settings.contentType}
                onChange={(e) => setSettings(prev => ({ ...prev, contentType: e.target.value as any }))}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="educational">Educational</option>
                <option value="entertainment">Entertainment</option>
                <option value="business">Business</option>
                <option value="personal">Personal</option>
                <option value="promotional">Promotional</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Adapts emotion style to content type</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Target Audience
              </label>
              <select
                value={settings.audienceAdaptation}
                onChange={(e) => setSettings(prev => ({ ...prev, audienceAdaptation: e.target.value as any }))}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="children">Children</option>
                <option value="teens">Teenagers</option>
                <option value="adults">Adults</option>
                <option value="seniors">Seniors</option>
                <option value="professional">Professional</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Adapts emotional intensity and style</p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Adaptive Tone</label>
                <Switch
                  checked={settings.adaptiveTone}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, adaptiveTone: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Contextual Emotions</label>
                <Switch
                  checked={settings.contextualEmotions}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, contextualEmotions: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Subtle Transitions</label>
                <Switch
                  checked={settings.subtleTransitions}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, subtleTransitions: checked }))}
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Emphasis Detection</label>
                <Switch
                  checked={settings.emphasisDetection}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emphasisDetection: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Controls */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={generateEmotionMapping}
          disabled={isGenerating || !script.trim()}
          className="bg-gradient-to-r from-pink-600 to-rose-600 hover:from-pink-700 hover:to-rose-700"
        >
          {isGenerating ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Mapping Emotions...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              Generate Emotion Mapping
            </>
          )}
        </Button>
        
        {generatedEmotionMaps.length > 0 && (
          <Button variant="outline" onClick={clearEmotionMaps}>
            Clear Mapping
          </Button>
        )}
        
        <Button
          variant="outline"
          onClick={() => setPreviewMode(!previewMode)}
          disabled={generatedEmotionMaps.length === 0}
        >
          {previewMode ? 'Hide Preview' : 'Preview Emotions'}
        </Button>
      </div>

      {/* Emotion Analysis */}
      {emotionAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Emotional Content Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-pink-600">{generatedEmotionMaps.length}</div>
                <div className="text-sm text-gray-600">Emotion Segments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(emotionAnalysis.emotionalVariation * 100)}%
                </div>
                <div className="text-sm text-gray-600">Emotional Variety</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(emotionAnalysis.transitionQuality * 100)}%
                </div>
                <div className="text-sm text-gray-600">Transition Quality</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(emotionAnalysis.audienceAlignment * 100)}%
                </div>
                <div className="text-sm text-gray-600">Audience Alignment</div>
              </div>
            </div>

            {/* Dominant Emotions */}
            <div className="mb-4">
              <h4 className="font-medium mb-2">Dominant Emotions:</h4>
              <div className="flex flex-wrap gap-2">
                {emotionAnalysis.dominantEmotions.map((emotion, index) => (
                  <Badge 
                    key={emotion.emotion} 
                    variant="outline" 
                    className={`${
                      index === 0 ? 'bg-pink-100 text-pink-800' :
                      index === 1 ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {emotion.emotion}: {emotion.percentage.toFixed(1)}%
                  </Badge>
                ))}
              </div>
            </div>

            {/* Preview Timeline */}
            {previewMode && (
              <div className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium mb-3">Emotion Timeline Preview</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {generatedEmotionMaps.slice(0, 10).map((emotionMap) => (
                    <div key={emotionMap.segment_id} className="flex items-center justify-between text-sm">
                      <span className="font-medium capitalize">{emotionMap.detected_emotion}</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{emotionMap.start_time.toFixed(1)}s</Badge>
                        <Badge variant="outline">{Math.round(emotionMap.emotion_intensity * 100)}%</Badge>
                        <Badge variant="outline" className="bg-pink-50">
                          {emotionMap.voice_adjustments.length + emotionMap.expression_adjustments.length + emotionMap.gesture_suggestions.length} adjustments
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {generatedEmotionMaps.length > 10 && (
                    <div className="text-xs text-gray-500 text-center pt-2">
                      ... and {generatedEmotionMaps.length - 10} more emotion segments
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quality Assessment */}
      {emotionAnalysis && (
        <Card className="bg-gradient-to-r from-pink-50 to-rose-50 border-pink-200">
          <CardHeader>
            <CardTitle className="text-base text-pink-800">Emotional Intelligence Quality Assessment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <div className="text-lg font-bold text-pink-700">
                  {Math.min(98, 70 + (emotionAnalysis.emotionalVariation * 28)).toFixed(0)}%
                </div>
                <div className="text-sm text-pink-600">Engagement Potential</div>
              </div>
              <div>
                <div className="text-lg font-bold text-rose-700">
                  {Math.min(95, 80 + (emotionAnalysis.transitionQuality * 15)).toFixed(0)}%
                </div>
                <div className="text-sm text-rose-600">Natural Flow</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-700">
                  {Math.min(96, 75 + (emotionAnalysis.audienceAlignment * 21)).toFixed(0)}%
                </div>
                <div className="text-sm text-purple-600">Audience Match</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}