'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useProjectWizard } from '@/context/project-wizard-context'

interface AudienceOption {
  id: 'beginner' | 'intermediate' | 'advanced' | 'mixed'
  title: string
  description: string
  characteristics: string[]
  icon: string
}

interface VideoStyleOption {
  id: 'explainer' | 'tutorial' | 'review' | 'comparison' | 'walkthrough'
  title: string
  description: string
  duration: string
  best_for: string[]
  icon: string
}

interface PlatformOption {
  id: 'youtube' | 'tiktok' | 'linkedin' | 'twitter' | 'facebook'
  name: string
  description: string
  optimal_length: string
  format_tips: string[]
  audience_type: string
  color: string
}

const audienceOptions: AudienceOption[] = [
  {
    id: 'beginner',
    title: 'Beginner',
    description: 'New to the topic, needs foundational knowledge',
    characteristics: ['No prior experience', 'Needs step-by-step guidance', 'Values clear explanations'],
    icon: '🌱'
  },
  {
    id: 'intermediate',
    title: 'Intermediate',
    description: 'Some experience, looking to expand knowledge',
    characteristics: ['Basic understanding', 'Wants practical examples', 'Ready for deeper concepts'],
    icon: '📈'
  },
  {
    id: 'advanced',
    title: 'Advanced',
    description: 'Experienced users seeking advanced techniques',
    characteristics: ['Strong foundation', 'Interested in optimization', 'Values expert insights'],
    icon: '🚀'
  },
  {
    id: 'mixed',
    title: 'Mixed Audience',
    description: 'Varying skill levels, broad appeal content',
    characteristics: ['Diverse experience levels', 'Needs layered explanations', 'Benefits from recap sections'],
    icon: '👥'
  }
]

const videoStyleOptions: VideoStyleOption[] = [
  {
    id: 'explainer',
    title: 'Explainer Video',
    description: 'Clear, concise explanation of concepts and processes',
    duration: '2-5 minutes',
    best_for: ['Introducing new concepts', 'Simplifying complex topics', 'Marketing content'],
    icon: '💡'
  },
  {
    id: 'tutorial',
    title: 'Step-by-Step Tutorial',
    description: 'Detailed walkthrough with hands-on demonstrations',
    duration: '5-15 minutes',
    best_for: ['Teaching practical skills', 'Software demonstrations', 'How-to content'],
    icon: '🎯'
  },
  {
    id: 'review',
    title: 'Review & Analysis',
    description: 'In-depth analysis with pros, cons, and recommendations',
    duration: '8-20 minutes',
    best_for: ['Product comparisons', 'Feature analysis', 'Decision-making content'],
    icon: '⭐'
  },
  {
    id: 'comparison',
    title: 'Comparison Video',
    description: 'Side-by-side analysis of alternatives and options',
    duration: '6-12 minutes',
    best_for: ['Tool comparisons', 'Alternative solutions', 'Buying guides'],
    icon: '⚖️'
  },
  {
    id: 'walkthrough',
    title: 'Live Walkthrough',
    description: 'Real-time demonstration with commentary',
    duration: '10-30 minutes',
    best_for: ['Complex workflows', 'Live problem-solving', 'Interactive content'],
    icon: '🎬'
  }
]

const platformOptions: PlatformOption[] = [
  {
    id: 'youtube',
    name: 'YouTube',
    description: 'Long-form educational content with high discoverability',
    optimal_length: '5-15 minutes',
    format_tips: ['Strong intro hook', 'Clear chapters/timestamps', 'Engaging thumbnails', 'SEO-optimized titles'],
    audience_type: 'Learning-focused, all ages',
    color: 'bg-red-100 text-red-800 border-red-200'
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    description: 'Short, engaging content with viral potential',
    optimal_length: '15-60 seconds',
    format_tips: ['Hook in first 3 seconds', 'Vertical format', 'Trending sounds', 'Quick tips format'],
    audience_type: 'Gen Z, millennials',
    color: 'bg-pink-100 text-pink-800 border-pink-200'
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    description: 'Professional content for business audiences',
    optimal_length: '1-3 minutes',
    format_tips: ['Professional tone', 'Business value focus', 'Industry insights', 'Thought leadership'],
    audience_type: 'Professionals, B2B',
    color: 'bg-blue-100 text-blue-800 border-blue-200'
  },
  {
    id: 'twitter',
    name: 'Twitter/X',
    description: 'Quick insights and thread-worthy content',
    optimal_length: '30-90 seconds',
    format_tips: ['Bite-sized insights', 'Thread potential', 'Quotable moments', 'Current trends'],
    audience_type: 'News-focused, tech-savvy',
    color: 'bg-gray-100 text-gray-800 border-gray-200'
  },
  {
    id: 'facebook',
    name: 'Facebook',
    description: 'Community-focused content with broad reach',
    optimal_length: '1-3 minutes',
    format_tips: ['Community engagement', 'Shareable content', 'Clear captions', 'Group-friendly'],
    audience_type: 'General public, older millennials',
    color: 'bg-blue-100 text-blue-800 border-blue-200'
  }
]

export function AudienceStyleStep() {
  const { projectData, dispatch } = useProjectWizard()
  const [selectedAudience, setSelectedAudience] = useState(projectData.target_audience)
  const [selectedStyle, setSelectedStyle] = useState(projectData.video_style)
  const [selectedPlatforms, setSelectedPlatforms] = useState(projectData.platforms)

  const handleAudienceSelect = (audience: AudienceOption['id']) => {
    setSelectedAudience(audience)
    updateProjectData({ target_audience: audience })
  }

  const handleStyleSelect = (style: VideoStyleOption['id']) => {
    setSelectedStyle(style)
    updateProjectData({ video_style: style })
  }

  const handlePlatformToggle = (platformId: PlatformOption['id']) => {
    const newPlatforms = selectedPlatforms.includes(platformId)
      ? selectedPlatforms.filter(p => p !== platformId)
      : [...selectedPlatforms, platformId]
    
    setSelectedPlatforms(newPlatforms)
    updateProjectData({ platforms: newPlatforms })
  }

  const updateProjectData = (updates: { target_audience?: any; video_style?: any; platforms?: any }) => {
    dispatch({
      type: 'UPDATE_AUDIENCE_STYLE',
      payload: {
        target_audience: updates.target_audience || selectedAudience,
        video_style: updates.video_style || selectedStyle,
        platforms: updates.platforms || selectedPlatforms
      }
    })
  }

  const getStyleRecommendations = () => {
    if (!selectedAudience || !selectedStyle) return []

    const recommendations = []
    
    if (selectedAudience === 'beginner' && selectedStyle === 'tutorial') {
      recommendations.push('Include plenty of context and background information')
      recommendations.push('Use slower pacing and repeat key concepts')
      recommendations.push('Provide links to prerequisite knowledge')
    }
    
    if (selectedAudience === 'advanced' && selectedStyle === 'walkthrough') {
      recommendations.push('Focus on advanced techniques and optimizations')
      recommendations.push('Skip basic explanations but link to them')
      recommendations.push('Include performance tips and best practices')
    }
    
    if (selectedPlatforms.includes('tiktok') && selectedStyle === 'explainer') {
      recommendations.push('Create multiple short segments from your content')
      recommendations.push('Use eye-catching visuals and animations')
      recommendations.push('Include captions for silent viewing')
    }

    return recommendations
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Audience & Style</h3>
        <p className="text-sm text-gray-600 mb-6">
          Define your target audience and video style to optimize content structure and delivery
        </p>
      </div>

      {/* Target Audience Selection */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Target Audience</h4>
        <div className="grid gap-4 md:grid-cols-2">
          {audienceOptions.map((option) => (
            <Card
              key={option.id}
              className={`cursor-pointer transition-all ${
                selectedAudience === option.id
                  ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                  : 'hover:border-gray-300'
              }`}
              onClick={() => handleAudienceSelect(option.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <span className="text-2xl">{option.icon}</span>
                  <div>
                    <h5 className="font-medium">{option.title}</h5>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </div>
                <div className="space-y-1">
                  {option.characteristics.map((char, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                      <span className="text-xs text-gray-600">{char}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Video Style Selection */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Video Style</h4>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {videoStyleOptions.map((option) => (
            <Card
              key={option.id}
              className={`cursor-pointer transition-all ${
                selectedStyle === option.id
                  ? 'ring-2 ring-purple-500 border-purple-200 bg-purple-50'
                  : 'hover:border-gray-300'
              }`}
              onClick={() => handleStyleSelect(option.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <span className="text-2xl">{option.icon}</span>
                  <div>
                    <h5 className="font-medium">{option.title}</h5>
                    <Badge variant="outline" className="text-xs mt-1">
                      {option.duration}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                <div>
                  <p className="text-xs font-medium text-gray-700 mb-1">Best for:</p>
                  <div className="space-y-1">
                    {option.best_for.slice(0, 2).map((use, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-1 h-1 bg-purple-500 rounded-full" />
                        <span className="text-xs text-gray-600">{use}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Platform Selection */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Publishing Platforms</h4>
        <p className="text-sm text-gray-600">Select where you plan to publish your video (you can choose multiple)</p>
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
          {platformOptions.map((platform) => (
            <Card
              key={platform.id}
              className={`cursor-pointer transition-all ${
                selectedPlatforms.includes(platform.id)
                  ? 'ring-2 ring-green-500 border-green-200 bg-green-50'
                  : 'hover:border-gray-300'
              }`}
              onClick={() => handlePlatformToggle(platform.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium">{platform.name}</h5>
                  <Badge className={platform.color}>
                    {platform.optimal_length}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-3">{platform.description}</p>
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-700">Audience: {platform.audience_type}</p>
                  <div className="flex flex-wrap gap-1">
                    {platform.format_tips.slice(0, 2).map((tip, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tip}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Personalized Recommendations */}
      {selectedAudience && selectedStyle && selectedPlatforms.length > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              Personalized Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Content Strategy</h5>
                <div className="space-y-1">
                  {getStyleRecommendations().map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{rec}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Platform Optimization</h5>
                <div className="space-y-2">
                  {selectedPlatforms.slice(0, 3).map((platformId) => {
                    const platform = platformOptions.find(p => p.id === platformId)
                    return (
                      <div key={platformId} className="flex items-center space-x-2">
                        <Badge className={platform?.color}>
                          {platform?.name}
                        </Badge>
                        <span className="text-sm text-gray-600">{platform?.optimal_length}</span>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selection Summary */}
      {selectedAudience && selectedStyle && (
        <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
          <span className="text-sm text-gray-600">Selected:</span>
          <Badge className="bg-blue-100 text-blue-800">
            {audienceOptions.find(a => a.id === selectedAudience)?.title} audience
          </Badge>
          <Badge className="bg-purple-100 text-purple-800">
            {videoStyleOptions.find(s => s.id === selectedStyle)?.title}
          </Badge>
          {selectedPlatforms.map(platformId => (
            <Badge key={platformId} className="bg-green-100 text-green-800">
              {platformOptions.find(p => p.id === platformId)?.name}
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}