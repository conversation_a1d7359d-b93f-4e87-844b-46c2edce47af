'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

const voiceProfiles = [
  { id: 1, name: 'Professional Male', accent: 'American', age: 'Adult', tone: 'Authoritative', sample: '/voices/prof-male.mp3' },
  { id: 2, name: 'Friendly Female', accent: 'British', age: 'Young Adult', tone: 'Warm', sample: '/voices/friendly-female.mp3' },
  { id: 3, name: 'Corporate Narrator', accent: 'Canadian', age: 'Middle-aged', tone: 'Neutral', sample: '/voices/corporate.mp3' },
  { id: 4, name: 'Energetic Host', accent: 'Australian', age: 'Young', tone: 'Enthusiastic', sample: '/voices/energetic.mp3' },
  { id: 5, name: 'Cal<PERSON> Instructor', accent: 'Indian', age: 'Adult', tone: 'Patient', sample: '/voices/instructor.mp3' },
  { id: 6, name: 'News Anchor', accent: 'American', age: 'Adult', tone: 'Professional', sample: '/voices/news.mp3' },
]

const translationPairs = [
  { from: 'English', to: 'Spanish', accuracy: 98, time: '2-3 min' },
  { from: 'English', to: 'French', accuracy: 97, time: '2-3 min' },
  { from: 'English', to: 'German', accuracy: 96, time: '3-4 min' },
  { from: 'Spanish', to: 'English', accuracy: 98, time: '2-3 min' },
  { from: 'Chinese', to: 'English', accuracy: 95, time: '4-5 min' },
  { from: 'Japanese', to: 'English', accuracy: 94, time: '4-5 min' },
]

export default function AIVoicePage() {
  const [selectedVoice, setSelectedVoice] = useState<number | null>(null)
  const [sourceLanguage, setSourceLanguage] = useState('English')
  const [targetLanguage, setTargetLanguage] = useState('Spanish')
  const [inputText, setInputText] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [activeTab, setActiveTab] = useState<'voice' | 'translate' | 'clone'>('voice')

  const handleProcess = async () => {
    if (!inputText.trim()) return
    
    setIsProcessing(true)
    setProcessingProgress(0)
    
    const interval = setInterval(() => {
      setProcessingProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsProcessing(false)
          return 100
        }
        return prev + Math.random() * 12
      })
    }, 400)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">AI Voice & Translation</h1>
          <p className="text-premium-400">Advanced voice synthesis, cloning, and real-time translation</p>
        </div>
        <div className="flex items-center gap-4">
          <Badge className="bg-gradient-emerald text-white border-0 px-4 py-2">
            140+ Languages
          </Badge>
          <Badge className="bg-gradient-violet text-white border-0 px-4 py-2">
            Voice Cloning
          </Badge>
        </div>
      </div>

      {/* Service Tabs */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <div className="flex space-x-1 bg-premium-800/30 rounded-lg p-1 mb-6">
          {[
            { id: 'voice', label: 'Text to Speech', icon: '🎤' },
            { id: 'translate', label: 'Translation', icon: '🌐' },
            { id: 'clone', label: 'Voice Cloning', icon: '🎭' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-gradient-tech text-white shadow-lg'
                  : 'text-premium-400 hover:text-white hover:bg-premium-700/50'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Text to Speech Tab */}
        {activeTab === 'voice' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Voice Selection */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Choose Voice Profile</h3>
                <div className="space-y-3">
                  {voiceProfiles.map((voice) => (
                    <div
                      key={voice.id}
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        selectedVoice === voice.id
                          ? 'border-ocean-primary bg-ocean-primary/10'
                          : 'border-premium-600/30 hover:border-premium-500/50'
                      }`}
                      onClick={() => setSelectedVoice(voice.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-white font-medium">{voice.name}</h4>
                          <p className="text-premium-400 text-sm">
                            {voice.accent} • {voice.age} • {voice.tone}
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          Preview
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Voice Settings */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Voice Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Speed</label>
                    <input type="range" min="0.5" max="2" step="0.1" defaultValue="1" className="w-full" />
                    <div className="flex justify-between text-xs text-premium-400 mt-1">
                      <span>0.5x</span>
                      <span>Normal</span>
                      <span>2x</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Pitch</label>
                    <input type="range" min="-50" max="50" step="5" defaultValue="0" className="w-full" />
                    <div className="flex justify-between text-xs text-premium-400 mt-1">
                      <span>Lower</span>
                      <span>Natural</span>
                      <span>Higher</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Emotion</label>
                    <select className="w-full bg-premium-800/50 border border-premium-600/30 rounded-lg px-3 py-2 text-white">
                      <option>Neutral</option>
                      <option>Happy</option>
                      <option>Sad</option>
                      <option>Excited</option>
                      <option>Calm</option>
                      <option>Serious</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Translation Tab */}
        {activeTab === 'translate' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">From Language</label>
                <select 
                  value={sourceLanguage}
                  onChange={(e) => setSourceLanguage(e.target.value)}
                  className="w-full bg-premium-800/50 border border-premium-600/30 rounded-lg px-3 py-2 text-white"
                >
                  <option>English</option>
                  <option>Spanish</option>
                  <option>French</option>
                  <option>German</option>
                  <option>Chinese</option>
                  <option>Japanese</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-white mb-2">To Language</label>
                <select 
                  value={targetLanguage}
                  onChange={(e) => setTargetLanguage(e.target.value)}
                  className="w-full bg-premium-800/50 border border-premium-600/30 rounded-lg px-3 py-2 text-white"
                >
                  <option>Spanish</option>
                  <option>English</option>
                  <option>French</option>
                  <option>German</option>
                  <option>Chinese</option>
                  <option>Japanese</option>
                </select>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Popular Translation Pairs</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {translationPairs.map((pair, index) => (
                  <div key={index} className="p-4 bg-premium-800/30 rounded-lg border border-premium-600/20">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">{pair.from} → {pair.to}</span>
                      <Badge className="bg-gradient-emerald text-white border-0 text-xs">
                        {pair.accuracy}% accuracy
                      </Badge>
                    </div>
                    <p className="text-premium-400 text-sm">Processing time: {pair.time}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Voice Cloning Tab */}
        {activeTab === 'clone' && (
          <div className="space-y-6">
            <div className="text-center p-8 border-2 border-dashed border-premium-600/30 rounded-lg">
              <div className="w-16 h-16 bg-gradient-violet rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 17a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"/>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Upload Voice Sample</h3>
              <p className="text-premium-400 mb-4">Upload 5-10 minutes of clear audio to create a custom voice clone</p>
              <Button variant="tech">
                Choose Audio File
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-premium-800/30 p-4 border border-premium-600/20">
                <h4 className="text-white font-medium mb-2">Requirements</h4>
                <ul className="text-premium-400 text-sm space-y-1">
                  <li>• 5-10 minutes of audio</li>
                  <li>• Clear, noise-free recording</li>
                  <li>• Single speaker only</li>
                  <li>• WAV or MP3 format</li>
                </ul>
              </Card>
              
              <Card className="bg-premium-800/30 p-4 border border-premium-600/20">
                <h4 className="text-white font-medium mb-2">Processing Time</h4>
                <ul className="text-premium-400 text-sm space-y-1">
                  <li>• Analysis: 2-3 minutes</li>
                  <li>• Training: 15-20 minutes</li>
                  <li>• Quality check: 5 minutes</li>
                  <li>• Total: ~25 minutes</li>
                </ul>
              </Card>
              
              <Card className="bg-premium-800/30 p-4 border border-premium-600/20">
                <h4 className="text-white font-medium mb-2">Usage</h4>
                <ul className="text-premium-400 text-sm space-y-1">
                  <li>• Unlimited generations</li>
                  <li>• All languages supported</li>
                  <li>• Commercial license</li>
                  <li>• API access available</li>
                </ul>
              </Card>
            </div>
          </div>
        )}
      </Card>

      {/* Text Input */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <h2 className="text-xl font-semibold text-white mb-4">Input Text</h2>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="Enter the text you want to convert to speech or translate..."
          className="w-full h-32 bg-premium-800/50 border border-premium-600/30 rounded-lg px-4 py-3 text-white placeholder-premium-400 focus:ring-2 focus:ring-ocean-primary focus:border-transparent resize-none"
        />
        <div className="flex items-center justify-between mt-4">
          <p className="text-premium-400 text-sm">
            {inputText.length} characters • Estimated processing: {Math.ceil(inputText.length / 100)} seconds
          </p>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Clear
            </Button>
            <Button variant="outline" size="sm">
              Sample Text
            </Button>
          </div>
        </div>
      </Card>

      {/* Processing */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Process Audio</h2>
          {isProcessing && (
            <Badge className="bg-gradient-emerald text-white border-0 animate-pulse">
              Processing...
            </Badge>
          )}
        </div>
        
        {isProcessing && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-premium-300 text-sm">Processing Progress</span>
              <span className="text-premium-300 text-sm">{Math.round(processingProgress)}%</span>
            </div>
            <Progress value={processingProgress} className="h-2" />
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <div className="text-premium-400 text-sm">
            {inputText.trim() 
              ? `Ready to process ${activeTab === 'voice' ? 'text to speech' : activeTab === 'translate' ? 'translation' : 'voice cloning'}`
              : 'Please enter text to continue'
            }
          </div>
          <Button 
            variant="tech"
            onClick={handleProcess}
            disabled={!inputText.trim() || isProcessing}
            className="px-8"
          >
            {isProcessing ? 'Processing...' : `Start ${activeTab === 'voice' ? 'Speech' : activeTab === 'translate' ? 'Translation' : 'Cloning'}`}
          </Button>
        </div>
      </Card>
    </div>
  )
}
