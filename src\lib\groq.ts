interface GroqMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface GroqChatRequest {
  messages: GroqMessage[]
  model: string
  temperature?: number
  max_tokens?: number
  top_p?: number
  stream?: boolean
}

interface GroqChatResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

interface ScriptEnhancementOptions {
  tone?: 'professional' | 'casual' | 'enthusiastic' | 'educational' | 'sales'
  targetLength?: 'short' | 'medium' | 'long' // 30s, 60s, 2min+
  industry?: string
  callToAction?: string
  platform?: 'youtube' | 'tiktok' | 'linkedin' | 'instagram' | 'general'
}

class GroqClient {
  private apiKey: string
  private baseURL: string

  constructor() {
    this.apiKey = process.env.GROQ_API_KEY!
    this.baseURL = process.env.GROQ_API_URL || 'https://api.groq.com/openai/v1'
  }

  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`Groq API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`)
    }

    return response.json()
  }

  async chat(request: GroqChatRequest): Promise<GroqChatResponse> {
    return this.makeRequest<GroqChatResponse>('/chat/completions', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async enhanceScript(
    originalScript: string, 
    options: ScriptEnhancementOptions = {}
  ): Promise<string> {
    const {
      tone = 'professional',
      targetLength = 'medium',
      industry = 'general',
      callToAction = '',
      platform = 'general'
    } = options

    const lengthGuidelines = {
      short: '20-30 seconds (approximately 50-75 words)',
      medium: '45-60 seconds (approximately 110-150 words)',
      long: '90-120 seconds (approximately 220-300 words)'
    }

    const platformGuidelines = {
      youtube: 'YouTube format with engaging hook, valuable content, and strong CTA',
      tiktok: 'TikTok format with immediate hook, trending language, and viral potential',
      linkedin: 'LinkedIn format with professional tone, industry insights, and networking CTA',
      instagram: 'Instagram format with visual storytelling and hashtag-ready content',
      general: 'versatile format suitable for multiple platforms'
    }

    const systemPrompt = `You are an expert video script writer specializing in creating engaging, conversion-focused scripts for AI avatar videos.

TASK: Enhance and optimize the provided script for maximum engagement and conversion.

GUIDELINES:
- Target Length: ${lengthGuidelines[targetLength]}
- Tone: ${tone}
- Industry: ${industry}
- Platform: ${platformGuidelines[platform]}
- Include Call-to-Action: ${callToAction || 'Create appropriate CTA based on content'}

SCRIPT STRUCTURE:
1. HOOK (first 3-5 seconds): Attention-grabbing opening
2. PROBLEM/QUESTION: Identify pain point or curiosity gap
3. SOLUTION/ANSWER: Provide valuable content
4. PROOF/CREDIBILITY: Add authority or social proof
5. CALL-TO-ACTION: Clear next step for viewer

OPTIMIZATION RULES:
- Use active voice and conversational language
- Include emotional triggers and power words
- Ensure natural flow for AI avatar delivery
- Add strategic pauses with [PAUSE] markers
- Include emphasis markers with [EMPHASIS] for key points
- Keep sentences concise and punchy
- End with compelling, specific call-to-action

Return ONLY the enhanced script without any additional explanation or formatting.`

    const userPrompt = `Original Script: "${originalScript}"

Please enhance this script following all the guidelines above.`

    const response = await this.chat({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.7,
      max_tokens: 1000
    })

    return response.choices[0]?.message?.content || originalScript
  }

  async generateScriptFromTopic(
    topic: string, 
    options: ScriptEnhancementOptions = {}
  ): Promise<string> {
    const {
      tone = 'professional',
      targetLength = 'medium',
      industry = 'general',
      callToAction = '',
      platform = 'general'
    } = options

    const lengthGuidelines = {
      short: '20-30 seconds (approximately 50-75 words)',
      medium: '45-60 seconds (approximately 110-150 words)',
      long: '90-120 seconds (approximately 220-300 words)'
    }

    const systemPrompt = `You are an expert video script writer specializing in creating engaging, conversion-focused scripts for AI avatar videos.

TASK: Create a complete video script from the given topic.

GUIDELINES:
- Target Length: ${lengthGuidelines[targetLength]}
- Tone: ${tone}
- Industry: ${industry}
- Platform: ${platform}
- Include Call-to-Action: ${callToAction || 'Create appropriate CTA based on content'}

SCRIPT STRUCTURE:
1. HOOK (first 3-5 seconds): Attention-grabbing opening
2. PROBLEM/QUESTION: Identify pain point or curiosity gap
3. SOLUTION/ANSWER: Provide valuable content
4. PROOF/CREDIBILITY: Add authority or social proof
5. CALL-TO-ACTION: Clear next step for viewer

OPTIMIZATION RULES:
- Use active voice and conversational language
- Include emotional triggers and power words
- Ensure natural flow for AI avatar delivery
- Add strategic pauses with [PAUSE] markers
- Include emphasis markers with [EMPHASIS] for key points
- Keep sentences concise and punchy
- End with compelling, specific call-to-action

Return ONLY the script without any additional explanation or formatting.`

    const userPrompt = `Topic: "${topic}"

Please create a complete video script following all the guidelines above.`

    const response = await this.chat({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.8,
      max_tokens: 1000
    })

    return response.choices[0]?.message?.content || `Hi there! Let me tell you about ${topic}. This is an amazing opportunity to learn something new. Don't miss out - take action today!`
  }

  async generateHooks(topic: string, count: number = 5): Promise<string[]> {
    const systemPrompt = `You are an expert at creating viral video hooks that capture attention in the first 3-5 seconds.

Create ${count} different attention-grabbing hooks for a video about the given topic. Each hook should:
- Be 1-2 sentences maximum
- Create curiosity or urgency
- Be specific and compelling
- Work for AI avatar delivery
- Target different psychological triggers

Return ONLY the hooks, one per line, without numbering or additional text.`

    const response = await this.chat({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: `Topic: ${topic}` }
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.9,
      max_tokens: 300
    })

    return response.choices[0]?.message?.content?.split('\n').filter(hook => hook.trim()) || []
  }
}

export const groqClient = new GroqClient()

export type {
  GroqMessage,
  GroqChatRequest,
  GroqChatResponse,
  ScriptEnhancementOptions
}