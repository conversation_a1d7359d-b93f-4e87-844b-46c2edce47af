import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { api_key } = body

    if (!api_key) {
      return NextResponse.json({
        success: false,
        error: 'API key is required'
      }, { status: 400 })
    }

    // Test HeyGen API connection by fetching avatars
    const avatarsResponse = await fetch('https://api.heygen.com/v2/avatars', {
      method: 'GET',
      headers: {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json',
      },
    })

    if (!avatarsResponse.ok) {
      const errorData = await avatarsResponse.json().catch(() => ({}))
      return NextResponse.json({
        success: false,
        error: errorData.message || `HeyGen API error: ${avatarsResponse.status}`,
        details: 'Failed to authenticate with HeyGen API'
      }, { status: 400 })
    }

    const avatarsData = await avatarsResponse.json()

    // Test voices endpoint
    const voicesResponse = await fetch('https://api.heygen.com/v2/voices', {
      method: 'GET',
      headers: {
        'X-API-KEY': api_key,
        'Content-Type': 'application/json',
      },
    })

    let voicesData = { data: [] }
    if (voicesResponse.ok) {
      voicesData = await voicesResponse.json()
    }

    // Return success with available resources
    return NextResponse.json({
      success: true,
      message: 'Successfully connected to HeyGen API',
      avatars: avatarsData.data || [],
      voices: voicesData.data || [],
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('HeyGen API test error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to test HeyGen API connection',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'HeyGen API test endpoint',
    usage: 'POST with { api_key: "your_key" } to test connection'
  })
}