const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Simple HTTP server to serve our application
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url);
  let pathname = `.${parsedUrl.pathname}`;
  
  // Default to index.html
  if (pathname === './') {
    pathname = './demo.html';
  }
  
  // Get file extension
  const ext = path.parse(pathname).ext;
  
  // Set content type based on extension
  const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon',
    '.svg': 'image/svg+xml',
  };
  
  const contentType = mimeTypes[ext] || 'application/octet-stream';
  
  // Check if file exists
  fs.exists(pathname, (exist) => {
    if (!exist) {
      // If file not found, return 404
      res.statusCode = 404;
      res.end(`File ${pathname} not found!`);
      return;
    }
    
    // Read file from file system
    fs.readFile(pathname, (err, data) => {
      if (err) {
        res.statusCode = 500;
        res.end(`Error getting the file: ${err}.`);
      } else {
        // Set content type and serve file
        res.setHeader('Content-type', contentType);
        res.end(data);
      }
    });
  });
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🚀 AI Video SaaS Server running at http://localhost:${PORT}`);
  console.log(`📱 Demo available at: http://localhost:${PORT}/demo.html`);
  console.log(`💡 Press Ctrl+C to stop the server`);
});