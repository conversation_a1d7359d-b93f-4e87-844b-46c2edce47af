'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select } from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/loading'

interface ScriptEditorProps {
  title: string
  script: string
  onUpdate: (data: { title?: string; script?: string }) => void
  errors: Record<string, string>
}

interface ScriptTemplate {
  id: string
  name: string
  description: string
  script: string
  category: string
}

interface EnhancementOptions {
  tone: 'professional' | 'casual' | 'enthusiastic' | 'educational' | 'sales'
  targetLength: 'short' | 'medium' | 'long'
  platform: 'youtube' | 'tiktok' | 'linkedin' | 'instagram' | 'general'
}

export function ScriptEditor({ title, script, onUpdate, errors }: ScriptEditorProps) {
  const [isEnhancing, setIsEnhancing] = useState(false)
  const [showEnhancementOptions, setShowEnhancementOptions] = useState(false)
  const [enhancementOptions, setEnhancementOptions] = useState<EnhancementOptions>({
    tone: 'professional',
    targetLength: 'medium',
    platform: 'general'
  })
  const [templates, setTemplates] = useState<ScriptTemplate[]>([])
  const [showTemplates, setShowTemplates] = useState(false)
  const [loadingTemplates, setLoadingTemplates] = useState(false)

  const scriptStats = {
    wordCount: script.split(' ').filter(word => word.length > 0).length,
    charCount: script.length,
    estimatedDuration: Math.ceil(script.split(' ').filter(word => word.length > 0).length / 2.5), // ~2.5 words per second
    sentenceCount: script.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length,
    paragraphCount: script.split('\n\n').filter(para => para.trim().length > 0).length
  }

  // Context length warnings and recommendations
  const getContextRecommendations = () => {
    const { wordCount, estimatedDuration, charCount } = scriptStats
    const recommendations = []
    
    if (wordCount > 1000) {
      recommendations.push({
        type: 'warning',
        message: `Very long script (${wordCount} words). Consider breaking into multiple videos for better engagement.`
      })
    } else if (wordCount > 500) {
      recommendations.push({
        type: 'info',
        message: `Long script detected. Ensure your content remains engaging throughout the ${estimatedDuration}s duration.`
      })
    }
    
    if (charCount > 5000) {
      recommendations.push({
        type: 'warning',
        message: 'Script exceeds recommended character limit. AI processing may take longer.'
      })
    }
    
    if (estimatedDuration > 300) {
      recommendations.push({
        type: 'error',
        message: 'Video duration exceeds 5 minutes. Consider shortening for better viewer retention.'
      })
    } else if (estimatedDuration > 120) {
      recommendations.push({
        type: 'warning',
        message: 'Video duration over 2 minutes. Ensure strong engagement throughout.'
      })
    }
    
    return recommendations
  }

  useEffect(() => {
    // Load templates when component mounts
    const loadTemplates = async () => {
      setLoadingTemplates(true)
      try {
        // Simulate API call - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1000))
        setTemplates([
          {
            id: '1',
            name: 'Product Demo',
            description: 'Showcase your product features effectively',
            script: 'Have you been struggling with [PROBLEM]? Well, I have the perfect solution for you. Our product helps you [SOLUTION] in just [TIME_FRAME]. Let me show you how it works...',
            category: 'business'
          },
          {
            id: '2',
            name: 'Welcome Message',
            description: 'Warm welcome for new users or customers',
            script: 'Hi there! Welcome to [COMPANY_NAME]. I\'m excited to have you here. Today, I want to show you how you can [BENEFIT]. This is going to be amazing! Ready to get started?',
            category: 'business'
          },
          {
            id: '3',
            name: 'Educational Content',
            description: 'Teach your audience something valuable',
            script: 'Did you know that [INTERESTING_FACT]? Today, I\'m going to teach you [TOPIC] in just [TIME_FRAME]. By the end of this video, you\'ll be able to [OUTCOME]. Let\'s dive in!',
            category: 'education'
          }
        ])
      } catch (error) {
        console.error('Failed to load templates:', error)
      } finally {
        setLoadingTemplates(false)
      }
    }

    loadTemplates()
  }, [])

  const handleEnhanceScript = async () => {
    if (!script.trim()) {
      alert('Please enter a script to enhance')
      return
    }

    setIsEnhancing(true)
    try {
      const response = await fetch('/api/groq/enhance-script', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          script,
          options: enhancementOptions
        }),
      })

      const result = await response.json()

      if (result.success) {
        onUpdate({ script: result.data.enhanced_script })
        setShowEnhancementOptions(false)
      } else {
        throw new Error(result.error || 'Failed to enhance script')
      }
    } catch (error) {
      console.error('Enhancement failed:', error)
      alert('Failed to enhance script. Please try again.')
    } finally {
      setIsEnhancing(false)
    }
  }

  const handleTemplateSelect = (template: ScriptTemplate) => {
    onUpdate({ script: template.script })
    setShowTemplates(false)
  }

  const toneOptions = [
    { value: 'professional', label: 'Professional' },
    { value: 'casual', label: 'Casual' },
    { value: 'enthusiastic', label: 'Enthusiastic' },
    { value: 'educational', label: 'Educational' },
    { value: 'sales', label: 'Sales' }
  ]

  const lengthOptions = [
    { value: 'short', label: 'Short (20-30 seconds)' },
    { value: 'medium', label: 'Medium (45-60 seconds)' },
    { value: 'long', label: 'Long (90-120 seconds)' }
  ]

  const platformOptions = [
    { value: 'general', label: 'General Purpose' },
    { value: 'youtube', label: 'YouTube' },
    { value: 'tiktok', label: 'TikTok' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'instagram', label: 'Instagram' }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Script & Content</h3>
        <p className="text-sm text-gray-600 mb-6">
          Write your script or use our AI-powered enhancement to create engaging content
        </p>
      </div>

      {/* Title Input */}
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium text-gray-700">
          Video Title *
        </label>
        <Input
          id="title"
          placeholder="Enter a compelling title for your video"
          value={title}
          onChange={(e) => onUpdate({ title: e.target.value })}
          className={errors.title ? 'border-red-500' : ''}
        />
        {errors.title && (
          <p className="text-sm text-red-600">{errors.title}</p>
        )}
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3">
        <Button
          variant="outline"
          onClick={() => setShowTemplates(!showTemplates)}
          disabled={loadingTemplates}
        >
          {loadingTemplates ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          )}
          Use Template
        </Button>
        <Button
          variant="outline"
          onClick={() => setShowEnhancementOptions(!showEnhancementOptions)}
          disabled={!script.trim()}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          Enhance with AI
        </Button>
      </div>

      {/* Templates Panel */}
      {showTemplates && (
        <Card>
          <CardHeader>
            <CardTitle>Script Templates</CardTitle>
            <CardDescription>
              Choose from our pre-built templates to get started quickly
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{template.name}</h4>
                    <Badge variant="outline">{template.category}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded overflow-hidden">
                    {template.script.substring(0, 100)}...
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Enhancement Options */}
      {showEnhancementOptions && (
        <Card>
          <CardHeader>
            <CardTitle>AI Enhancement Options</CardTitle>
            <CardDescription>
              Customize how you want your script to be enhanced
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Tone
                </label>
                <Select
                  options={toneOptions}
                  value={enhancementOptions.tone}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, tone: e.target.value as any }))}
                />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Target Length
                </label>
                <Select
                  options={lengthOptions}
                  value={enhancementOptions.targetLength}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, targetLength: e.target.value as any }))}
                />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Platform
                </label>
                <Select
                  options={platformOptions}
                  value={enhancementOptions.platform}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, platform: e.target.value as any }))}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowEnhancementOptions(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleEnhanceScript}
                disabled={isEnhancing}
              >
                {isEnhancing ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Enhancing...
                  </>
                ) : (
                  'Enhance Script'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Script Editor */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label htmlFor="script" className="text-sm font-medium text-gray-700">
            Script Content *
          </label>
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span>{scriptStats.wordCount} words</span>
            <span>{scriptStats.charCount} characters</span>
            <span>~{scriptStats.estimatedDuration}s duration</span>
          </div>
        </div>
        <Textarea
          id="script"
          placeholder="Enter your script here... You can use placeholders like [PAUSE] for strategic pauses and [EMPHASIS] to emphasize key points. Supports scripts up to 10,000 characters for extended content."
          value={script}
          onChange={(e) => onUpdate({ script: e.target.value })}
          className={`min-h-[200px] ${errors.script ? 'border-red-500' : ''}`}
          maxLength={10000}
        />
        {errors.script && (
          <p className="text-sm text-red-600">{errors.script}</p>
        )}
        
        {/* Context Length Recommendations */}
        {script.trim() && getContextRecommendations().length > 0 && (
          <div className="space-y-2">
            {getContextRecommendations().map((rec, index) => (
              <div 
                key={index}
                className={`p-3 rounded-md text-sm ${
                  rec.type === 'error' ? 'bg-red-50 text-red-700 border border-red-200' :
                  rec.type === 'warning' ? 'bg-yellow-50 text-yellow-700 border border-yellow-200' :
                  'bg-blue-50 text-blue-700 border border-blue-200'
                }`}
              >
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 mt-0.5">
                    {rec.type === 'error' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                      </svg>
                    )}
                    {rec.type === 'warning' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                      </svg>
                    )}
                    {rec.type === 'info' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                      </svg>
                    )}
                  </div>
                  <p>{rec.message}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-gray-500">
          <p>💡 <strong>Pro tips:</strong></p>
          <ul className="ml-4 mt-1 space-y-1">
            <li>• Use [PAUSE] to add strategic pauses for better delivery</li>
            <li>• Use [EMPHASIS] around important words or phrases</li>
            <li>• Keep sentences conversational and not too long</li>
            <li>• Include a clear call-to-action at the end</li>
            <li>• Scripts support up to 10,000 characters for extended content</li>
          </ul>
        </div>
      </div>

      {/* Script Stats */}
      {script.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Script Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{scriptStats.wordCount}</div>
                <div className="text-sm text-gray-600">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{scriptStats.estimatedDuration}s</div>
                <div className="text-sm text-gray-600">Est. Duration</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{scriptStats.charCount}</div>
                <div className="text-sm text-gray-600">Characters</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {script.split('[PAUSE]').length - 1}
                </div>
                <div className="text-sm text-gray-600">Pauses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-600">{scriptStats.sentenceCount}</div>
                <div className="text-sm text-gray-600">Sentences</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">{scriptStats.paragraphCount}</div>
                <div className="text-sm text-gray-600">Paragraphs</div>
              </div>
            </div>
            
            {/* Context Length Progress Bar */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Script Length</span>
                <span className="text-sm text-gray-500">{scriptStats.charCount}/10,000 characters</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    scriptStats.charCount > 8000 ? 'bg-red-500' :
                    scriptStats.charCount > 5000 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.min((scriptStats.charCount / 10000) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}