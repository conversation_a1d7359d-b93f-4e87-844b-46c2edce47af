'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'

export default function ShowcasePage() {
  return (
    <div className="space-y-12">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Design System Showcase</h1>
        <p className="text-white/70">Comprehensive overview of all design components and interactions</p>
      </div>

      {/* Color Palette */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Color Palette</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-premium p-6 border border-white/10">
            <h3 className="text-white font-medium">Premium Gradient</h3>
            <p className="text-white/70 text-sm">Primary brand colors</p>
          </Card>
          <Card className="bg-task-card p-6 border border-white/10">
            <h3 className="text-white font-medium">Task Card</h3>
            <p className="text-white/70 text-sm">Glass morphism</p>
          </Card>
          <Card className="bg-project-card p-6 border border-white/10">
            <h3 className="text-white font-medium">Project Card</h3>
            <p className="text-white/70 text-sm">Enhanced glass</p>
          </Card>
          <Card className="bg-sidebar p-6 border border-white/10">
            <h3 className="text-white font-medium">Sidebar</h3>
            <p className="text-white/70 text-sm">Navigation background</p>
          </Card>
        </div>
      </section>

      {/* Buttons */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Button Variants</h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="premium">Premium Button</Button>
          <Button variant="glass">Glass Button</Button>
          <Button variant="task">Task Button</Button>
          <Button variant="outline">Outline Button</Button>
          <Button variant="ghost">Ghost Button</Button>
          <Button variant="default">Default Button</Button>
        </div>
        <div className="flex flex-wrap gap-4">
          <Button variant="premium" size="sm">Small</Button>
          <Button variant="premium" size="default">Default</Button>
          <Button variant="premium" size="lg">Large</Button>
        </div>
      </section>

      {/* Badges */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Badge Variants</h2>
        <div className="flex flex-wrap gap-4">
          <Badge variant="premium">Premium</Badge>
          <Badge variant="glass">Glass</Badge>
          <Badge variant="success">Success</Badge>
          <Badge variant="warning">Warning</Badge>
          <Badge variant="info">Info</Badge>
          <Badge variant="outline">Outline</Badge>
          <Badge variant="default">Default</Badge>
        </div>
      </section>

      {/* Cards */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Card Styles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-task-card p-6 border border-white/10 card-float">
            <h3 className="text-lg font-semibold text-white mb-2">Task Card</h3>
            <p className="text-white/70 mb-4">Glass morphism with hover effects</p>
            <Badge variant="premium">Interactive</Badge>
          </Card>
          <Card className="bg-project-card p-6 border border-white/10 card-float">
            <h3 className="text-lg font-semibold text-white mb-2">Project Card</h3>
            <p className="text-white/70 mb-4">Enhanced glass with animations</p>
            <Badge variant="glass">Animated</Badge>
          </Card>
          <Card className="bg-gradient-premium p-6 border-0 card-float">
            <h3 className="text-lg font-semibold text-white mb-2">Premium Card</h3>
            <p className="text-white/90 mb-4">Gradient background with glow</p>
            <Badge className="bg-white/20 text-white border-0">Premium</Badge>
          </Card>
        </div>
      </section>

      {/* Inputs */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Input Components</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Input placeholder="Search tasks..." />
            <Input placeholder="Project name..." />
            <Input type="email" placeholder="<EMAIL>" />
          </div>
          <div className="space-y-4">
            <div className="relative">
              <Input placeholder="Search with icon..." className="pl-10" />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <Input placeholder="Disabled input" disabled />
            <Input placeholder="Focus me for effects" className="focus-premium" />
          </div>
        </div>
      </section>

      {/* Progress Bars */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Progress Indicators</h2>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-white/70">Project Progress</span>
              <span className="text-white/70">75%</span>
            </div>
            <Progress value={75} className="h-3" />
          </div>
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-white/70">Task Completion</span>
              <span className="text-white/70">45%</span>
            </div>
            <Progress value={45} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-white/70">Team Performance</span>
              <span className="text-white/70">90%</span>
            </div>
            <Progress value={90} className="h-4" />
          </div>
        </div>
      </section>

      {/* Animations */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Animation Examples</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-task-card p-6 border border-white/10 animate-fade-in">
            <h3 className="text-white font-medium">Fade In</h3>
            <p className="text-white/70 text-sm">Smooth entrance animation</p>
          </Card>
          <Card className="bg-task-card p-6 border border-white/10 animate-slide-up">
            <h3 className="text-white font-medium">Slide Up</h3>
            <p className="text-white/70 text-sm">Upward slide transition</p>
          </Card>
          <Card className="bg-task-card p-6 border border-white/10 animate-float">
            <h3 className="text-white font-medium">Float</h3>
            <p className="text-white/70 text-sm">Continuous floating motion</p>
          </Card>
        </div>
      </section>

      {/* Interactive Elements */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Interactive Elements</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button className="interactive-scale bg-gradient-premium text-white border-0">
            Scale Effect
          </Button>
          <Button className="btn-hover-lift bg-task-bg text-white border border-white/20">
            Lift Effect
          </Button>
          <Button className="pulse-on-hover bg-project-bg text-white border border-white/20">
            Pulse on Hover
          </Button>
          <Button className="glow-on-hover bg-white/10 text-white border border-white/20">
            Glow Effect
          </Button>
        </div>
      </section>

      {/* Team Members */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Team Member Avatars</h2>
        <div className="flex items-center space-x-4">
          <div className="flex -space-x-2">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full border-2 border-white flex items-center justify-center">
              <span className="text-sm text-white font-medium">JD</span>
            </div>
            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full border-2 border-white flex items-center justify-center">
              <span className="text-sm text-white font-medium">SC</span>
            </div>
            <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-400 rounded-full border-2 border-white flex items-center justify-center">
              <span className="text-sm text-white font-medium">MT</span>
            </div>
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full border-2 border-white flex items-center justify-center">
              <span className="text-sm text-white font-medium">LW</span>
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full border-2 border-white flex items-center justify-center">
              <span className="text-sm text-white font-medium">+5</span>
            </div>
          </div>
          <span className="text-white/70">Team collaboration indicators</span>
        </div>
      </section>

      {/* Status Indicators */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Status Indicators</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            <span className="text-white/70">Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
            <span className="text-white/70">In Progress</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
            <span className="text-white/70">Pending</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-400 rounded-full"></div>
            <span className="text-white/70">Overdue</span>
          </div>
        </div>
      </section>

      {/* Typography */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-white">Typography Scale</h2>
        <div className="space-y-4">
          <h1 className="text-4xl font-bold text-white">Heading 1 - Main Titles</h1>
          <h2 className="text-3xl font-semibold text-white">Heading 2 - Section Titles</h2>
          <h3 className="text-2xl font-semibold text-white">Heading 3 - Subsections</h3>
          <h4 className="text-xl font-medium text-white">Heading 4 - Card Titles</h4>
          <p className="text-base text-white/80">Body text - Regular content and descriptions</p>
          <p className="text-sm text-white/70">Small text - Secondary information</p>
          <p className="text-xs text-white/60">Caption text - Metadata and labels</p>
        </div>
      </section>
    </div>
  )
}
