'use client'

import { useState, useCallback } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { LoadingSpinner } from '@/components/ui/loading'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { MicroExpressionEngine } from './MicroExpressionEngine'
import { VoiceModulationEngine } from './VoiceModulationEngine'
import { GestureIntelligenceEngine } from './GestureIntelligenceEngine'
import { ConversationalFlowAnalyzer } from './ConversationalFlowAnalyzer'
import { NaturalPauseAI } from './NaturalPauseAI'
import { EmotionMappingEngine } from './EmotionMappingEngine'

import { 
  MicroExpression, 
  VoiceModulation, 
  GestureIntelligence, 
  ConversationalFlow, 
  EmotionMapping, 
  HumanizationResult,
  HumanizationConfig 
} from '@/types/humanization'

interface HumanizationDashboardProps {
  script: string
  duration: number
  voiceId: string
  avatarPosition: 'side' | 'corner' | 'overlay' | 'fullscreen'
  onHumanizationComplete: (result: HumanizationResult) => void
  onScriptImproved: (improvedScript: string) => void
}

interface HumanizationState {
  microExpressions: MicroExpression[]
  voiceModulations: VoiceModulation[]
  gestures: GestureIntelligence[]
  conversationalFlow: ConversationalFlow | null
  emotionMappings: EmotionMapping[]
  optimizedScript: string
  humanizationConfig: HumanizationConfig
}

export function HumanizationDashboard({
  script,
  duration,
  voiceId,
  avatarPosition,
  onHumanizationComplete,
  onScriptImproved
}: HumanizationDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isGeneratingComplete, setIsGeneratingComplete] = useState(false)
  const [humanizationState, setHumanizationState] = useState<HumanizationState>({
    microExpressions: [],
    voiceModulations: [],
    gestures: [],
    conversationalFlow: null,
    emotionMappings: [],
    optimizedScript: script,
    humanizationConfig: {
      naturalness_level: 'moderate',
      preserve_brand_voice: true,
      platform_specific_optimization: true,
      target_audience_adaptation: true,
      micro_expression_frequency: 8,
      voice_variation_intensity: 0.7,
      gesture_frequency: 6,
      imperfection_rate: 0.1,
      content_type: 'educational',
      formality_level: 'semi_formal',
      energy_level: 'moderate'
    }
  })

  // Calculate overall humanization quality score
  const calculateQualityScore = useCallback((): number => {
    const { microExpressions, voiceModulations, gestures, conversationalFlow, emotionMappings } = humanizationState
    
    let score = 0.5 // Base score
    let factors = 1 // Number of factors
    
    // Micro-expressions quality
    if (microExpressions.length > 0) {
      const expressionScore = Math.min(1, (microExpressions.length / (duration / 60 * 8)) * 0.8)
      const avgIntensity = microExpressions.reduce((sum, expr) => sum + expr.intensity, 0) / microExpressions.length
      score += (expressionScore + avgIntensity * 0.3) * 0.2
      factors++
    }
    
    // Voice modulation quality
    if (voiceModulations.length > 0) {
      const voiceScore = Math.min(1, voiceModulations.length / 10) * 0.8
      const emotionalVariation = voiceModulations.filter(v => v.modulationType === 'emotion').length / voiceModulations.length
      score += (voiceScore + emotionalVariation * 0.4) * 0.25
      factors++
    }
    
    // Gesture intelligence
    if (gestures.length > 0) {
      const gestureScore = Math.min(1, (gestures.length / (duration / 60 * 6)) * 0.9)
      const contextualGestures = gestures.filter(g => g.coordination.relates_to_content).length / gestures.length
      score += (gestureScore + contextualGestures * 0.3) * 0.2
      factors++
    }
    
    // Conversational flow
    if (conversationalFlow) {
      const flowScore = conversationalFlow.natural_flow_score
      const issuesPenalty = Math.max(0, 1 - (conversationalFlow.robotic_patterns.length * 0.1))
      score += (flowScore * issuesPenalty) * 0.25
      factors++
    }
    
    // Emotion mapping
    if (emotionMappings.length > 0) {
      const emotionScore = Math.min(1, emotionMappings.length / 5) * 0.9
      const avgIntensity = emotionMappings.reduce((sum, em) => sum + em.emotion_intensity, 0) / emotionMappings.length
      score += (emotionScore + avgIntensity * 0.2) * 0.1
      factors++
    }
    
    return Math.min(1, score / factors * 1.8) // Normalize and boost slightly
  }, [humanizationState, duration])

  // Generate complete humanization
  const generateCompleteHumanization = useCallback(async () => {
    setIsGeneratingComplete(true)
    
    try {
      // Wait for all components to process (they should already be generated individually)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const qualityScore = calculateQualityScore()
      
      const humanizationResult: HumanizationResult = {
        config_used: humanizationState.humanizationConfig,
        processing_time: 2.5,
        improvements_applied: {
          micro_expressions: humanizationState.microExpressions,
          voice_modulations: humanizationState.voiceModulations,
          gesture_intelligence: humanizationState.gestures,
          visual_intelligence: {
            scene_transitions: [],
            visual_elements: [],
            camera_movements: []
          },
          editing_patterns: {
            cut_timing: { cuts: [], pacing_analysis: { average_shot_length: 3, pacing_variation: 0.7, content_density_score: 0.6 } },
            natural_imperfections: [],
            audio_enhancements: { ambient_sounds: true, natural_room_tone: true, microphone_variations: true, breathing_sounds: true }
          },
          platform_optimization: {
            platform: 'youtube',
            creator_patterns: { typical_pacing: 2.5, common_expressions: [], editing_style: 'smooth_transitions', engagement_techniques: [] },
            platform_specific_adjustments: { aspect_ratio_optimization: true, length_optimization: true, hook_placement: 3, call_to_action_timing: 10, trending_elements: [] }
          }
        },
        quality_assessment: {
          authenticity_score: qualityScore,
          detection_risk_factors: [],
          improvement_areas: [],
          overall_assessment: {
            passes_human_like_threshold: qualityScore > 0.8,
            confidence_level: qualityScore,
            ready_for_publication: qualityScore > 0.75,
            additional_processing_needed: qualityScore < 0.75 ? ['Increase expression frequency', 'Improve voice variation'] : []
          }
        },
        before_after_comparison: {
          authenticity_improvement: (qualityScore - 0.4) * 100,
          engagement_prediction: qualityScore * 0.9,
          detection_risk_reduction: qualityScore * 85
        }
      }
      
      onHumanizationComplete(humanizationResult)
      
    } catch (error) {
      console.error('Failed to generate complete humanization:', error)
      alert('Failed to generate complete humanization. Please try again.')
    } finally {
      setIsGeneratingComplete(false)
    }
  }, [humanizationState, calculateQualityScore, onHumanizationComplete])

  // Update handlers for each component
  const handleMicroExpressionsGenerated = (expressions: MicroExpression[], voiceMods: VoiceModulation[], gestureIntel: GestureIntelligence[]) => {
    setHumanizationState(prev => ({
      ...prev,
      microExpressions: expressions,
      voiceModulations: [...prev.voiceModulations, ...voiceMods],
      gestures: [...prev.gestures, ...gestureIntel]
    }))
  }

  const handleVoiceModulationsGenerated = (modulations: VoiceModulation[], emotionMaps: EmotionMapping[]) => {
    setHumanizationState(prev => ({
      ...prev,
      voiceModulations: [...prev.voiceModulations.filter(v => v.modulationType !== 'emotion'), ...modulations],
      emotionMappings: emotionMaps
    }))
  }

  const handleGesturesGenerated = (gestures: GestureIntelligence[]) => {
    setHumanizationState(prev => ({
      ...prev,
      gestures: [...prev.gestures.filter(g => !g.id.includes('gesture_')), ...gestures]
    }))
  }

  const handleFlowAnalysisComplete = (analysis: ConversationalFlow) => {
    setHumanizationState(prev => ({
      ...prev,
      conversationalFlow: analysis
    }))
  }

  const handleScriptOptimized = (optimizedScript: string, pauseAnalysis: any) => {
    setHumanizationState(prev => ({
      ...prev,
      optimizedScript
    }))
    onScriptImproved(optimizedScript)
  }

  const handleEmotionMappingComplete = (emotionMaps: EmotionMapping[]) => {
    setHumanizationState(prev => ({
      ...prev,
      emotionMappings: emotionMaps
    }))
  }

  const qualityScore = calculateQualityScore()
  const completionPercentage = Math.round(
    ((humanizationState.microExpressions.length > 0 ? 20 : 0) +
     (humanizationState.voiceModulations.length > 0 ? 20 : 0) +
     (humanizationState.gestures.length > 0 ? 15 : 0) +
     (humanizationState.conversationalFlow ? 20 : 0) +
     (humanizationState.emotionMappings.length > 0 ? 15 : 0) +
     (humanizationState.optimizedScript !== script ? 10 : 0))
  )

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-2">Advanced AI Humanization Dashboard</h2>
        <p className="text-sm text-gray-600">
          Transform your AI avatar into an indistinguishable human presenter with advanced humanization technology
        </p>
      </div>

      {/* Overview Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Humanization Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-5 mb-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                qualityScore >= 0.9 ? 'text-green-600' :
                qualityScore >= 0.7 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {Math.round(qualityScore * 100)}%
              </div>
              <div className="text-sm text-gray-600">Human-Like Quality</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{completionPercentage}%</div>
              <div className="text-sm text-gray-600">Processing Complete</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{humanizationState.microExpressions.length}</div>
              <div className="text-sm text-gray-600">Micro-Expressions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{humanizationState.voiceModulations.length}</div>
              <div className="text-sm text-gray-600">Voice Modulations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{humanizationState.gestures.length}</div>
              <div className="text-sm text-gray-600">Intelligent Gestures</div>
            </div>
          </div>

          {/* Quality Assessment */}
          {qualityScore > 0.5 && (
            <Alert className={`${
              qualityScore >= 0.9 ? 'border-green-200 bg-green-50' :
              qualityScore >= 0.7 ? 'border-yellow-200 bg-yellow-50' :
              'border-orange-200 bg-orange-50'
            }`}>
              <AlertDescription className={`${
                qualityScore >= 0.9 ? 'text-green-800' :
                qualityScore >= 0.7 ? 'text-yellow-800' :
                'text-orange-800'
              }`}>
                {qualityScore >= 0.9 && '🎉 Excellent! Your AI avatar will be virtually indistinguishable from human content.'}
                {qualityScore >= 0.7 && qualityScore < 0.9 && '✅ Very good humanization quality. Minor improvements could enhance realism further.'}
                {qualityScore < 0.7 && '⚠️ Good start! Consider adding more humanization elements for maximum authenticity.'}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Humanization Components */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="expressions">Expressions</TabsTrigger>
          <TabsTrigger value="voice">Voice</TabsTrigger>
          <TabsTrigger value="gestures">Gestures</TabsTrigger>
          <TabsTrigger value="flow">Flow</TabsTrigger>
          <TabsTrigger value="emotions">Emotions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Processing Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Micro-Expressions</span>
                    <Badge variant={humanizationState.microExpressions.length > 0 ? 'default' : 'outline'}>
                      {humanizationState.microExpressions.length > 0 ? 'Generated' : 'Pending'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Voice Modulation</span>
                    <Badge variant={humanizationState.voiceModulations.length > 0 ? 'default' : 'outline'}>
                      {humanizationState.voiceModulations.length > 0 ? 'Generated' : 'Pending'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Gesture Intelligence</span>
                    <Badge variant={humanizationState.gestures.length > 0 ? 'default' : 'outline'}>
                      {humanizationState.gestures.length > 0 ? 'Generated' : 'Pending'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Conversational Flow</span>
                    <Badge variant={humanizationState.conversationalFlow ? 'default' : 'outline'}>
                      {humanizationState.conversationalFlow ? 'Analyzed' : 'Pending'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Emotion Mapping</span>
                    <Badge variant={humanizationState.emotionMappings.length > 0 ? 'default' : 'outline'}>
                      {humanizationState.emotionMappings.length > 0 ? 'Generated' : 'Pending'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">AI Detection Risk</span>
                    <Badge variant={qualityScore > 0.8 ? 'default' : 'outline'} className="bg-green-100 text-green-800">
                      {Math.max(5, 100 - qualityScore * 85).toFixed(0)}% risk
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Natural Movement</span>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      {Math.round(qualityScore * 98)}% natural
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Engagement Potential</span>
                    <Badge variant="outline" className="bg-purple-100 text-purple-800">
                      {Math.round(qualityScore * 92)}% engaging
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Voice Authenticity</span>
                    <Badge variant="outline" className="bg-orange-100 text-orange-800">
                      {Math.round(qualityScore * 89)}% authentic
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Generate All Button */}
          <div className="text-center">
            <Button
              onClick={generateCompleteHumanization}
              disabled={isGeneratingComplete || completionPercentage < 80}
              size="lg"
              className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 hover:from-indigo-700 hover:via-purple-700 hover:to-pink-700"
            >
              {isGeneratingComplete ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Finalizing Humanization...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Generate Complete Human-Like Avatar
                </>
              )}
            </Button>
            {completionPercentage < 80 && (
              <p className="text-sm text-gray-500 mt-2">
                Complete at least 80% of humanization components to generate final result
              </p>
            )}
          </div>
        </TabsContent>

        <TabsContent value="expressions">
          <MicroExpressionEngine
            script={humanizationState.optimizedScript}
            duration={duration}
            onExpressionsGenerated={handleMicroExpressionsGenerated}
            existingExpressions={humanizationState.microExpressions}
          />
        </TabsContent>

        <TabsContent value="voice">
          <VoiceModulationEngine
            script={humanizationState.optimizedScript}
            duration={duration}
            voiceId={voiceId}
            onModulationsGenerated={handleVoiceModulationsGenerated}
            existingModulations={humanizationState.voiceModulations}
          />
        </TabsContent>

        <TabsContent value="gestures">
          <GestureIntelligenceEngine
            script={humanizationState.optimizedScript}
            duration={duration}
            avatarPosition={avatarPosition}
            onGesturesGenerated={handleGesturesGenerated}
            existingGestures={humanizationState.gestures}
          />
        </TabsContent>

        <TabsContent value="flow">
          <div className="space-y-6">
            <ConversationalFlowAnalyzer
              script={humanizationState.optimizedScript}
              onAnalysisComplete={handleFlowAnalysisComplete}
              onScriptImproved={(improvedScript) => {
                setHumanizationState(prev => ({ ...prev, optimizedScript: improvedScript }))
                onScriptImproved(improvedScript)
              }}
            />
            <NaturalPauseAI
              script={humanizationState.optimizedScript}
              duration={duration}
              onPausesOptimized={handleScriptOptimized}
            />
          </div>
        </TabsContent>

        <TabsContent value="emotions">
          <EmotionMappingEngine
            script={humanizationState.optimizedScript}
            duration={duration}
            onEmotionMappingComplete={handleEmotionMappingComplete}
            existingEmotionMaps={humanizationState.emotionMappings}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}