import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-hero flex flex-col items-center justify-center px-6 py-24">
      <div className="max-w-md mx-auto text-center">
        {/* Logo */}
        <Link href="/" className="inline-block mb-8 hover:opacity-80 transition-opacity">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <div className="w-5 h-5 bg-gradient-primary rounded-sm"></div>
            </div>
            <span className="text-2xl font-bold text-white">HeyGen Video</span>
          </div>
        </Link>

        {/* 404 Content */}
        <div className="glass-card p-8 rounded-2xl shadow-floating border border-white/20">
          <div className="text-center">
            <h1 className="text-6xl font-bold text-gradient mb-4">404</h1>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
            <p className="text-gray-600 mb-6">
              Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/">
                <Button className="btn-gradient text-white">
                  Go Home
                </Button>
              </Link>
              <Link href="/dashboard">
                <Button variant="outline">
                  Go to Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Help Links */}
        <div className="mt-8 text-center">
          <p className="text-white/70 text-sm mb-4">
            Need help finding what you're looking for?
          </p>
          <div className="flex justify-center space-x-6 text-sm">
            <Link href="/contact" className="text-white/80 hover:text-white transition-colors">
              Contact Support
            </Link>
            <Link href="/help" className="text-white/80 hover:text-white transition-colors">
              Help Center
            </Link>
            <Link href="/videos" className="text-white/80 hover:text-white transition-colors">
              Video Library
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}