'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON>lider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { LoadingSpinner } from '@/components/ui/loading'

interface NaturalPauseAIProps {
  script: string
  duration: number
  onPausesOptimized: (optimizedScript: string, pauseAnalysis: PauseAnalysis) => void
}

interface PauseAnalysis {
  totalPauses: number
  naturalPauses: number
  unnaturalPauses: number
  pauseDistribution: {
    after_sentences: number
    after_clauses: number
    for_emphasis: number
    for_breathing: number
    transitional: number
  }
  pauseQualityScore: number
  improvements: string[]
}

interface PauseSettings {
  pauseFrequency: number // pauses per minute
  emphasizePauses: boolean
  breathingPauses: boolean
  transitionPauses: boolean
  removeUnnaturalPauses: boolean
  contentBasedPauses: boolean
  pauseIntensity: 'minimal' | 'moderate' | 'expressive'
}

// Pause placement rules based on natural speech patterns
const PAUSE_RULES = {
  sentence_endings: {
    pattern: /[.!?]\s+/g,
    pauseType: 'natural',
    priority: 0.9,
    duration: 'medium',
    description: 'Natural pause after complete thoughts'
  },
  clause_boundaries: {
    pattern: /,\s+(?=and|but|or|so|because|although|however|while|when|if|unless)/g,
    pauseType: 'clause',
    priority: 0.6,
    duration: 'short',
    description: 'Brief pause at clause boundaries'
  },
  emphasis_words: {
    pattern: /\b(important|key|crucial|essential|remember|note|pay attention)\b/gi,
    pauseType: 'emphasis',
    priority: 0.8,
    duration: 'medium',
    description: 'Pause before or after emphasis words'
  },
  list_items: {
    pattern: /\b(first|second|third|next|then|finally|lastly),?\s+/gi,
    pauseType: 'enumeration',
    priority: 0.7,
    duration: 'short',
    description: 'Pause after enumeration markers'
  },
  questions: {
    pattern: /\?\s*/g,
    pauseType: 'rhetorical',
    priority: 0.85,
    duration: 'long',
    description: 'Pause after questions for effect'
  },
  breathing_points: {
    pattern: /[.!?]\s+[A-Z][^.!?]{30,}/g,
    pauseType: 'breathing',
    priority: 0.5,
    duration: 'medium',
    description: 'Natural breathing pause in long sentences'
  },
  transition_words: {
    pattern: /\b(however|therefore|meanwhile|furthermore|moreover|additionally|consequently|nevertheless)\b/gi,
    pauseType: 'transition',
    priority: 0.6,
    duration: 'short',
    description: 'Pause around transition words'
  }
}

// Unnatural pause patterns to remove
const UNNATURAL_PAUSE_PATTERNS = {
  consecutive_pauses: /\[PAUSE\]\s*\[PAUSE\]/g,
  pause_after_articles: /\b(the|a|an)\s*\[PAUSE\]/gi,
  pause_before_prepositions: /\[PAUSE\]\s*(in|on|at|by|for|with|to|from)/gi,
  pause_mid_phrase: /\b(\w+)\s*\[PAUSE\]\s*(and|or|but)\s*(\w+)\b/g,
  pause_after_comma: /,\s*\[PAUSE\]/g,
  excessive_pauses: /(\[PAUSE\].*?\[PAUSE\].*?\[PAUSE\].*?\[PAUSE\])/g // More than 3 pauses in close proximity
}

// Natural pause variations
const PAUSE_VARIATIONS = {
  minimal: { short: 0.3, medium: 0.5, long: 0.8 },
  moderate: { short: 0.5, medium: 0.8, long: 1.2 },
  expressive: { short: 0.8, medium: 1.2, long: 1.8 }
}

export function NaturalPauseAI({
  script,
  duration,
  onPausesOptimized
}: NaturalPauseAIProps) {
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [settings, setSettings] = useState<PauseSettings>({
    pauseFrequency: 8, // pauses per minute
    emphasizePauses: true,
    breathingPauses: true,
    transitionPauses: true,
    removeUnnaturalPauses: true,
    contentBasedPauses: true,
    pauseIntensity: 'moderate'
  })
  
  const [currentAnalysis, setCurrentAnalysis] = useState<PauseAnalysis | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  // Analyze current pause placement
  const analyzePauses = useCallback((text: string): PauseAnalysis => {
    const pauseMatches = Array.from(text.matchAll(/\[PAUSE\]/g))
    const totalPauses = pauseMatches.length
    
    // Analyze pause distribution
    const distribution = {
      after_sentences: 0,
      after_clauses: 0,
      for_emphasis: 0,
      for_breathing: 0,
      transitional: 0
    }
    
    let naturalPauses = 0
    let unnaturalPauses = 0
    const improvements: string[] = []
    
    // Check for natural pause placement
    pauseMatches.forEach(match => {
      const pauseIndex = match.index || 0
      const beforePause = text.substring(Math.max(0, pauseIndex - 20), pauseIndex)
      const afterPause = text.substring(pauseIndex + 7, Math.min(text.length, pauseIndex + 27))
      
      // Classify pause type
      if (/[.!?]\s*$/.test(beforePause)) {
        distribution.after_sentences++
        naturalPauses++
      } else if (/,\s*$/.test(beforePause)) {
        distribution.after_clauses++
        if (/\s+(and|but|or|so)\s+/.test(afterPause)) {
          naturalPauses++
        } else {
          unnaturalPauses++
        }
      } else if (/\b(important|key|crucial|remember)\b/i.test(beforePause + afterPause)) {
        distribution.for_emphasis++
        naturalPauses++
      } else if (/\b(however|therefore|meanwhile)\b/i.test(beforePause + afterPause)) {
        distribution.transitional++
        naturalPauses++
      } else {
        // Check if it's a breathing pause (after long text)
        const precedingText = text.substring(Math.max(0, pauseIndex - 100), pauseIndex)
        if (precedingText.length > 80 && /[.!?]/.test(precedingText)) {
          distribution.for_breathing++
          naturalPauses++
        } else {
          unnaturalPauses++
        }
      }
    })
    
    // Check for unnatural patterns
    Object.entries(UNNATURAL_PAUSE_PATTERNS).forEach(([patternName, pattern]) => {
      const matches = Array.from(text.matchAll(pattern))
      if (matches.length > 0) {
        improvements.push(`Remove ${matches.length} ${patternName.replace('_', ' ')} instances`)
      }
    })
    
    // Calculate quality score
    const pauseQualityScore = totalPauses > 0 ? 
      (naturalPauses / totalPauses) * 0.7 + 
      (Math.min(distribution.for_emphasis, 5) / 5) * 0.2 +
      (improvements.length === 0 ? 0.1 : 0)
      : 0.5
    
    // Generate improvement suggestions
    if (totalPauses === 0) {
      improvements.push('Add natural pauses for better speech flow')
    } else if (totalPauses / (duration / 60) > 15) {
      improvements.push('Reduce excessive pause frequency')
    } else if (totalPauses / (duration / 60) < 5) {
      improvements.push('Add more pauses for natural breathing')
    }
    
    if (distribution.for_emphasis === 0 && text.length > 200) {
      improvements.push('Add emphasis pauses for important points')
    }
    
    if (unnaturalPauses > naturalPauses) {
      improvements.push('Improve pause placement for better natural flow')
    }

    return {
      totalPauses,
      naturalPauses,
      unnaturalPauses,
      pauseDistribution: distribution,
      pauseQualityScore: Math.max(0, Math.min(1, pauseQualityScore)),
      improvements
    }
  }, [duration])

  // Optimize pause placement
  const optimizePauses = useCallback(async () => {
    setIsOptimizing(true)
    
    try {
      let optimizedScript = script
      
      // Step 1: Remove unnatural pauses if enabled
      if (settings.removeUnnaturalPauses) {
        Object.values(UNNATURAL_PAUSE_PATTERNS).forEach(pattern => {
          optimizedScript = optimizedScript.replace(pattern, (match) => {
            // Smart replacement based on pattern
            if (match.includes('[PAUSE][PAUSE]')) {
              return '[PAUSE]'
            } else if (match.includes(',[PAUSE]')) {
              return ', '
            } else if (match.includes('[PAUSE] and') || match.includes('[PAUSE] or')) {
              return ' and'
            } else {
              return match.replace(/\[PAUSE\]/g, '')
            }
          })
        })
      }
      
      // Step 2: Add natural pauses based on content
      if (settings.contentBasedPauses) {
        Object.entries(PAUSE_RULES).forEach(([ruleName, rule]) => {
          if (!shouldApplyRule(ruleName)) return
          
          const matches = Array.from(optimizedScript.matchAll(rule.pattern))
          
          matches.reverse().forEach(match => { // Reverse to maintain indices
            const matchIndex = match.index || 0
            const matchLength = match[0].length
            
            // Check if pause already exists nearby
            const nearbyText = optimizedScript.substring(
              Math.max(0, matchIndex - 10),
              Math.min(optimizedScript.length, matchIndex + matchLength + 10)
            )
            
            if (nearbyText.includes('[PAUSE]')) return // Skip if pause already exists
            
            // Determine pause placement
            let pausePosition = matchIndex + matchLength
            let pauseMarker = '[PAUSE]'
            
            // Adjust based on rule type
            if (rule.pauseType === 'emphasis') {
              pausePosition = matchIndex // Before emphasis word
              pauseMarker = '[PAUSE] '
            } else if (rule.pauseType === 'transition') {
              pausePosition = matchIndex + matchLength
              pauseMarker = ' [PAUSE] '
            }
            
            // Insert pause
            optimizedScript = 
              optimizedScript.substring(0, pausePosition) +
              pauseMarker +
              optimizedScript.substring(pausePosition)
          })
        })
      }
      
      // Step 3: Add breathing pauses for long segments
      if (settings.breathingPauses) {
        optimizedScript = addBreathingPauses(optimizedScript)
      }
      
      // Step 4: Optimize pause frequency
      optimizedScript = optimizePauseFrequency(optimizedScript)
      
      // Step 5: Final analysis
      const finalAnalysis = analyzePauses(optimizedScript)
      setCurrentAnalysis(finalAnalysis)
      
      onPausesOptimized(optimizedScript, finalAnalysis)
      
    } catch (error) {
      console.error('Failed to optimize pauses:', error)
      alert('Failed to optimize pauses. Please try again.')
    } finally {
      setIsOptimizing(false)
    }
  }, [script, settings, analyzePauses, onPausesOptimized])

  // Helper function to determine if a rule should be applied
  const shouldApplyRule = (ruleName: string): boolean => {
    switch (ruleName) {
      case 'emphasis_words': return settings.emphasizePauses
      case 'breathing_points': return settings.breathingPauses
      case 'transition_words': return settings.transitionPauses
      default: return true
    }
  }

  // Add breathing pauses for long text segments
  const addBreathingPauses = (text: string): string => {
    const sentences = text.split(/([.!?])/g)
    let result = ''
    let wordCount = 0
    
    for (let i = 0; i < sentences.length; i += 2) {
      const sentence = sentences[i] || ''
      const punctuation = sentences[i + 1] || ''
      
      const words = sentence.trim().split(/\s+/).length
      wordCount += words
      
      result += sentence + punctuation
      
      // Add breathing pause after approximately 20-30 words
      if (wordCount > 25 && punctuation && i < sentences.length - 2) {
        if (!result.endsWith('[PAUSE]') && !sentences[i + 2]?.trim().startsWith('[PAUSE]')) {
          result += ' [PAUSE]'
          wordCount = 0
        }
      }
    }
    
    return result
  }

  // Optimize overall pause frequency
  const optimizePauseFrequency = (text: string): string => {
    const currentPauses = (text.match(/\[PAUSE\]/g) || []).length
    const targetPauses = Math.floor((duration / 60) * settings.pauseFrequency)
    
    if (currentPauses > targetPauses * 1.2) {
      // Remove some pauses, starting with least important
      return removeLeastImportantPauses(text, currentPauses - targetPauses)
    } else if (currentPauses < targetPauses * 0.8) {
      // Add more natural pauses
      return addAdditionalPauses(text, targetPauses - currentPauses)
    }
    
    return text
  }

  // Remove least important pauses
  const removeLeastImportantPauses = (text: string, toRemove: number): string => {
    let result = text
    let removed = 0
    
    // Remove pauses in order of least importance
    const removalOrder = [
      /\[PAUSE\]\s*,/g, // Before commas
      /\b(and|or|but)\s*\[PAUSE\]/g, // After conjunctions
      /\[PAUSE\]\s*\[PAUSE\]/g // Consecutive pauses
    ]
    
    for (const pattern of removalOrder) {
      if (removed >= toRemove) break
      
      const matches = Array.from(result.matchAll(pattern))
      for (let i = 0; i < Math.min(matches.length, toRemove - removed); i++) {
        result = result.replace(pattern, (match) => match.replace('[PAUSE]', ''))
        removed++
      }
    }
    
    return result
  }

  // Add additional natural pauses
  const addAdditionalPauses = (text: string, toAdd: number): string => {
    let result = text
    let added = 0
    
    // Add pauses at natural points
    const additionPoints = [
      /([.!?])\s+([A-Z])/g, // Between sentences
      /\b(however|therefore|meanwhile),?\s+/g, // After transition words
      /\b(first|second|third|next|then),?\s+/g // After sequence words
    ]
    
    for (const pattern of additionPoints) {
      if (added >= toAdd) break
      
      const matches = Array.from(result.matchAll(pattern))
      for (let i = 0; i < Math.min(matches.length, toAdd - added); i++) {
        const match = matches[i]
        if (match.index !== undefined) {
          const beforePause = result.substring(Math.max(0, match.index - 10), match.index + match[0].length)
          if (!beforePause.includes('[PAUSE]')) {
            result = result.replace(match[0], match[0] + '[PAUSE] ')
            added++
          }
        }
      }
    }
    
    return result
  }

  // Auto-analyze when script changes
  useEffect(() => {
    if (script) {
      const analysis = analyzePauses(script)
      setCurrentAnalysis(analysis)
    }
  }, [script, analyzePauses])

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Natural Pause AI</h3>
        <p className="text-sm text-gray-600">
          Intelligently place pauses for natural speech flow, emphasis, and breathing patterns
        </p>
      </div>

      {/* Pause Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Pause Optimization Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Pause Frequency: {settings.pauseFrequency}/min
              </label>
              <Slider
                value={[settings.pauseFrequency]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, pauseFrequency: value }))}
                min={4}
                max={20}
                step={1}
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">Target number of pauses per minute</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Pause Intensity
              </label>
              <select
                value={settings.pauseIntensity}
                onChange={(e) => setSettings(prev => ({ ...prev, pauseIntensity: e.target.value as any }))}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="minimal">Minimal (Quick flow)</option>
                <option value="moderate">Moderate (Natural flow)</option>
                <option value="expressive">Expressive (Dramatic flow)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Duration and emphasis of pauses</p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Emphasis Pauses</label>
                <Switch
                  checked={settings.emphasizePauses}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emphasizePauses: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Breathing Pauses</label>
                <Switch
                  checked={settings.breathingPauses}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, breathingPauses: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Transition Pauses</label>
                <Switch
                  checked={settings.transitionPauses}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, transitionPauses: checked }))}
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Remove Unnatural Pauses</label>
                <Switch
                  checked={settings.removeUnnaturalPauses}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, removeUnnaturalPauses: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Content-Based Pauses</label>
                <Switch
                  checked={settings.contentBasedPauses}
                  onCheckedChange={(checked) => setSettings(prev => ({ ...prev, contentBasedPauses: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Analysis */}
      {currentAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Current Pause Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{currentAnalysis.totalPauses}</div>
                <div className="text-sm text-gray-600">Total Pauses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{currentAnalysis.naturalPauses}</div>
                <div className="text-sm text-gray-600">Natural</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{currentAnalysis.unnaturalPauses}</div>
                <div className="text-sm text-gray-600">Unnatural</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  currentAnalysis.pauseQualityScore >= 0.8 ? 'text-green-600' :
                  currentAnalysis.pauseQualityScore >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {Math.round(currentAnalysis.pauseQualityScore * 100)}%
                </div>
                <div className="text-sm text-gray-600">Quality Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round((currentAnalysis.totalPauses / duration) * 60)}
                </div>
                <div className="text-sm text-gray-600">Per Minute</div>
              </div>
            </div>

            {/* Pause Distribution */}
            <div className="grid gap-2 md:grid-cols-5 text-sm">
              <div className="text-center">
                <div className="font-medium text-blue-700">{currentAnalysis.pauseDistribution.after_sentences}</div>
                <div className="text-gray-600">After Sentences</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-green-700">{currentAnalysis.pauseDistribution.for_emphasis}</div>
                <div className="text-gray-600">For Emphasis</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-orange-700">{currentAnalysis.pauseDistribution.for_breathing}</div>
                <div className="text-gray-600">For Breathing</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-purple-700">{currentAnalysis.pauseDistribution.transitional}</div>
                <div className="text-gray-600">Transitional</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-teal-700">{currentAnalysis.pauseDistribution.after_clauses}</div>
                <div className="text-gray-600">After Clauses</div>
              </div>
            </div>

            {/* Improvements */}
            {currentAnalysis.improvements.length > 0 && (
              <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">Suggested Improvements:</h4>
                <ul className="space-y-1">
                  {currentAnalysis.improvements.map((improvement, index) => (
                    <li key={index} className="text-sm text-yellow-700 flex items-start">
                      <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {improvement}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Optimization Controls */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={optimizePauses}
          disabled={isOptimizing || !script.trim()}
          className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
        >
          {isOptimizing ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Optimizing Pauses...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
              </svg>
              Optimize Natural Pauses
            </>
          )}
        </Button>
        
        <Button
          variant="outline"
          onClick={() => setPreviewMode(!previewMode)}
          disabled={!currentAnalysis}
        >
          {previewMode ? 'Hide Preview' : 'Preview Rules'}
        </Button>
      </div>

      {/* Pause Rules Preview */}
      {previewMode && (
        <Card className="bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
          <CardHeader>
            <CardTitle className="text-base text-emerald-800">Natural Pause Rules</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 text-sm">
              {Object.entries(PAUSE_RULES).map(([ruleName, rule]) => (
                <div key={ruleName} className="p-3 border rounded-lg bg-white">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="capitalize">
                      {ruleName.replace('_', ' ')}
                    </Badge>
                    <Badge variant="outline" className={
                      rule.priority > 0.8 ? 'bg-green-100 text-green-800' :
                      rule.priority > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }>
                      Priority: {Math.round(rule.priority * 100)}%
                    </Badge>
                  </div>
                  <p className="text-gray-700 mb-1">{rule.description}</p>
                  <div className="text-xs text-gray-500">
                    Duration: {rule.duration} | Type: {rule.pauseType}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}