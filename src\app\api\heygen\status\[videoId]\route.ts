import { NextRequest, NextResponse } from 'next/server'
import { heygenClient } from '@/lib/heygen'
import { createSupabaseServerClient } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { videoId: string } }
) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { videoId } = params

    // Get video from database
    const { data: videoData, error: videoError } = await supabase
      .from('videos')
      .select('*')
      .eq('id', videoId)
      .eq('user_id', user.id)
      .single()

    if (videoError || !videoData) {
      return NextResponse.json(
        { success: false, error: 'Video not found' },
        { status: 404 }
      )
    }

    // If video doesn't have a HeyGen ID, return current status
    if (!videoData.heygen_video_id) {
      return NextResponse.json({
        success: true,
        data: {
          video_id: videoData.id,
          status: videoData.status,
          video_url: videoData.video_url,
          thumbnail_url: videoData.thumbnail_url,
          duration: videoData.duration
        }
      })
    }

    // Check status with HeyGen
    const heygenStatus = await heygenClient.getVideoStatus(videoData.heygen_video_id)

    // Update database with latest status
    const updateData: any = {
      status: heygenStatus.data.status
    }

    if (heygenStatus.data.video_url) {
      updateData.video_url = heygenStatus.data.video_url
    }

    if (heygenStatus.data.thumbnail_url) {
      updateData.thumbnail_url = heygenStatus.data.thumbnail_url
    }

    if (heygenStatus.data.duration) {
      updateData.duration = heygenStatus.data.duration
    }

    const { error: updateError } = await supabase
      .from('videos')
      .update(updateData)
      .eq('id', videoId)

    if (updateError) {
      console.error('Failed to update video status:', updateError)
    }

    return NextResponse.json({
      success: true,
      data: {
        video_id: videoData.id,
        heygen_video_id: videoData.heygen_video_id,
        status: heygenStatus.data.status,
        video_url: heygenStatus.data.video_url,
        thumbnail_url: heygenStatus.data.thumbnail_url,
        duration: heygenStatus.data.duration,
        error: heygenStatus.data.error
      }
    })

  } catch (error) {
    console.error('Error checking video status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to check video status' },
      { status: 500 }
    )
  }
}