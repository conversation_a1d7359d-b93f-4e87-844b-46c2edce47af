'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { DynamicLoader } from '@/components/ui/dynamic-loader'

const avatars = [
  { id: 1, name: '<PERSON>', category: 'Professional', accent: 'American', preview: '/avatars/sarah.jpg', premium: true },
  { id: 2, name: '<PERSON>', category: 'Business', accent: 'British', preview: '/avatars/marcus.jpg', premium: false },
  { id: 3, name: '<PERSON>', category: 'Creative', accent: 'Spanish', preview: '/avatars/elena.jpg', premium: true },
  { id: 4, name: '<PERSON>', category: 'Tech', accent: 'Korean', preview: '/avatars/david.jpg', premium: false },
  { id: 5, name: '<PERSON><PERSON>', category: 'Education', accent: 'Nigerian', preview: '/avatars/amara.jpg', premium: true },
  { id: 6, name: '<PERSON>', category: 'Healthcare', accent: 'Indian', preview: '/avatars/raj.jpg', premium: false },
]

const languages = [
  'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Dutch', 'Polish',
  'Russian', 'Ukrainian', 'Czech', 'Slovak', 'Hungarian', 'Romanian', 'Bulgarian', 'Croatian',
  'Serbian', 'Slovenian', 'Estonian', 'Latvian', 'Lithuanian', 'Finnish', 'Swedish', 'Norwegian',
  'Danish', 'Icelandic', 'Irish', 'Welsh', 'Catalan', 'Basque', 'Galician', 'Maltese',
  'Chinese (Mandarin)', 'Chinese (Cantonese)', 'Japanese', 'Korean', 'Thai', 'Vietnamese',
  'Indonesian', 'Malay', 'Filipino', 'Hindi', 'Bengali', 'Tamil', 'Telugu', 'Marathi',
  'Gujarati', 'Kannada', 'Malayalam', 'Punjabi', 'Urdu', 'Arabic', 'Hebrew', 'Turkish',
  'Persian', 'Kurdish', 'Armenian', 'Georgian', 'Azerbaijani', 'Kazakh', 'Uzbek', 'Kyrgyz'
]

const templates = [
  { id: 1, name: 'Product Demo', category: 'Business', thumbnail: '/templates/product-demo.jpg', duration: '2-3 min' },
  { id: 2, name: 'Training Video', category: 'Education', thumbnail: '/templates/training.jpg', duration: '5-10 min' },
  { id: 3, name: 'Marketing Pitch', category: 'Sales', thumbnail: '/templates/marketing.jpg', duration: '1-2 min' },
  { id: 4, name: 'News Update', category: 'Media', thumbnail: '/templates/news.jpg', duration: '3-5 min' },
  { id: 5, name: 'Tutorial', category: 'Education', thumbnail: '/templates/tutorial.jpg', duration: '10-15 min' },
  { id: 6, name: 'Company Update', category: 'Corporate', thumbnail: '/templates/company.jpg', duration: '2-4 min' },
]

export default function AIVideoPage() {
  const [selectedAvatar, setSelectedAvatar] = useState<number | null>(null)
  const [selectedLanguage, setSelectedLanguage] = useState('English')
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null)
  const [script, setScript] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)

  const handleGenerate = async () => {
    if (!selectedAvatar || !script.trim()) return
    
    setIsGenerating(true)
    setGenerationProgress(0)
    
    // Simulate video generation progress
    const interval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsGenerating(false)
          return 100
        }
        return prev + Math.random() * 15
      })
    }, 500)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">AI Video Generation</h1>
          <p className="text-premium-400">Create professional videos with AI avatars in 140+ languages</p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="tech" className="px-4 py-2">
            230+ AI Avatars
          </Badge>
          <Badge className="bg-gradient-emerald text-white border-0 px-4 py-2">
            140+ Languages
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Avatar Selection */}
        <Card className="bg-premium-card p-6 border border-premium-200/20 xl:col-span-2">
          <h2 className="text-xl font-semibold text-white mb-4">Choose Your AI Avatar</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {avatars.map((avatar) => (
              <div
                key={avatar.id}
                className={`relative cursor-pointer rounded-xl overflow-hidden transition-all duration-300 ${
                  selectedAvatar === avatar.id
                    ? 'ring-2 ring-ocean-primary shadow-lg scale-105'
                    : 'hover:scale-102 hover:shadow-md'
                }`}
                onClick={() => setSelectedAvatar(avatar.id)}
              >
                <div className="aspect-[3/4] bg-gradient-professional rounded-xl flex items-center justify-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-2xl font-bold text-white">
                      {avatar.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                </div>
                {avatar.premium && (
                  <Badge className="absolute top-2 right-2 bg-gradient-amber text-white border-0 text-xs">
                    Premium
                  </Badge>
                )}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
                  <h3 className="text-white font-medium text-sm">{avatar.name}</h3>
                  <p className="text-white/70 text-xs">{avatar.accent} • {avatar.category}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex items-center justify-between">
            <p className="text-premium-400 text-sm">
              {selectedAvatar ? `Selected: ${avatars.find(a => a.id === selectedAvatar)?.name}` : 'Select an avatar to continue'}
            </p>
            <Button variant="outline" size="sm">
              Browse All 230+ Avatars
            </Button>
          </div>
        </Card>

        {/* Video Settings */}
        <Card className="bg-premium-card p-6 border border-premium-200/20">
          <h2 className="text-xl font-semibold text-white mb-4">Video Settings</h2>
          
          <div className="space-y-4">
            {/* Language Selection */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Language</label>
              <select 
                value={selectedLanguage}
                onChange={(e) => setSelectedLanguage(e.target.value)}
                className="w-full bg-premium-800/50 border border-premium-600/30 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-ocean-primary focus:border-transparent"
              >
                {languages.map((lang) => (
                  <option key={lang} value={lang}>{lang}</option>
                ))}
              </select>
            </div>

            {/* Voice Settings */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Voice Style</label>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">Natural</Button>
                <Button variant="outline" size="sm">Professional</Button>
                <Button variant="outline" size="sm">Friendly</Button>
                <Button variant="outline" size="sm">Energetic</Button>
              </div>
            </div>

            {/* Video Quality */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Quality</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="radio" name="quality" value="720p" className="mr-2" />
                  <span className="text-premium-300">720p HD</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="quality" value="1080p" className="mr-2" defaultChecked />
                  <span className="text-premium-300">1080p Full HD</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="quality" value="4k" className="mr-2" />
                  <span className="text-premium-300">4K Ultra HD</span>
                  <Badge className="ml-2 bg-gradient-violet text-white border-0 text-xs">Pro</Badge>
                </label>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Templates */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Video Templates</h2>
          <Badge className="bg-gradient-tech text-white border-0">
            250+ Templates
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`relative cursor-pointer rounded-lg overflow-hidden transition-all duration-300 ${
                selectedTemplate === template.id
                  ? 'ring-2 ring-ocean-primary shadow-lg'
                  : 'hover:shadow-md'
              }`}
              onClick={() => setSelectedTemplate(template.id)}
            >
              <div className="aspect-video bg-gradient-professional rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l7-5-7-5z"/>
                    </svg>
                  </div>
                  <h3 className="text-white font-medium text-sm">{template.name}</h3>
                  <p className="text-white/70 text-xs">{template.category} • {template.duration}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <Button variant="outline" className="w-full">
          Browse All Templates
        </Button>
      </Card>

      {/* Script Input */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <h2 className="text-xl font-semibold text-white mb-4">Video Script</h2>
        <textarea
          value={script}
          onChange={(e) => setScript(e.target.value)}
          placeholder="Enter your video script here... Our AI will automatically generate natural speech and lip-sync for your chosen avatar."
          className="w-full h-32 bg-premium-800/50 border border-premium-600/30 rounded-lg px-4 py-3 text-white placeholder-premium-400 focus:ring-2 focus:ring-ocean-primary focus:border-transparent resize-none"
        />
        <div className="flex items-center justify-between mt-4">
          <p className="text-premium-400 text-sm">
            {script.length} characters • Estimated duration: {Math.ceil(script.length / 150)} minutes
          </p>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              AI Enhance
            </Button>
            <Button variant="outline" size="sm">
              Translate
            </Button>
          </div>
        </div>
      </Card>

      {/* Generation */}
      <Card className="bg-premium-card p-6 border border-premium-200/20">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Generate Video</h2>
          {isGenerating && (
            <Badge className="bg-gradient-emerald text-white border-0 animate-pulse">
              Generating...
            </Badge>
          )}
        </div>
        
        {isGenerating && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-premium-300 text-sm">Generation Progress</span>
              <span className="text-premium-300 text-sm">{Math.round(generationProgress)}%</span>
            </div>
            <Progress value={generationProgress} className="h-2" />
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <div className="text-premium-400 text-sm">
            {selectedAvatar && script.trim() 
              ? 'Ready to generate your AI video'
              : 'Please select an avatar and enter a script'
            }
          </div>
          <Button 
            variant="tech"
            onClick={handleGenerate}
            disabled={!selectedAvatar || !script.trim() || isGenerating}
            className="px-8"
          >
            {isGenerating ? 'Generating...' : 'Generate Video'}
          </Button>
        </div>
      </Card>
    </div>
  )
}
