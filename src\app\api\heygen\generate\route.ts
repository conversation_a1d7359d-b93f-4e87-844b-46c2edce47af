import { NextRequest, NextResponse } from 'next/server'
import { heygenClient } from '@/lib/heygen'
import { createSupabaseServerClient } from '@/lib/supabase'
import { z } from 'zod'

const generateVideoSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  script: z.string().min(1, 'Script is required'),
  avatar_id: z.string().min(1, 'Avatar ID is required'),
  voice_id: z.string().min(1, 'Voice ID is required'),
  aspect_ratio: z.enum(['16:9', '9:16', '1:1']).optional().default('16:9'),
  background: z.object({
    type: z.enum(['color', 'image']),
    value: z.string()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = generateVideoSchema.parse(body)

    // Check user's credits
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('credits_remaining, subscription_tier')
      .eq('id', user.id)
      .single()

    if (userError || !userData) {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user data' },
        { status: 500 }
      )
    }

    if (userData.credits_remaining < 1) {
      return NextResponse.json(
        { success: false, error: 'Insufficient credits' },
        { status: 402 }
      )
    }

    // Create video entry in database first
    const { data: videoData, error: videoError } = await supabase
      .from('videos')
      .insert({
        user_id: user.id,
        title: validatedData.title,
        script: validatedData.script,
        avatar_id: validatedData.avatar_id,
        voice_id: validatedData.voice_id,
        status: 'pending',
        metadata: {
          aspect_ratio: validatedData.aspect_ratio,
          background: validatedData.background
        }
      })
      .select()
      .single()

    if (videoError || !videoData) {
      return NextResponse.json(
        { success: false, error: 'Failed to create video record' },
        { status: 500 }
      )
    }

    try {
      // Generate video with HeyGen
      const heygenResponse = await heygenClient.createTalkingHeadVideo({
        script: validatedData.script,
        avatarId: validatedData.avatar_id,
        voiceId: validatedData.voice_id,
        aspectRatio: validatedData.aspect_ratio,
        background: validatedData.background,
        test: false
      })

      // Update video record with HeyGen video ID
      const { error: updateError } = await supabase
        .from('videos')
        .update({
          heygen_video_id: heygenResponse.data.video_id,
          status: 'processing'
        })
        .eq('id', videoData.id)

      if (updateError) {
        console.error('Failed to update video with HeyGen ID:', updateError)
      }

      // Deduct credit from user
      const { error: creditError } = await supabase
        .from('users')
        .update({
          credits_remaining: userData.credits_remaining - 1
        })
        .eq('id', user.id)

      if (creditError) {
        console.error('Failed to deduct credit:', creditError)
      }

      return NextResponse.json({
        success: true,
        data: {
          video_id: videoData.id,
          heygen_video_id: heygenResponse.data.video_id,
          status: 'processing'
        }
      })

    } catch (heygenError) {
      // If HeyGen fails, update video status to failed
      await supabase
        .from('videos')
        .update({ status: 'failed' })
        .eq('id', videoData.id)

      throw heygenError
    }

  } catch (error) {
    console.error('Error generating video:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to generate video' },
      { status: 500 }
    )
  }
}