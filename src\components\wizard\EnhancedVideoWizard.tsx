'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/context/auth-context'
import { ProjectWizardProvider, useProjectWizard } from '@/context/project-wizard-context'
import { TopicResearchStep } from './TopicResearchStep'
import { AudienceStyleStep } from './AudienceStyleStep'
import { VoiceBrandingStep } from './VoiceBrandingStep'
import { VisualContentStep } from './VisualContentStep'
import { TimelineEditor } from './TimelineEditor'
import { PublishingStep } from './PublishingStep'
import { ScriptEditor } from '../video/ScriptEditor'
import { AvatarSelector } from '../video/AvatarSelector'
import { VoiceSelector } from '../video/VoiceSelector'
import { VideoSettings } from '../video/VideoSettings'
import { GenerationProgress } from '../video/GenerationProgress'
import { LoadingSpinner } from '@/components/ui/loading'

const enhancedSteps = [
  { id: 1, title: 'Topic', description: 'Research your topic with AI assistance' },
  { id: 2, title: 'Audience', description: 'Define target audience and video style' },
  { id: 3, title: 'Script', description: 'Generate and enhance your script' },
  { id: 4, title: 'Avatar', description: 'Choose your AI presenter' },
  { id: 5, title: 'Voice', description: 'Select voice and branding' },
  { id: 6, title: 'Visual', description: 'Upload or generate visual content' },
  { id: 7, title: 'Timeline', description: 'Edit timeline and settings' },
  { id: 8, title: 'Publish', description: 'Configure publishing platforms' },
  { id: 9, title: 'Review', description: 'Review and generate' },
  { id: 10, title: 'Generate', description: 'Creating your video' },
]

interface EnhancementOptions {
  tone: 'professional' | 'casual' | 'enthusiastic' | 'educational' | 'sales'
  targetLength: 'short' | 'medium' | 'long'
  includeResearch: boolean
  useAudienceData: boolean
}

// Enhanced ScriptEditor component that integrates with the wizard context
function EnhancedScriptEditor() {
  const { projectData, dispatch } = useProjectWizard()
  const [isGenerating, setIsGenerating] = useState(false)
  const [isEnhancing, setIsEnhancing] = useState(false)
  const [showEnhancementOptions, setShowEnhancementOptions] = useState(false)
  const [enhancementOptions, setEnhancementOptions] = useState<EnhancementOptions>({
    tone: 'educational',
    targetLength: 'medium',
    includeResearch: true,
    useAudienceData: true
  })

  const handleUpdate = (updates: { title?: string; script?: string }) => {
    dispatch({ type: 'UPDATE_SCRIPT', payload: updates })
  }

  const generateResearchBasedScript = async () => {
    if (!projectData.topic || !projectData.researched_content) {
      alert('Please complete topic research first')
      return
    }

    setIsGenerating(true)
    try {
      // Generate script based on research data and audience preferences
      const prompt = `Create an engaging ${projectData.video_style} video script about "${projectData.topic}" for a ${projectData.target_audience} audience.

Research Context:
- Tutorial Steps: ${projectData.researched_content.tutorial_steps.join(', ')}
- Best Practices: ${projectData.researched_content.best_practices.join(', ')}
- Common Issues: ${projectData.researched_content.common_issues.join(', ')}
- Difficulty Level: ${projectData.researched_content.estimated_difficulty}

Target Platforms: ${projectData.platforms.join(', ')}
Video Style: ${projectData.video_style}
Audience Level: ${projectData.target_audience}

Requirements:
- Engaging introduction that hooks the viewer
- Clear structure following the research-based tutorial steps
- Address common issues and best practices
- Include strategic pauses with [PAUSE] markers
- Emphasize key points with [EMPHASIS] markers
- Keep appropriate length for ${projectData.platforms.join(' and ')} platform(s)
- End with clear call-to-action

Generate a complete, engaging script that teaches this topic effectively.`

      const response = await fetch('/api/groq/generate-script', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          topic: projectData.topic,
          audience: projectData.target_audience,
          style: projectData.video_style,
          platforms: projectData.platforms,
          research: projectData.researched_content
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          dispatch({ 
            type: 'UPDATE_SCRIPT', 
            payload: { 
              title: result.data.title || `${projectData.topic} - ${projectData.video_style}`,
              script: result.data.script 
            } 
          })
        } else {
          throw new Error(result.error || 'Failed to generate script')
        }
      } else {
        // Fallback with structured template based on research
        const fallbackScript = `Hi there! Welcome to this ${projectData.video_style} about ${projectData.topic}. [PAUSE]

Today, I'm going to show you everything you need to know about this topic, designed specifically for ${projectData.target_audience} learners. [EMPHASIS]This is going to be really valuable![/EMPHASIS] [PAUSE]

${projectData.researched_content?.tutorial_steps.slice(0, 3).map((step, i) => 
  `${i + 1}. ${step} [PAUSE]`
).join('\n\n')}

Now, let me share some [EMPHASIS]best practices[/EMPHASIS] that will save you time:

${projectData.researched_content?.best_practices.slice(0, 2).map(practice => 
  `• ${practice}`
).join('\n')}

[PAUSE]

Before we wrap up, let me quickly address the most common issues people face:

${projectData.researched_content?.common_issues.slice(0, 2).map(issue => 
  `⚠️ ${issue}`
).join('\n\n')}

[PAUSE]

That's a wrap! You now have everything you need to get started with ${projectData.topic}. If you found this helpful, make sure to like and subscribe for more content like this! [PAUSE]

What topic would you like me to cover next? Let me know in the comments below!`

        dispatch({ 
          type: 'UPDATE_SCRIPT', 
          payload: { 
            title: `Complete Guide to ${projectData.topic} for ${projectData.target_audience}s`,
            script: fallbackScript
          } 
        })
      }
    } catch (error) {
      console.error('Script generation failed:', error)
      alert('Failed to generate script. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const enhanceExistingScript = async () => {
    if (!projectData.script.trim()) {
      alert('Please enter a script to enhance')
      return
    }

    setIsEnhancing(true)
    try {
      const enhancementPrompt = `Enhance this video script based on the following context:

Original Script:
${projectData.script}

Enhancement Context:
- Topic: ${projectData.topic}
- Target Audience: ${projectData.target_audience}
- Video Style: ${projectData.video_style}
- Platforms: ${projectData.platforms.join(', ')}
- Desired Tone: ${enhancementOptions.tone}
- Target Length: ${enhancementOptions.targetLength}

${enhancementOptions.includeResearch && projectData.researched_content ? `
Research Data:
- Best Practices: ${projectData.researched_content.best_practices.join(', ')}
- Common Issues: ${projectData.researched_content.common_issues.join(', ')}
` : ''}

${enhancementOptions.useAudienceData ? `
Audience Optimization:
- Adjust complexity for ${projectData.target_audience} level
- Optimize for ${projectData.video_style} format
- Include platform-specific elements for ${projectData.platforms.join(' and ')}
` : ''}

Please enhance the script to:
1. Improve engagement and clarity
2. Add strategic [PAUSE] and [EMPHASIS] markers
3. Ensure appropriate length for target platforms
4. Include best practices from research (if available)
5. Address the target audience appropriately
6. Maintain the desired tone throughout

Return only the enhanced script.`

      const response = await fetch('/api/groq/enhance-script', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          script: projectData.script,
          prompt: enhancementPrompt,
          options: {
            ...enhancementOptions,
            context: {
              topic: projectData.topic,
              audience: projectData.target_audience,
              style: projectData.video_style,
              platforms: projectData.platforms,
              research: enhancementOptions.includeResearch ? projectData.researched_content : null
            }
          }
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          dispatch({ 
            type: 'UPDATE_SCRIPT', 
            payload: { script: result.data.enhanced_script || result.data.script } 
          })
          setShowEnhancementOptions(false)
        } else {
          throw new Error(result.error || 'Failed to enhance script')
        }
      } else {
        throw new Error('Enhancement service unavailable')
      }
    } catch (error) {
      console.error('Enhancement failed:', error)
      alert('Failed to enhance script. Please try again.')
    } finally {
      setIsEnhancing(false)
    }
  }

  const scriptStats = {
    wordCount: projectData.script.split(' ').filter(word => word.length > 0).length,
    charCount: projectData.script.length,
    estimatedDuration: Math.ceil(projectData.script.split(' ').filter(word => word.length > 0).length / 2.5),
    pauseCount: (projectData.script.match(/\[PAUSE\]/g) || []).length,
    emphasisCount: (projectData.script.match(/\[EMPHASIS\]/g) || []).length
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">AI-Enhanced Script Generation</h3>
        <p className="text-sm text-gray-600 mb-6">
          Generate engaging scripts based on your research, or enhance existing content with AI assistance
        </p>
      </div>

      {/* Research-Based Generation */}
      {projectData.researched_content && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              AI Script Generation Ready
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700 mb-4">
              Your topic research is complete! Generate a structured script based on your research data, 
              audience preferences, and platform requirements.
            </p>
            <div className="flex items-center space-x-3">
              <Button
                onClick={generateResearchBasedScript}
                disabled={isGenerating}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {isGenerating ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Generating Script...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Generate Research-Based Script
                  </>
                )}
              </Button>
              <Badge className="bg-green-100 text-green-800">
                {projectData.researched_content.tutorial_steps.length} steps identified
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Original Script Editor */}
      <ScriptEditor
        title={projectData.title}
        script={projectData.script}
        onUpdate={handleUpdate}
        errors={{}}
      />

      {/* Enhanced AI Options */}
      {projectData.script.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Advanced Enhancement Options</CardTitle>
            <p className="text-sm text-gray-600">
              Enhance your script using research data and audience insights
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Enhancement Style</label>
                <select
                  value={enhancementOptions.tone}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, tone: e.target.value as any }))}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="educational">Educational & Clear</option>
                  <option value="professional">Professional</option>
                  <option value="casual">Casual & Friendly</option>
                  <option value="enthusiastic">Enthusiastic</option>
                  <option value="sales">Sales-Oriented</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Target Length</label>
                <select
                  value={enhancementOptions.targetLength}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, targetLength: e.target.value as any }))}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="short">Short (30-60s)</option>
                  <option value="medium">Medium (60-180s)</option>
                  <option value="long">Long (180s+)</option>
                </select>
              </div>
            </div>
            
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={enhancementOptions.includeResearch}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, includeResearch: e.target.checked }))}
                  disabled={!projectData.researched_content}
                  className="rounded"
                />
                <span className="text-sm text-gray-700">
                  Include research insights and best practices
                  {!projectData.researched_content && <span className="text-gray-400"> (requires research data)</span>}
                </span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={enhancementOptions.useAudienceData}
                  onChange={(e) => setEnhancementOptions(prev => ({ ...prev, useAudienceData: e.target.checked }))}
                  className="rounded"
                />
                <span className="text-sm text-gray-700">
                  Optimize for {projectData.target_audience} audience and {projectData.video_style} style
                </span>
              </label>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={enhanceExistingScript}
                disabled={isEnhancing}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                {isEnhancing ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Enhancing...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Enhance with Context
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Script Stats */}
      {projectData.script.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Enhanced Script Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{scriptStats.wordCount}</div>
                <div className="text-sm text-gray-600">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{scriptStats.estimatedDuration}s</div>
                <div className="text-sm text-gray-600">Est. Duration</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{scriptStats.pauseCount}</div>
                <div className="text-sm text-gray-600">Strategic Pauses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{scriptStats.emphasisCount}</div>
                <div className="text-sm text-gray-600">Emphasis Points</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  scriptStats.estimatedDuration <= 60 ? 'text-green-600' :
                  scriptStats.estimatedDuration <= 180 ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {projectData.platforms.includes('tiktok') && scriptStats.estimatedDuration <= 60 ? '✓' :
                   projectData.platforms.includes('youtube') && scriptStats.estimatedDuration <= 600 ? '✓' :
                   '⚠️'}
                </div>
                <div className="text-sm text-gray-600">Platform Fit</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function EnhancedVideoWizardContent() {
  const router = useRouter()
  const { user } = useAuth()
  const { projectData, validateStep, dispatch } = useProjectWizard()
  const [currentStep, setCurrentStep] = useState(1)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationId, setGenerationId] = useState<string | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < enhancedSteps.length) {
        setCurrentStep(currentStep + 1)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleGenerate = async () => {
    if (!validateStep(9)) return

    // Check credits
    if (!user || user.credits_remaining < 1) {
      alert('Insufficient credits. Please upgrade your plan.')
      router.push('/billing')
      return
    }

    setIsGenerating(true)
    setCurrentStep(10)

    try {
      const response = await fetch('/api/heygen/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: projectData.title,
          script: projectData.script,
          avatar_id: projectData.avatar_id,
          voice_id: projectData.voice_id,
          aspect_ratio: projectData.aspect_ratio,
          background: projectData.background,
          // Enhanced data
          topic: projectData.topic,
          target_audience: projectData.target_audience,
          video_style: projectData.video_style,
          platforms: projectData.platforms,
          custom_branding: projectData.custom_branding,
          timeline_settings: projectData.timeline_settings
        }),
      })

      const result = await response.json()

      if (result.success) {
        setGenerationId(result.data.video_id)
      } else {
        throw new Error(result.error || 'Failed to generate video')
      }
    } catch (error) {
      console.error('Generation failed:', error)
      alert('Failed to generate video. Please try again.')
      setCurrentStep(9) // Go back to review step
    } finally {
      setIsGenerating(false)
    }
  }

  const progress = ((currentStep - 1) / (enhancedSteps.length - 1)) * 100

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <TopicResearchStep />
      case 2:
        return <AudienceStyleStep />
      case 3:
        return (
          <EnhancedScriptEditor />
        )
      case 4:
        return (
          <AvatarSelector
            selectedAvatarId={projectData.avatar_id}
            onSelect={(avatar_id) => {
              dispatch({ type: 'UPDATE_AVATAR', payload: avatar_id })
            }}
            error={errors.avatar}
          />
        )
      case 5:
        return <VoiceBrandingStep />
      case 6:
        return <VisualContentStep />
      case 7:
        return <TimelineEditor />
      case 8:
        return <PublishingStep />
      case 9:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Review Your Project</h3>
            <div className="space-y-6">
              {/* Project Overview */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Topic & Audience</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Topic</label>
                      <p className="text-sm text-gray-900">{projectData.topic}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Target Audience</label>
                      <p className="text-sm text-gray-900 capitalize">{projectData.target_audience}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Video Style</label>
                      <p className="text-sm text-gray-900 capitalize">{projectData.video_style}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Selected Platforms</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {projectData.platforms.map((platform) => (
                          <Badge key={platform} variant="outline" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Publishing Platforms</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {(projectData.publishing_config?.platforms || []).map((config) => (
                          <Badge key={config.platform} variant="outline" className="text-xs bg-green-50">
                            {config.platform} ({config.hashtags.length} tags)
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Content Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Title</label>
                      <p className="text-sm text-gray-900">{projectData.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Script Length</label>
                      <p className="text-sm text-gray-900">
                        {projectData.script.split(' ').filter(w => w.length > 0).length} words
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Estimated Duration</label>
                      <p className="text-sm text-gray-900">
                        ~{Math.ceil(projectData.script.split(' ').filter(w => w.length > 0).length / 2.5)}s
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Aspect Ratio</label>
                      <p className="text-sm text-gray-900">{projectData.aspect_ratio}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Script Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Script Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="p-3 border rounded-md bg-gray-50 max-h-32 overflow-y-auto">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">
                      {projectData.script.length > 200 
                        ? `${projectData.script.substring(0, 200)}...` 
                        : projectData.script
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Generation Notice */}
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <p className="font-medium text-blue-900">Ready to generate your video?</p>
                  <p className="text-sm text-blue-700">This will use 1 credit from your account</p>
                </div>
                <Badge variant="info">
                  {user?.credits_remaining || 0} credits remaining
                </Badge>
              </div>
            </div>
          </div>
        )
      case 10:
        return (
          <GenerationProgress
            videoId={generationId}
            videoTitle={projectData.title}
            onComplete={(videoUrl) => {
              router.push(`/videos/${generationId}`)
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Step {currentStep} of {enhancedSteps.length}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {enhancedSteps[currentStep - 1]?.description}
              </p>
            </div>
            <Badge variant="outline">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="mt-4" />
        </CardHeader>
      </Card>

      {/* Step Navigation */}
      <div className="flex justify-center overflow-x-auto pb-2">
        <div className="flex items-center space-x-2 min-w-max">
          {enhancedSteps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep > step.id
                    ? 'bg-green-500 text-white'
                    : currentStep === step.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {currentStep > step.id ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  step.id
                )}
              </div>
              <span className="ml-2 text-sm font-medium text-gray-700 hidden md:block">
                {step.title}
              </span>
              {index < enhancedSteps.length - 1 && (
                <div className="w-6 h-px bg-gray-300 ml-4" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      {currentStep < 10 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </Button>

          {currentStep === 9 ? (
            <Button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8 4h8m-8 4h8M7 4h10a2 2 0 012 2v12a2 2 0 01-2 2H7a2 2 0 01-2-2V6a2 2 0 012-2z" />
                  </svg>
                  Generate Video
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleNext} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              Next
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

export function EnhancedVideoWizard() {
  return (
    <ProjectWizardProvider>
      <EnhancedVideoWizardContent />
    </ProjectWizardProvider>
  )
}